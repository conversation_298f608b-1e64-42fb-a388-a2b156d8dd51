declare interface Window {
  kobe: any;
  $ENV: 'daily' | 'alta' | 'altb' | 'altc' | 'ppe' | 'production';
}

declare interface Document {
  startViewTransition?: (callback: () => any) => void;
}

declare module '*.less' {
  const content: { [key: string]: any };
  export default content;
}

declare module '*.svg'
declare module '*.png'
declare module '*.jpg'
declare module '*.jpeg'
declare module '*.gif'
declare module '*.bmp'
declare module '*.tiff'
