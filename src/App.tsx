/* eslint-disable import/first */
import './common/app-version';
import React from 'react';
import zhCN from 'antd/es/locale/zh_CN';
import { But<PERSON>, ConfigProvider, Row } from 'antd';
import '@/common/styles/utils.less';
import '@/common/styles/common.less';
import '@/common/styles/iconfont.less';
import { updateGlobalConfig } from '@/common/components/CommonBlockWrap';
import './App.less';

updateGlobalConfig({
  errTitle: (
    <>
      这里出现了问题，请您先保存页面数据，后续联系
      <a href="dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=i2v_w1f1e2yym">@骆舟（224682）</a>，提供操作时间、页面网址进行排查。
    </>
  ),
  errMessage: '如无任何影响可刷新后重试！！！',
  errCompnent: (
    <Row justify="center">
      <Button onClick={() => window.location.reload()}>刷新页面</Button>
    </Row>
  ),
});

export function render() {
  return (props) => (
    <ConfigProvider locale={zhCN}>{props.children}</ConfigProvider>
  );
}
