export const mapUrl = {
  daily: 'https://xy.daily.elenet.me/',
  altc: 'https://xy.altc.elenet.me/',
  ppe: 'https://ppe-xy.ele.me/',
  production: 'https://xy.ele.me/',
};

export const apiUrl = {
  daily: 'https://xy-api.daily.elenet.me/',
  altc: 'https://xy.altc.elenet.me/',
  ppe: 'https://ppe-xy-api.ele.me/',
  production: 'https://xy-api.ele.me/',
};

export type IENV = 'production' | 'ppe' | 'daily' | 'altc';

export enum ENV_ENUM {
  PRODUCTION = 'production',
  PPE = 'ppe',
  DAILY = 'daily',
  ALTC = 'altc',
}
