import { createEnumObject } from 'ts-enum-object';

export enum AssessResultNameEnum {
  KPI = 'kpiItem',
  COMM = 'commItem',
}

export const ruleTypeEnumMap = [
  {
    key: 'COMPLEX_INDICATOR',
    value: 'COMPLEX_INDICATOR',
    text: '根据规则生产',
    children: [
      {
        key: 'EXPRESSION',
        value: 'EXPRESSION',
        text: '公式',
      },
    ],
  },
  {
    key: 'ODPS',
    value: 'ODPS',
    text: '基于ODPS导入',
    children: [],
  },
  {
    key: 'HOLOGRES',
    value: 'HOLOGRES',
    text: 'HOLO',
    children: [],
  },
  {
    key: 'GROUP_INDICATOR',
    value: 'GROUP_INDICATOR',
    text: '排名',
    children: [],
  },
  {
    key: 'CAL_ITEM_INDICATOR',
    value: 'CAL_ITEM_INDICATOR',
    text: '计算结果',
    children: [],
  },
  {
    key: 'LAKE_INDICATOR_CODE',
    value: 'LAKE_INDICATOR_CODE',
    text: '数据湖',
    children: [],
  },
];

/** 字段类型 */
export const FieldTypeEnum = createEnumObject([
  {
    name: 'PERCENT',
    value: 'PERCENT',
    label: '百分比',
  },
  {
    name: 'NUMBER',
    value: 'NUMBER',
    label: '数值',
  },
] as const);

/** 业务域类型 */
export const BusinessGroupEnum = [
  // 后续放开
  // {
  //   value: 'C33_AGENT',
  //   label: '到店-C33服务商',
  // },
  {
    value: 'C33_DIRECT',
    label: '高德商户通-直营',
  },
  {
    value: 'C33_AGENT',
    label: '高德商户通-服务商',
  },
  {
    value: 'C31_AGENT',
    label: '高德旺铺-服务商',
  },
];

export const BusinessGroupEnumMap = {
  C33_DIRECT: '高德商户通-直营',
  C33_AGENT: '高德商户通-服务商',
  C31_AGENT: '高德旺铺-服务商',
};

/** 业务线类型 */
export const BusinessLineEnum = {
  // 后续放开，值待定
  // C33_AGENT: [
  //   {
  //     value: '',
  //     label: 'C33服务商',
  //   },
  // ],
  C33_DIRECT: [
    {
      value: 'C33_DEFAULT',
      label: '商户通-直营 ',
    },
  ],
  C33_AGENT: [
    {
      value: 'DEFAULT',
      label: '商户通-服务商',
    },
  ],
  C31_AGENT: [
    {
      value: 'C31_DEFAULT',
      label: '旺铺-服务商',
    },
  ],
};

/** 指标周期类型 */
export const TimeTypeEnum = [
  {
    value: 'MONTH',
    label: '月度',
  },
  {
    value: 'QUARTER',
    label: '季度',
  },
];

export const AssessResultMap = {
  KPI: [
    {
      text: '公式',
      value: 'EXPRESSION',
      key: 'EXPRESSION',
    },
    {
      text: '矩阵',
      value: 'ARRAY',
      key: 'ARRAY',
    },
    {
      text: '规则组',
      value: 'RULES',
      key: 'RULES',
    },
  ],
  NUMERIC: [
    {
      text: '公式',
      value: 'EXPRESSION',
      key: 'EXPRESSION',
    },
    {
      text: '矩阵',
      value: 'ARRAY',
      key: 'ARRAY',
    },
    {
      text: '规则组',
      value: 'RULES',
      key: 'RULES',
    },
  ],
  LEVEL: [
    {
      text: '矩阵',
      value: 'ARRAY',
      key: 'ARRAY',
    },
  ],
  RANK: [
    {
      text: '矩阵',
      value: 'ARRAY',
      key: 'ARRAY',
    },
  ],
};

export enum ActionTypeEnum {
  SEE = 'see',
  COPY = 'copy',
  EDIT = 'edit',
  ADD = 'add',
}

export enum planStateEnum {
  INIT = 'INIT',
  PASS = 'PASS',
  INVALID = 'INVALID',
  DELETED = 'DELETED',
}

export enum planStateListEnum {
  INIT = '未生效',
  PASS = '生效中',
}

export enum periodTypeEnum {
  MONTH = 'MONTH',
  MULTI_MONTH = 'MULTI_MONTH',
  CUSTOMIZE = 'CUSTOMIZE',
}

export enum periodTypeListEnum {
  MONTH = '单月',
  MULTI_MONTH = '多月',
  CUSTOMIZE = '自定义',
}

export const containButtonStateMap = {
  EDIT: ['INIT'] /** 编辑 */,
  CHECK: ['INIT', 'PASS'] /** 查看 */,
  COPY: ['INIT', 'PASS'] /** 复制 */,
  RELEASE: ['INIT'] /** 发布 */,
  STOP: ['PASS'] /** 停用 */,
  DELETE: ['INIT'] /** 删除 */,
};

export enum planStateActionEnum {
  PASS = '发布',
  INVALID = '停用',
  DELETED = '删除',
}

export const planStateListMap = [
  {
    value: 'INIT',
    label: '未生效' /** 发布 */,
  },
  {
    value: 'PASS',
    label: '生效中' /** 启用 */,
  },
]; /** DELETED 删除 */

export interface OptionsEnmuras {
  configType?: string;
  label: string;
  type: string;
  value: string;
}
export interface CurValues {
  varMap: object;
  textExpression: string;
  expression: string;
  type: string;
  funcName: string;
  varIds: string[];
}

// 埋点数据类型
export interface tracertInfo {
  // 产品域
  domain?: string;
  // 业务域
  businessLine?: string;
  // 方案ID
  planId?: string;
  // 其它所有信息
  othersInfo?: {
    // 组件名称
    componentName?: string;
    // 方案名称
    planName?: string;
    // 方案状态
    planStatus?: string;
    // 考核周期:
    planCycleStart?: string;
    planCycleEnd?: string;
  };
  // 操作类型（新建/复制新建/编辑）
  operateType?: string;
  // 操作间隔时间
  pageStartTime?: number;
}
// 产品域业务域方案ID方案名称考核周期操作类型（新建/复制新建/编辑）方案状态

interface EXP_WIN_DETAIL_EXTRA_TYPE {
  indicatorId: string;
  indicatorName: string;
}

interface ExpWinIndicatorExtraType {
  name: string;
  indicatorId: string;
}

interface PPEandProd_TYPE {
  businessDomain: string;
  indicatorDomain: string;
  bizType: any;
  businessGroup: string;
  businessLineCode: string;
  levelCode: string;
  levelCodes: string[];
  levelNumber: number;
  periodType: string;
  startTime: string;
  endTime: string;
  autoCircle: boolean;
  objectCircleFlag: string;
  circleOrgList: any;
  modules: any;
  ruleGroup: {
    showType: string;
    expression: {
      expression: {
        expression: string;
        textExpression: string;
        type: string;
        varMap: Record<string, string>;
      };
    };
    arrayExpressionObj: any;
    indicatorSortExtension: any;
    indicatorRules: any;
  };
  resultFormat: {
    expressionType: number;
    formatType: string;
    precision: number;
    thousandthShow: boolean;
    unitName: string;
  };
  caption: Record<string, any>;
  createTime: any;
  createdBy: any;
  effectRangeTime: any;
  effectiveStatus: any;
  modifyPower: any;
  parentId: any;
  planStatus: string;
  relations: any;
  updateBy: any;
  updateTime: any;
  strategySource: {
    strategySourceType: string;
  };
  appraisalDetailsList: any[];
  projectPersonalizeAttributes: {
    targetIndicatorCode: string;
    reachMerchantNumIndicatorCode: string;
    targetMerchantNumIndicatorCode: string;
    targetCircleMerchantNumIndicatorCode: string;
    standardExtension: any;
  };
}

export interface EXP_WIN_DEFAULT_CONFIG_INTERFACE {
  EXP_WIN_BIZ_TYPE: string;
  EXP_WIN_INDICATOR_DATASETS: string[];
  EXP_WIN_INDICATOR_EXTRA: ExpWinIndicatorExtraType[];
  EXP_WIN_DETAIL_DATASETS: string[][];
  EXP_WIN_DETAIL_EXTRA: EXP_WIN_DETAIL_EXTRA_TYPE[];
  PPEandProd: PPEandProd_TYPE;
  EXP_WIN_DEFAULT_CONFIG: PPEandProd_TYPE;
  DETAIL_LIST: Array<{
    config: any;
    datasets: string[];
    extra: any[];
  }>;
}

export const ModuleTypeEnum = {
  NUMERIC: '数值',
  LEVEL: '目标档位',
  RANK: '牌级',
};