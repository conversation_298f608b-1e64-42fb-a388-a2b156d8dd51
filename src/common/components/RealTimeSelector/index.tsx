import React, { useEffect, useState } from 'react';
import { Select, Tag, Tooltip } from 'antd';
import { groupBy } from 'lodash';

import './index.less';

const RealTimeSelector: React.FC<any> = (props) => {
  const [options, setOptions] = useState({});
  useEffect(
    () => {
      if (props?.optionsdata) {
        const tempArr = groupBy(props.optionsdata, 'dataSetName');
        setOptions(tempArr);
      }
    },
    [props?.optionsdata],
  );
  return (
    <Select {...props} className="real-time-warp">
      {
        Object.keys(options).map(key => (
          <>
            <Select.OptGroup label={key === 'null' ? '未关联数据集' : key} onChange={props?.onChange && props.onChange}>
              {options[key]?.map(item => (
                <Select.Option {...item} key={item.value} value={item.value} className="real-time-warp__options">
                  {item?.timeType === '2' ? <Tag color="processing">准实时</Tag> : null}
                  <Tooltip placement="bottom" title={item?.label}>
                    {item?.label || ''}
                  </Tooltip>
                </Select.Option>
              ))}
            </Select.OptGroup>
          </>
        ))
      }
    </Select>
  );
};

export default RealTimeSelector;
