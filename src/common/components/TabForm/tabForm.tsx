import React, { useEffect, useState } from 'react';
import { FormListFieldData } from 'antd/lib/form/FormList';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext, PointerSensor, useSensor } from '@dnd-kit/core';
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
} from '@dnd-kit/sortable';
import { Button, Tabs, Popconfirm } from 'antd';
import { DraggableTabNode } from '@/common/components/TabForm/draggableTabNode';
import { CloseOutlined, PlusSquareOutlined } from '@ant-design/icons';
import Tooltip from 'antd/es/tooltip';

interface Iprops {
  add: () => void;
  remove: (number) => void;
  move: (a: number, b: number) => void;
  formListData: FormListFieldData[];
  tabTitle: string;
  TabChild: React.FC<{ name: number; index: number; [x: string]: any }>;
  showAddIcon?: boolean;
  [x: string]: any;
  tabChange?: (key) => void;
}

interface tabItems {
  label: string;
  key: string;
  children: any;
}

const TabForm: React.FC<Iprops> = (props: Iprops) => {
  const { add, formListData = [], remove, move, TabChild, showAddIcon = true, tabChange } = props;
  const [items, setItems] = useState<tabItems[]>([]);
  const [defaultActiveKey, setActiveKey] = useState('0');

  useEffect(() => {
    const tempTabList = formListData.map((item) => ({
      key: item.name.toString(),
      label: `${props.tabTitle}${item.name + 1}`,
      closeIcon: (
        <Popconfirm
          title="确定删除当前选项吗"
          onConfirm={() => {
            if (item.name !== +defaultActiveKey) {
              if (item.name < +defaultActiveKey) {
                setActiveKey((+defaultActiveKey - 1).toString());
              }
            } else if (item.name > 0) {
              setActiveKey((item.name - 1).toString());
            }
            remove(item.name);
          }}
        >
          <CloseOutlined />
        </Popconfirm>
      ),
      children: (
        <TabChild
          name={item.name}
          index={item.key}
          {...props}
        />
      ),
    }));
    setItems(tempTabList);
  }, [formListData, props]);

  const [className, setClassName] = useState('');

  const sensor = useSensor(PointerSensor, { activationConstraint: { distance: 10 } });

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setItems((prev) => {
        move(Number(active.id), Number(over?.id));
        setActiveKey(over?.id.toString());
        const activeIndex = prev.findIndex((i) => i.key === active.id);
        const overIndex = prev.findIndex((i) => i.key === over?.id);
        return arrayMove(prev, activeIndex, overIndex);
      });
    }
  };

  return (
    <Tabs
      className={className}
      type="editable-card"
      hideAdd
      items={items}
      activeKey={defaultActiveKey}
      onChange={(key) => {
        tabChange && tabChange(key);
        setActiveKey(key);
      }}
      // onEdit={}
      tabBarExtraContent={{
        right: (
          <Tooltip title={showAddIcon ? '' : '新增考核对象不能大于层级数量'}>
            <Button
              onClick={() => {
                add();
                if (formListData?.length !== 0) {
                  setActiveKey(String(formListData.length));
                }
              }}
              icon={<PlusSquareOutlined />}
              disabled={!showAddIcon}
              type="link"
              size="large"
            />
          </Tooltip>
        ),
      }}
      renderTabBar={(tabBarProps, DefaultTabBar) => (
        <DndContext sensors={[sensor]} onDragEnd={onDragEnd}>
          <SortableContext items={items.map((i) => i.key)} strategy={horizontalListSortingStrategy}>
            <DefaultTabBar {...tabBarProps}>
              {(node) => (
                <DraggableTabNode
                  {...node.props}
                  key={node.key}
                  onActiveBarTransform={setClassName}
                >
                  {node}
                </DraggableTabNode>
              )}
            </DefaultTabBar>
          </SortableContext>
        </DndContext>
      )}
    />
  );
};

export default TabForm;
