import React, { useEffect, useState } from 'react';
import { Mo<PERSON>, Button, Transfer } from 'antd';
import { useDebounceFn } from 'ahooks';
import IndicatorGatewayService from '@/_docplus/target/service/gateway/IndicatorGatewayService';
import { IndicatorDTO } from '@/_docplus/target/types/gateway';

interface IProps {
  value?: IndicatorDTO[];
  onChange?: Function;
  onClose?: Function;
  businessGroup: string;
}

const QuoSelectModal = (props: IProps) => {
  const [quotaList, setQuotaList] = useState<Array<IndicatorDTO>>([]);
  const [targetKeys, setTargetKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [submiting, setSubmiting] = useState(false);

  useEffect(() => {
    searchQuota({}).then((res) => {
      setQuotaList(res);
    });
  }, []);

  useEffect(() => {
    if (Array.isArray(props.value)) {
      const keys = props.value.map((item) => item.indicatorId);
      setTargetKeys(keys);
    }
  }, props.value);

  /**
   * 搜索指标列表
   * @param keyWord
   */
  const searchQuota = async (param) => {
    const { data } = await IndicatorGatewayService.queryIndicatorsByCondition({
      pageSize: 400,
      pageNum: 1,
      businessGroup: props.businessGroup, // 业务域写死“C33服务商”
      indicatorSourceFrom: 'TARGET_SYSTEM_IMPORT',
      ...param,
    });

    return data?.indicatorDTOList || [];
  };

  const onChange = (nextTargetKeys: string[], direction, moveKeys: string[]) => {
    setTargetKeys(nextTargetKeys);
  };

  const onSelectChange = (sourceSelectedKeys: string[], targetSelectedKeys: string[]) => {
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
  };

  const handleOk = () => {
    setSubmiting(true);
    searchQuota({
      indicatorIds: targetKeys,
    })
      .then((res) => {
        setSubmiting(false);
        props?.onChange(res);
        props?.onClose();
      })
      .catch((e) => {
        setSubmiting(false);
      });
  };

  const handleCancel = () => {
    props?.onClose();
  };

  return (
    <>
      <Modal
        title="指标选择"
        open
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={submiting}
        width={800}
        okButtonProps={{ disabled: targetKeys.length === 0 }}
      >
        <Transfer
          showSearch
          rowKey={(record) => record.indicatorId}
          dataSource={quotaList}
          titles={['未选择', '已选择']}
          targetKeys={targetKeys}
          selectedKeys={selectedKeys}
          onChange={onChange}
          onSelectChange={onSelectChange}
          render={(item) => item.name}
          listStyle={{ width: 356, height: 400 }}
          filterOption={(inputValue, option) => {
            return option.name.indexOf(inputValue) > -1;
          }}
        />
      </Modal>
    </>
  );
};

export default QuoSelectModal;
