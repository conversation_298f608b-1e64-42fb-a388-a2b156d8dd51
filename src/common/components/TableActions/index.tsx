import React from 'react';
import './style.less';

interface TableOptionsProps {
  buttons: React.ReactElement[];
}

function arrayJoinItem(array: any[], insertItem: any) {
  const result = [];
  // eslint-disable-next-line no-restricted-syntax
  for (let index = 0; index < array.length; index++) {
    if (index) result.push(insertItem({ key: index }));
    result.push(array[index]);
  }
  return result;
}

export const TableActions: React.FC<TableOptionsProps> = (props) => (
  <div className="table-actions">
    { arrayJoinItem(props.buttons, (props) => <span key={props.key} className="table-actions__spliter" />) }
  </div>
);
