import { Formula } from '@/common/components/formula';
import { FormulaProps } from '@/common/components/formula/type';
import { DEFAULT_INDICATOR_LIST_PAGE_SIZE } from '@/common/constants';
import PlanManageFacade from '@/_docplus/target/service/gateway/KpiPolicyQueryGatewayService';
import React from 'react';
import { parse } from 'query-string';

export const FormulaWithSearch: React.FC<FormulaProps> = (props) => {
  const urlParam = parse(window.location.search);
  const businessGroup =
    props.businessGroup || (urlParam.role === 'agent' ? 'C33_AGENT' : 'C33_DIRECT');
  const searchRemoteFunc = (searchText) =>
    PlanManageFacade.queryIndicatorList({
      businessGroup,
      keyword: searchText,
      pageSize: DEFAULT_INDICATOR_LIST_PAGE_SIZE,
      pageStart: 0,
    })
      .then((res) => {
        const options = res?.data || [];
        return [
          ...options.map((item: any) => ({
            label: item.indicatorName,
            value: item.indicatorId,
            type: 'Indicator',
            indicatorType: item.indicatorType,
            classificationLabel: item?.label,
          })),
        ];
      })
      .catch(() => Promise.resolve([]));

  return <Formula {...props} searchRemoteFunc={searchRemoteFunc} />;
};
