.matrix {
  border-radius: 2px;

  .matrix__top {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
  }

  .matrix__add-btn--x {
    margin: 10px;
    width: 100px;
    height: 30px;
    text-align: center;
    vertical-align: middle;
  }

  .matrix__add-btn--y {
    margin-right: 10px;
    width: 30px;
    flex-shrink: 0;
    height: 100px;
    line-height: 5px;
    text-align: center;
    outline-width: 30px;
    padding-left: 0;
    padding-right: 0;

    span {
      writing-mode: vertical-rl;
    }
  }

  .matrix__content {
    .matrix__content__config-list {
      display: flex;
      background-color: white;
      border: 1px solid #ebebeb;
      overflow-x: auto;
      overflow-y: hidden;
      margin: 10px;
      min-height: 250px;
      pointer-events: all;
      opacity: 0.8;
      cursor: pointer;
      box-sizing: content-box;

      .config-list__col {
        flex: 0 0 auto;
        display: flex;
        flex-direction: column;
        align-items: stretch;

        .col__header {
          border: 1px solid #ebebeb;
          height: 55px;
          overflow: hidden;
          border-left: none;
          border-top: none;

          .header--default {
            width: 0;
            height: 0;
            border-top: 55px #f7f7f7 solid;
            border-left: 460px #efefef solid;
            background-color: white;
            position: relative;

            .header__item--x,
            .header__item--y {
              width: 100px;
              position: absolute;
              text-align: center;
            }

            .header__item--x {
              left: -455px;
              bottom: 10px;
            }

            .header__item--y {
              right: 0;
              top: -45px;
            }
          }

          .header--single {
            line-height: 55px;
            text-align: center;
            background-color: #efefef;
          }
        }

        .ant-form-item {
          margin-bottom: 0 !important;
        }

        .col__item {
          height: 55px;
          display: flex;
          padding: 0 10px;
          justify-content: center;
          align-items: center;
          background-color: #f7f7f7;
          border: 1px solid #ebebeb;
          border-left: none;
          border-top: none;

          & > .ant-row {
            width: 100%;
            margin-bottom: 0;

            .ant-form-item-explain.ant-form-item-explain-connected {
              margin-bottom: -24px;
            }
          }
        }
      }

      .config-list--y {
        display: flex;
        .config-list__col:last-child {
          .col__item {
            border-right: none;
          }
        }

        .col__header {
          background-color: #f7f7f7 !important;
        }

        .col__item {
          background-color: #fff;
        }
      }
    }
  }
}

.express {
  width: 100%;

  & > div:first-child {
    padding-left: 0 !important;
  }

  .express__flag {
    line-height: 32px;
    text-align: center;
    font-size: 21px;
    color: #acafb5;
    padding: 0 !important;
  }
}
