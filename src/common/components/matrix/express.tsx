import React from 'react';
import { DeleteOutlined } from '@ant-design/icons';
import { regularCheck } from '@/common/utils';
import { Row, Col, Form, Select, Input } from 'antd';
import { FormulaWithSearch } from '../FormulaWithSearch';

const operators = [
  { key: 'LT', value: '<' },
  { key: 'LTEQ', value: '<=' },
];

interface IPropsExpress {
  deleteHandler: () => void;
  options: any;
  formName?: any;
  director: string;
  [key: string]: any;
}

export const Express: React.FC<IPropsExpress> = (props: IPropsExpress) => {
  const formName = props.formName || [];
  return (
    <Row
      // key={props.formName.toString()}
      align="middle"
      gutter={[10, 0]}
      className="express"
    >
      <Col key="col-2" span={4} offset={0}>
        <Form.Item
          preserve={false}
          key={0}
          name={[...formName, 'conditionName']}
          style={{ width: '60px' }}
          rules={[{ required: false, message: '请选择运算符' }]}
          initialValue=""
          noStyle
        >
          <Input placeholder="分段名称" />
        </Form.Item>
      </Col>

      <Col key="col-1" span={6} flex="2">
        <Form.Item
          preserve={false}
          key={1}
          name={[...formName, 'leftExpression']}
          style={{ padding: '0px 10px 0px 0px', width: '100px' }}
          noStyle
          rules={[
            {
              validator: (_, value) => regularCheck(props.options, value),
            },
          ]}
        >
          <FormulaWithSearch
            tracertInfo={props?.tracertInfo}
            wrap={false}
            key="leftExpression"
            options={props.options}
            name={`${props?.name}/左`}
            periodTypeWatch={props?.periodTypeWatch}
            periodValueWatch={props?.periodValueWatch}
            periodValueMultiWatch={props?.periodValueMultiWatch}
            businessGroup={props.businessGroup}
            calendarMode={props?.calendarMode}
          />
        </Form.Item>
      </Col>

      <Col key="col-2" span={3} offset={0}>
        <Form.Item
          preserve={false}
          key={2}
          name={[...formName, 'leftSymbol']}
          style={{ width: '60px' }}
          rules={[{ required: false, message: '请选择运算符' }]}
          initialValue="LTEQ"
          noStyle
        >
          <Select key="select-leftSymbol">
            {operators.map((item) => (
              <Select.Option className="symbol-option" key={`left-${item.key}`} value={item.key}>
                {item.value}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col>

      <Col span={1} key="col-3" className="express__flag">
        <span>{props.director || 'X'}</span>
      </Col>

      <Col key="col-4" span={3}>
        <Form.Item
          preserve={false}
          key={4}
          name={[...formName, 'rightSymbol']}
          style={{ width: '60px' }}
          rules={[{ required: false, message: '请选择运算符' }]}
          noStyle
          initialValue="LT"
        >
          <Select key="select-rightSymbol">
            {operators.map((item) => (
              <Select.Option className="symbol-option" key={`right-${item.key}`} value={item.key}>
                {item.value}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col>

      <Col key="col-5" span={6}>
        <Form.Item
          preserve={false}
          key={5}
          name={[...formName, 'rightExpression']}
          style={{ padding: '0px 0px 0px 10px', width: '100px' }}
          noStyle
          rules={[
            {
              validator: (_, value) => regularCheck(props.options, value),
            },
          ]}
        >
          <FormulaWithSearch
            wrap={false}
            key="rightExpression"
            options={props.options}
            tracertInfo={props?.tracertInfo}
            name={`${props?.name}/右`}
            periodTypeWatch={props?.periodTypeWatch}
            periodValueWatch={props?.periodValueWatch}
            periodValueMultiWatch={props?.periodValueMultiWatch}
            businessGroup={props?.businessGroup}
            calendarMode={props?.calendarMode}
          />
        </Form.Item>
      </Col>

      <Col key="col-6" span={1}>
        <DeleteOutlined
          key="remove"
          className="delect-icon"
          onClick={() => props.deleteHandler()}
        />
      </Col>
    </Row>
  );
};
