import React, { useState, useEffect, useCallback } from 'react';
import { Form, Row, Col, Button, message, Input, Select } from 'antd';
import { regularCheck } from '@/common/utils';
import { FormulaValue } from '../formula';
import { FormulaWithSearch } from '../FormulaWithSearch';
import { Express } from './express';
import { MatrixType } from './type';
import './style.less';

export const Matrix: React.FC<MatrixType.Props> = (props) => {
  const completeFormName = [...(props.parentFormName || []), ...(props.formName || [])];
  const [isInitialed, setIsInitialed] = useState(false);
  const [indicatorListX, setIndicatorListX] = useState<number[]>([0]);
  const [indicatorListY, setIndicatorListY] = useState<number[]>([]);
  const [indicatorX, setIndicatorX] = useState<string>();
  const [indicatorY, setIndicatorY] = useState<string>();
  // 控制Y指标的显示与隐藏
  const indicatorYvisible = true;

  console.log('Matrix state', indicatorX, props);

  /** 添加指标 */
  const addIndicator = useCallback(
    (direct = 'x') => {
      console.log('Matrix addIndicator', direct);

      if (direct === 'x') {
        const tArrx = [...indicatorListX];
        tArrx.push(tArrx.length ? Math.max(...indicatorListX) + 1 : 0);
        setIndicatorListX(tArrx);
      } else {
        const tArry = [...indicatorListY];
        tArry.push(tArry.length ? Math.max(...indicatorListY) + 1 : 0);
        setIndicatorListY(tArry);
      }
    },
    [indicatorListX, indicatorListY],
  );

  const removeIndicator = (index = 0, direct = 'x') => {
    console.log('Matrix removeIndicator', index, direct);
    const formData = props.form.getFieldValue(completeFormName);

    /** 清除Form中的数据 */
    if (formData?.arrayExpressions?.ruleConditions) {
      delete formData.arrayExpressions.ruleConditions[`${direct}-${index}`];
    }

    if (formData?.arrayExpressions?.expression) {
      Object.keys(formData.arrayExpressions.expression).forEach((key) => {
        const [xIndex, yIndex] = key.split('-');
        if (direct === 'x') {
          if (+xIndex === index) {
            delete formData.arrayExpressions.expression[key];
          }
        } else if (direct === 'y') {
          if (+yIndex === index) {
            delete formData.arrayExpressions.expression[key];
          }
        }
      });
    }
    console.log('Matrix removeIndicator newform', formData);

    if (direct === 'x') {
      if (indicatorListX.length === 1 && indicatorX) {
        message.warn('必须保留一个X指标');
        return;
      }
      const tArrx = [...indicatorListX];
      const deleteIndex = tArrx.findIndex((id) => id === index);
      tArrx.splice(deleteIndex, 1);
      console.log('Matrix removeIndicator index', index, deleteIndex, indicatorListX);
      setIndicatorListX(tArrx);
    } else {
      if (indicatorListY.length === 1 && indicatorY) {
        message.warn('必须保留一个Y指标');
        return;
      }
      const tArry = [...indicatorListY];
      tArry.splice(index, 1);
      setIndicatorListY(tArry);
    }
    if (props.removeInvalidFormData) props.removeInvalidFormData(formData);
  };

  useEffect(() => {
    const formData = props.form.getFieldValue(completeFormName);
    const xNewList: number[] = [];
    const yNewList: number[] = [];
    Object.keys(formData?.arrayExpressions?.ruleConditions || {}).forEach((v) => {
      if (v.startsWith('x')) xNewList.push(+v.replace('x-', ''));
      else yNewList.push(+v.replace('y-', ''));
    });

    if (
      isInitialed &&
      indicatorListX.join() === xNewList.join() &&
      indicatorListY.join() === yNewList.join()
    )
      return;
    console.log('Matrix useEffect props.defaultData', completeFormName.join(), formData);

    setIndicatorX(formData?.indicatorIdX);
    setIndicatorY(formData?.indicatorIdY);

    setIndicatorListX(xNewList);
    setIndicatorListY(yNewList);
    setIsInitialed(true);
  }, [props.formName, props.defaultData]);

  // const onIndicatorXChange = useCallback((value) => {
  //   console.log('Matrix onIndicatorXChange', indicatorX, value, indicatorListX);

  //   setIndicatorX(value);
  //   if (!value) {
  //     setIndicatorListX([]);
  //     setIndicatorListY([]);
  //   } else if (indicatorListX.length === 0) {
  //     const tArrx = [...indicatorListX];
  //     tArrx.push(tArrx.length ? Math.max(...tArrx) + 1 : 0);
  //     setIndicatorListX(tArrx);
  //   }
  // }, [indicatorListX]);

  const onIndicatorYChange = useCallback(
    (value) => {
      setIndicatorY(value);
      if (!value) {
        setIndicatorListY([]);

        /** 清除公式数据 */
        props.form.resetFields([[...completeFormName, 'arrayExpressions', 'expression']]);
      } else {
        const tArry = [...indicatorListY];
        if (tArry.length === 0) {
          tArry.push(tArry.length ? Math.max(...tArry) + 1 : 0);
          setIndicatorListY(tArry);
        }
      }
    },
    [indicatorListY],
  );

  /** 快速录入 */
  const onQuickImport = useCallback(() => {
    /** 快速录入内容 */
    const quickImportStr: string = props.form.getFieldValue([...completeFormName, 'quickContent']);
    if (quickImportStr) {
      console.log('Matrix onQuickImport ', quickImportStr.split(' '));

      /** 得分列数据 */
      const expressionList: {
        [position: string]: FormulaValue;
      } = props.form.getFieldValue([...completeFormName, 'arrayExpressions', 'expression']);

      if (expressionList && Object.keys(expressionList).length) {
        /** 导入内容 */
        const importArr = quickImportStr.split(' ').filter((v) => parseFloat(v));
        /** 新的得分结果 */
        const newExpressions = {};
        Object.keys(expressionList).forEach((position, index) => {
          const foumulaInfo = Object.assign({}, expressionList[position]);
          if (importArr[index]) {
            foumulaInfo.expression = importArr[index];
            foumulaInfo.textExpression = importArr[index];
          }
          newExpressions[position] = foumulaInfo;
        });
        if (props.onQuickImport) props.onQuickImport(newExpressions);
      }
    }
  }, [props]);

  const renderDirectionX = useCallback(
    (yIndex = 0) =>
      indicatorListX.map((item) => (
        <div key={`col__item-${item}`} className="col__item">
          <Form.Item
            style={{ width: '100%' }}
            key={`xy-express-${item}-`}
            fieldKey={`${item}-${yIndex}`}
            name={[...props.formName, 'arrayExpressions', 'expression', `${item}-${yIndex}`]}
            preserve={false}
            // validateTrigger={['onBlur']}
            shouldUpdate
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if (value && value.expression !== '') {
                    return regularCheck(props.options, value);
                  }
                  return Promise.reject(new Error('指标必填'));
                },
                // (value && value.expression !== '')
                // ? Promise.resolve()
                // : Promise.reject(new Error('指标必填'))),
              },
            ]}
          >
            <FormulaWithSearch
              tracertInfo={props?.tracertInfo}
              wrap={false}
              key={`xy-express-formula-${item}-${yIndex}`}
              options={props.options}
              name={`${props?.tracertname}/得分`}
              periodTypeWatch={props?.periodTypeWatch}
              periodValueWatch={props?.periodValueWatch}
              periodValueMultiWatch={props?.periodValueMultiWatch}
              businessGroup={props.businessGroup}
              calendarMode={props?.calendarMode}
            />
          </Form.Item>
        </div>
      )),
    [indicatorX, indicatorY, indicatorListX, props.options],
  );

  /** 渲染指标Y */
  const renderDirectionY = useCallback(
    () =>
      indicatorListY.map((item, index) => (
        <div key={`config-list--y__item-${item}`} className="config-list__col">
          <div className="col__item">
            <Express
              tracertInfo={props?.tracertInfo}
              director="Y"
              key={`y-express-${item}-${indicatorY}`}
              formName={[...props.formName, 'arrayExpressions', 'ruleConditions', `y-${item}`]}
              options={props.options}
              deleteHandler={() => removeIndicator(index, 'y')}
              periodTypeWatch={props?.periodTypeWatch}
              periodValueWatch={props?.periodValueWatch}
              periodValueMultiWatch={props?.periodValueMultiWatch}
            />
          </div>

          {renderDirectionX(item)}
        </div>
      )),
    [indicatorY, indicatorListX, indicatorListY, props.options],
  );

  const renderScore = useCallback(
    () => (
      <div key="config-list--y__col" className="config-list__col">
        <div className="col__header">
          <div className="header--single">得分</div>
        </div>

        {renderDirectionX()}
      </div>
    ),
    [indicatorListX, props.options],
  );

  return (
    <div className="matrix">
      <Form.Item
        name={[...props.formName, 'indicatorIdX']}
        label="考核结果"
        rules={[
          {
            required: true,
            message: '请输入考核结果',
          },
          {
            required: true,
            validator: (_, value) => regularCheck(props.options, value),
          },
        ]}
      >
        <FormulaWithSearch
          tracertInfo={props?.tracertInfo}
          options={props.options}
          name={`${props?.tracertname}/考核结果`}
          periodTypeWatch={props?.periodTypeWatch}
          periodValueWatch={props?.periodValueWatch}
          periodValueMultiWatch={props?.periodValueMultiWatch}
          businessGroup={props.businessGroup}
          calendarMode={props?.calendarMode}
        />
      </Form.Item>
      <div className="matrix__top" style={indicatorYvisible ? { display: 'none' } : null}>
        {/* <Form.Item
          preserve={false}
          hidden={indicatorYvisible}
          name={[...props.formName, 'indicatorIdX']} label="指标X"
          validateTrigger={['onBlur']}
          rules={[{ required: true, message: '指标X必填' }]}
          style={{ padding: '0px 0px 0px 10px', width: '300px', marginBottom: 0 }}
        >
          <Select<string> showSearch onChange={onIndicatorXChange}>
            {
              props.options.map(item =>
                <Select.Option key={item.value} value={item.value}>{item.label}</Select.Option>)
            }
          </Select>
        </Form.Item> */}

        {indicatorX && (
          <Form.Item
            hidden={indicatorYvisible}
            name={[...props.formName, 'indicatorIdY']}
            label="指标Y"
            preserve={false}
            style={{ padding: '0px 0px 0px 10px', width: '300px', marginBottom: 0 }}
          >
            <Select<string> showSearch allowClear onChange={onIndicatorYChange}>
              {props.options.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )}
      </div>

      <Row gutter={24}>
        <Col span={18}>
          <Form.Item
            hidden={indicatorYvisible}
            label="快速录入"
            style={{ margin: '0 40px 10px 30px' }}
            name={[...props.formName, 'quickContent']}
          >
            <Input placeholder="快速录入矩阵结果" />
          </Form.Item>
        </Col>

        <Col>
          <Button hidden={indicatorYvisible} onClick={onQuickImport}>
            录入
          </Button>
        </Col>
      </Row>

      <div className="matrix__content">
        <div className="matrix__content__config-list">
          <div className="config-list--x config-list__col">
            {indicatorY ? (
              <div className="col__header">
                <div className="header--default">
                  <div className="header__item--x">X指标</div>
                  <div className="header__item--y">Y指标</div>
                </div>
              </div>
            ) : (
              <div className="col__header">
                <div className="header--single">
                  <div className="header__item--x">X指标</div>
                </div>
              </div>
            )}
            {indicatorListX.map((id) => (
              <Form.Item key={`config-list__item-${id}`} shouldUpdate>
                <div className="col__item">
                  <Express
                    tracertInfo={props?.tracertInfo}
                    director="X"
                    key={`x-express-${id}`}
                    formName={[...props.formName, 'arrayExpressions', 'ruleConditions', `x-${id}`]}
                    options={props.options}
                    deleteHandler={() => removeIndicator(id, 'x')}
                    name={`${props?.tracertname}/x指标`}
                    periodTypeWatch={props?.periodTypeWatch}
                    periodValueWatch={props?.periodValueWatch}
                    periodValueMultiWatch={props?.periodValueMultiWatch}
                    businessGroup={props.businessGroup}
                    calendarMode={props?.calendarMode}
                  />
                </div>
              </Form.Item>
            ))}
          </div>

          <div key="config-list--y" className="config-list--y config-list__col">
            {indicatorY ? renderDirectionY() : renderScore()}
          </div>
        </div>

        {indicatorY && (
          <Button
            className="matrix__add-btn--y"
            type="default"
            style={{ width: '30px', height: '100px' }}
            onClick={() => addIndicator('y')}
          >
            添加Y分段
          </Button>
        )}
      </div>

      <Button className="matrix__add-btn--x" type="default" onClick={() => addIndicator('x')}>
        添加X分段
      </Button>
    </div>
  );
};
export default Matrix;
