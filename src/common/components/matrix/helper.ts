// ============= FORM数据转组件数据 ==============

import groupBy from 'lodash.groupby';
import isEmpty from 'lodash.isempty';
import { ArrayRuleGroupDTO } from '@/_docplus/target/types/gateway';
import { OptionItemTypePrefix } from '../formula/constant';
import { OptionItem } from '../formula/type';

export function transformExpressToFormula(expressionData) {
  const regPeek = /[+\-*/)(]/;
  let varMap = {};
  // if (expressionData?.type === 'INDICATOR') {
  if (expressionData?.type === 'EXPRESSION') {
    const { expression, textExpression, varMap: _varMap } = expressionData?.expression || {};
    const varId = expression.split(regPeek);
    const varName = textExpression?.split(regPeek);
    if (textExpression) {
      varId.forEach((ele, index) => {
        if (ele && '$#@'.includes(ele?.[0])) {
          varMap[ele] = varName[index];
        }
      });
    } else {
      varMap = _varMap;
    }
    return {
      expression,
      textExpression,
      varMap,
      type: 'COMMON',
    };
  }
  if (expressionData?.type === 'CONSTANT') {
    return {
      expression: expressionData?.values[0],
      textExpression: expressionData?.values[0],
      varMap: {},
    };
  }
  if (expressionData?.type === 'MIN' || expressionData?.type === 'MAX') {
    return null;
  }
  return {
    expression: '',
    textExpression: '',
    varMap,
  };
}

export function parseArrayExpressionObjIndicator(k, v, type = 'INDICATOR') {
  const expression = k;
  const textExpression = v;
  const varMap = {};
  varMap[expression] = textExpression;
  return {
    expression,
    textExpression,
    varMap: type === 'INDICATOR' ? varMap : {},
  };
}

/** 构建矩阵内部数据 */
export function parseMatrixFormToData(data) {
  let arrayExpressionObj: any = {};
  if (data) {
    arrayExpressionObj = {
      indicatorIdX: data?.indicatorX || null,
      arrayExpressions: {},
    };
    const arrayExpressions = {
      ruleConditions: {},
      expression: {},
    };
    if (data?.arrayExpressions) {
      data.arrayExpressions.forEach((m) => {
        // const [x, y] = m.position.split('-');
        const x = m.position;
        arrayExpressions.expression[`${m.position}-0`] = m.expression;
        const ruleConditionsX = m.condition || (undefined as any);
        if (ruleConditionsX) {
          arrayExpressions.ruleConditions[`x-${x}`] = {
            conditionName: ruleConditionsX.conditionName,
            leftExpression: transformExpressToFormula(ruleConditionsX?.leftValue),
            rightExpression: transformExpressToFormula(ruleConditionsX?.rightValue),
            leftSymbol: ruleConditionsX.leftOperator,
            rightSymbol: ruleConditionsX.rightOperator,
          };
        }
      });
    }
    arrayExpressionObj.arrayExpressions = arrayExpressions;
  }
  return arrayExpressionObj;
}

// ============= 组件数据转FORM数据 ==============

export function transFormularToContain(formulaObj: any = {}, type = '') {
  console.log('transFormularToContain', formulaObj);
  const { expression, /* textExpression */ varMap } = formulaObj || {};
  if (type === 'MAX' || type === 'MIN') {
    return {
      code: '',
      name: '',
      values: [],
      type,
    };
  }
  if (varMap && isEmpty(varMap)) {
    // CONSTANT
    return {
      // code: null,
      // name: null,
      expression: null,
      values: [expression],
      type: 'CONSTANT',
    };
  }

  return {
    // code: expression,
    // name: textExpression,
    expression: formulaObj,
    values: null,
    // type: 'INDICATOR',
    type: 'EXPRESSION',
  };
}

/** 将指标转成带类型前缀 */
export function indicatorIdToData(id: string, options: OptionItem[]) {
  const indicator = options.find((o) => o.value === id);
  const prefix = indicator?.type ? OptionItemTypePrefix[indicator.type] : '';

  return {
    id: id || null,
    idWithPrefix: id ? `${prefix}${id}` : null,
    lable: id ? indicator?.label : null,
  };
}

/** 解析矩阵内部数据给Form提交 */
export function parseMatrixDataToForm(
  data: any,
  options: OptionItem[],
): Partial<ArrayRuleGroupDTO> {
  if (!data) return {} as any;
  const indicatorIdXData = indicatorIdToData(data.indicatorIdX, options);
  let arrayExpressions = [];
  if (data.arrayExpressions) {
    const ruleConditionsObj = data.arrayExpressions.ruleConditions;
    const expressionObj = data.arrayExpressions.expression || {};

    const fixedKeysMap = Object.values(
      groupBy(Object.keys(expressionObj)?.sort(), (o) => o[0]),
    ).reduce((prev, next, index) => {
      next.forEach((item, subIndex) => {
        // eslint-disable-next-line no-param-reassign
        prev[`${index}-${subIndex}`] = item;
      });
      return prev;
    }, {});

    console.log('fixedKeysMap', fixedKeysMap);

    arrayExpressions = Object.keys(fixedKeysMap)
      .map((position) => {
        // const [positionX, positionY] = position.split('-')
        const [positionX] = fixedKeysMap[position].split('-');

        let tRuleConditions = {};
        const conditionX: any = Object.assign({}, ruleConditionsObj[`x-${positionX}`]);

        if (!isEmpty(conditionX)) {
          let typex = '';
          let typey = '';
          if (isEmpty(conditionX.leftExpression)) {
            typex = 'MIN';
          }
          if (isEmpty(conditionX.rightExpression)) {
            typey = 'MAX';
          }
          conditionX.leftExpression = transFormularToContain(conditionX.leftExpression, typex);
          conditionX.rightExpression = transFormularToContain(conditionX.rightExpression, typey);
          tRuleConditions = {
            conditionName: conditionX.conditionName,
            leftValue: conditionX.leftExpression,
            rightValue: conditionX.rightExpression,
            leftOperator: conditionX.leftSymbol,
            rightOperator: conditionX.rightSymbol,
          };
        }

        const obj = {
          position: positionX,
          expression: expressionObj[fixedKeysMap[position]],
          condition: tRuleConditions,
        };
        return obj;
      })
      .sort((a, b) => a.position - b.position)
      .map((obj, index) => ({ ...obj, position: index }));
  }
  const result = {
    indicatorX: data?.indicatorIdX,
    indicatorXName: indicatorIdXData.lable,
    arrayExpressions,
  };
  return result;
}
