# 矩阵组件说明

## 依赖组件
- Formula：公式组件
- Express：判断表达式组件

## 数据构建

### 表单提交数据整理

```ts
import { parseMatrixDataToForm } from './matrix/helper'

const SomeComp = props => {

  const onFinish  = (data) => {
    /** 将数据构建成提交数据结构 */
    data.myMatrix = parseMatrixDataToForm(data.myMatrix, options)
    updateIndicatorRequest(data)
  }

  return (
    <Form onFinish={onFinish}>
      <Matrix formName="myMatrix">
    </Form>
  )
}
```

### 表单数据组件回显

```ts
import { parseMatrixFormToData } from './matrix/helper'

const SomeComp = props => {
  const form = useForm()

  useEffect(() => {
    /** 将指标数据构建成组件回显结构 */
    const myMatrix = parseMatrixFormToData(value.myMatrix)
    form.setFieldsValue({
      myMatrix
    })
  }, [value])

  return (
    <Form form={form} onFinish={onFinish}>
      <Matrix formName="myMatrix">
    </Form>
  )
}
```
