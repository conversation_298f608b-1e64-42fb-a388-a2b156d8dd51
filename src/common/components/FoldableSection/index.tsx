import React, { CSSProperties, useRef, useState } from 'react';
import classNames from 'classnames';
import { UpOutlined } from '@ant-design/icons';
import './style.less';

interface FoldableSectionProps {
  /** 外部控制折叠 */
  // fold?: boolean;
  /** 常显部分 */
  // static?: React.ReactNode;
  /** 按钮自定义 */
  // handlerNode?: (isFold: boolean, changeFold: (isFold: boolean) => void) => React.ReactNode;
  /** 展示状态变化 */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  // onChange?: (isFold: boolean) => void;
  /** 默认折叠 */
  defaultFold?: boolean;
  /** 折叠清除子元素 */
  // destoryChildOnFold?: boolean;
}

const ANIMATION_DURATION = 200;

/** 可折叠区域 */
export const FoldableSection: React.FC<FoldableSectionProps> = (props) => {
  const contentRef = useRef<HTMLDivElement>();
  const maxHeightRef = useRef(10000);

  const [isFold, setIsFold] = useState(props.defaultFold ?? false);
  const [isDuringAnimation, setIsDuringAnimation] = useState(false);

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const startAnimation = () => {
    if (timeoutRef.current !== null) clearTimeout(timeoutRef.current);

    setIsDuringAnimation(true);
    timeoutRef.current = setTimeout(() => {
      setIsDuringAnimation(false);
    }, ANIMATION_DURATION);
  };

  const onChange = () => {
    setIsFold(prev => {
      maxHeightRef.current = contentRef?.current?.offsetHeight || 1e5;
      startAnimation();
      return !prev;
    });
  };

  const getMaxHeight = (): CSSProperties['maxHeight'] => {
    if (isDuringAnimation) return maxHeightRef.current;
    return isFold ? 0 : 'unset';
  };

  return (
    <div className="u-br-2 u-flex-dir-col u-px-24 u-py-16 foldable-section">
      {/* {!!props.static && ( */}
      {/*  <div className="foldable-section__static"> */}
      {/*    {props.static} */}
      {/*  </div> */}
      {/* )} */}

      <div
        className={classNames('foldable-section__foldable', isFold && 'foldable-section__foldable__is-fold')}
        style={{ maxHeight: getMaxHeight() }}
      >
        <div className="foldable-section__children" ref={contentRef}>
          {props.children}
        </div>
      </div>

      <div className="foldable-section__handler u-c-hl-main">
        <span className="u-cursor-pointer" onClick={onChange}>
          <span className="u-mr-5">更多设置</span>
          <UpOutlined className={classNames('u-fs-12', 'foldable-section__icon', isFold && 'foldable-section__icon__is-fold')} />
        </span>
      </div>
    </div>
  );
};

// FoldableSection.defaultProps = {
//   defaultFold: true,
//   destoryChildOnFold: false,
//   handlerNode: (isFold, changeFold) => (
//     <span className="u-cursor-pointer" onClick={() => changeFold(!isFold)}>
//       <span className="u-mr-5">更多设置</span>
//       { isFold ? <DownOutlined className="u-fs-12" /> : <UpOutlined className="u-fs-12" /> }
//     </span>
//   ),
// };
