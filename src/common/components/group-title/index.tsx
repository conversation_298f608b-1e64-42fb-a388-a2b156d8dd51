import React, { FC } from 'react';
import { Input, InputProps, Space } from 'antd';
import {
  DeleteOutlined,
  FormOutlined,
  FileAddOutlined,
  DownSquareOutlined,
  UpSquareOutlined,
} from '@ant-design/icons';
import './style.less';

interface GroupTitleProps extends InputProps {
  onCopy?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onUp?: () => void;
  onDown?: () => void;
  isInput?: boolean;
}

/** 组标题组件 */
export const GroupTitle: FC<GroupTitleProps> = ({
  onCopy,
  onEdit,
  onDelete,
  onUp,
  onDown,
  isInput = true,
  ...inputProps
}) => (
  <div className={isInput ? 'group-title-component' : 'group-title-component single'}>
    {isInput && <Input {...inputProps} />}

    <Space>
      {onEdit && <FormOutlined onClick={onEdit} />}
      {onUp && <UpSquareOutlined onClick={onUp} />}
      {onDown && <DownSquareOutlined onClick={onDown} />}
      {onCopy && <FileAddOutlined onClick={onCopy} />}
      {onDelete && <DeleteOutlined onClick={onDelete} />}
    </Space>
  </div>
);

export default GroupTitle;
