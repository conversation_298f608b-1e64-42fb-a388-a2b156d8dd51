
.rule-group-component {
  width: 1000px;

  .rule {
    margin: 10px;
    box-shadow: 0 0 0 1px #ebebeb;
    // background-color: #f7f7f7;
    .ruleConditions-container {
      padding: 10px;
    }

    .result-container {
      padding: 10px;

      .result-mix-container {
        padding: 10px;
      }
    }

    .rule-group-item {
      display: flex;
      justify-content: space-between;
      background: gray;
      height: 52px;
      align-items: center;
      line-height: 52px;
      margin-left: 12px;

      p {
        margin-bottom: 0;
      }
    }

    .rule-list-item {
      border: 1px solid rgba(0, 0, 0, 0.15);
      margin-left: 12px;
      // padding: 24px 24px 0 !important;
    }
  }
}
