import { REG_EXPRESSION, ExpressionType } from '@/common/components/formula';
import { isArray } from 'lodash';
import moment from 'moment';
/**
 * 将表单数据转成内部表达式结构
 * @example expression: executePeriodVar(@123,2022-05-20)/@123'
 */
function defaultParseFormToData(formData) {
  if (!formData) {
    return null;
  }

  /** 匹配ID的正则 */
  const idMatchExp = Object.keys(formData.varMap)
    .filter((o) => !!o.replace(/\d/g, ''))
    .reduce((prev, next) => {
      const curItem = next.includes('$') ? `\\${next}` : next;
      return prev.concat([curItem, `executePeriodVar\\(${curItem},MONTH,\\d{6},content\\)`]);
    }, [])
    .join('|');

  /** 公式中的指标 */
  const indicatorList = formData?.expression
    ?.match(new RegExp(`(${idMatchExp})`, 'g'))
    ?.filter((o) => !!o);
  if (!indicatorList?.length) {
    /** 如果没有变量则直接返回文本表达式 */
    if (formData.expression) return [{ label: formData.expression }];
    return [];
  }

  const result = [];
  /** 待处理公式 */
  let remainFormula = formData.expression;

  indicatorList.forEach((item, index) => {
    /** 指标元素结果数据 */
    const resultItem = { label: '', value: '', date: '' };
    /** 当前指标所在开始索引 */
    const itemStartIndex = remainFormula.indexOf(item);
    /** 替换掉当前指标前缀字符串 */
    const prefix = remainFormula.substring(0, itemStartIndex);
    /** 替换掉当前指标后缀字符串 */
    const subfix = remainFormula.substring(itemStartIndex + item.length);

    if (prefix) {
      result.push({ label: prefix });
    }

    /** 通过正常匹配出ID */
    const idSection = item.match(REG_EXPRESSION.IdWithPrefix)?.[0] || '';
    resultItem.value = idSection.slice(1);
    resultItem.date = (item.match(REG_EXPRESSION.DateFromExp)?.[0] || '').substring(1);
    result.push(resultItem);
    remainFormula = subfix || '';

    if (index === indicatorList.length - 1 && subfix) {
      result.push({ label: subfix });
    }
  });

  return result;
}

/** 构建规则组数据结构 */
export function parseRuleGroupFormToData(values) {
  const {
    expressionObj,
    indicatorId,
    ruleGroupType,
    indicatorType,
    expression,
    tableDimColumn,
    tableExtraColumn,

    groupIndicatorAggType,
    sortType,
    groupRuleDim,
    groupTargetType,
    groupValueType,
    itemCode, // 费用项
    staffRelationType, // 下属关系
    indicatorAggType, // 计算逻辑
    traceCycle, // 追溯周期
  } = values || {};

  const data = {
    indicatorRules: undefined,
    tableDimColumn: undefined,
    tableExtraColumn: undefined,
  } as any;

  // 规则生成，公式
  if (indicatorType === 'COMPLEX_INDICATOR') {
    const expressionData = defaultParseFormToData(expressionObj?.expression);
    const upperLimitData = defaultParseFormToData(expressionObj?.upperLimit);
    const lowLimitData = defaultParseFormToData(expressionObj?.lowLimit);
    let expressionExpression = '';
    let upperLimitExpression = '';
    let lowLimitExpression = '';

    expressionData?.forEach((item) => {
      if (item.value) {
        expressionExpression += item.date
          ? `executePeriodVar('${item.value}','MONTH','${item.date}',content)`
          : `@${item.value}`;
      } else {
        expressionExpression += item.label.replace(/\s/g, '');
      }
    });

    upperLimitData?.forEach((item) => {
      if (item.value) {
        upperLimitExpression += item.date
          ? `executePeriodVar('${item.value}','MONTH','${item.date}',content)`
          : `@${item.value}`;
      } else {
        upperLimitExpression += item.label.replace(/\s/g, '');
      }
    });

    lowLimitData?.forEach((item) => {
      if (item.value) {
        lowLimitExpression += item.date
          ? `executePeriodVar('${item.value}','MONTH','${item.date}',content)`
          : `@${item.value}`;
      } else {
        lowLimitExpression += item.label.replace(/\s/g, '');
      }
    });

    data.indicatorRules = [
      {
        indicatorId,
        ruleCondition: {
          ruleType: 'QL_EXPRESSION',
          qlRuleCondition: {
            ruleGroupType,
            expressionRuleGroup: {
              expression: expressionExpression
                ? {
                    ...expressionObj?.expression,
                    expression: expressionExpression,
                    type: ExpressionType.Common,
                  }
                : null,
              upperLimit: upperLimitExpression
                ? {
                    ...expressionObj?.upperLimit,
                    expression: upperLimitExpression,
                    type: ExpressionType.Common,
                  }
                : null,
              lowLimit: lowLimitExpression
                ? {
                    ...expressionObj?.lowLimit,
                    expression: lowLimitExpression,
                    type: ExpressionType.Common,
                  }
                : null,
            },
          },
          ruleVersion: values.ruleVersion ? moment(values.ruleVersion).format('YYYYMM') : undefined,
        },
      },
    ];
  }
  // ODPS
  if (indicatorType === 'ODPS') {
    data.indicatorRules = null;
  }
  // HOLO
  if (indicatorType === 'HOLOGRES') {
    data.indicatorRules = [
      {
        indicatorId,
        ruleCondition: {
          ruleType: 'SQL_EXPRESSION',
          sqlRuleCondition: {
            expression,
          },
          ruleVersion: values.ruleVersion ? moment(values.ruleVersion).format('YYYYMM') : undefined,
        },
      },
    ];
  }

  if (indicatorType === 'LAKE_INDICATOR_CODE') {

    data.indicatorRules = [
      {
        ruleCondition: {
          ruleType: 'LAKE_INDICATOR_CODE',
          lakeRuleCondition: {
            lakeIndicatorCode: values.lakeIndicatorCode,
            sqlTemplateCode: values.sqlTemplateCode,
          },
          ruleVersion: values.ruleVersion ? moment(values.ruleVersion).format('YYYYMM') : undefined,
        },
      },
    ];
  }

  // GROUP_INDICATOR
  if (indicatorType === 'GROUP_INDICATOR') {
    const groupRuleDimArr = groupRuleDim?.split('&')?.map((item) => {
      const itemObj = {
        dimAliasCode: item,
      };
      return itemObj;
    });

    data.indicatorRules = [
      {
        indicatorId,
        ruleCondition: {
          ruleType: 'GROUP_INDICATOR_RULE',
          groupIndicatorRuleCondition: {
            periodType: 'MONTH',
            groupIndicatorAggType,
            sortType,
            groupRuleDim: groupRuleDimArr,
            groupTargetType,
            groupValueType,
          },
          ruleVersion: values.ruleVersion ? moment(values.ruleVersion).format('YYYYMM') : undefined,
        },
      },
    ];
  }

  if (indicatorType === 'CAL_ITEM_INDICATOR') {
    data.indicatorRules = [
      {
        indicatorId,
        ruleCondition: {
          ruleType: 'CAL_ITEM_CODE',
          calItemRuleCondition: {
            itemCode: isArray(itemCode) ? itemCode?.join(',') : itemCode, // 费用项
            staffRelationType, // 下属关系
            indicatorAggType, // 计算逻辑
            traceCycle, // 追溯周期
          },
          ruleVersion: values.ruleVersion ? moment(values.ruleVersion).format('YYYYMM') : undefined,
        },
      },
    ];
  }
  // tableDimColumn字段
  if (tableDimColumn?.length) {
    data.tableDimColumn = (tableDimColumn || []).map((item) => ({
      name: item?.split('/')?.[0],
      field: item?.split('/')?.[1],
    }));
  }
  // tableExtraColumn字段
  if (tableExtraColumn?.length) {
    data.tableExtraColumn = (tableExtraColumn || []).map((item) => ({
      name: item?.split('/')?.[0],
      field: item?.split('/')?.[1],
    }));
  }

  return data;
}

/** 构建规则组数据结构 */
export function parseDataToRuleGroupForm(resultData, targetVersion?: string) {
  const { indicatorType, tableDimColumn, tableExtraColumn, sourceType } = resultData || {};
  
  let firstRule;
  if (targetVersion && resultData?.indicatorRules?.length > 0) {
    const backendVersion = targetVersion.replace('-', '');
    firstRule = resultData.indicatorRules.find(rule => 
      (rule.ruleCondition as any)?.ruleVersion === backendVersion
    );
    if (!firstRule) return null;
  } else {
    firstRule = resultData?.indicatorRules?.[0];
  }
  
  const ruleCondition = firstRule?.ruleCondition;
  const ruleType = ruleCondition?.ruleType;
  
  // 基础数据
  const data = {
    expressionObj: ruleCondition?.qlRuleCondition?.expressionRuleGroup,
    ruleVersion: (ruleCondition as any)?.ruleVersion,
    expression: ruleCondition?.sqlRuleCondition?.expression,
    ruleGroupType: ruleCondition?.qlRuleCondition?.ruleGroupType,
    indicatorValueType: resultData?.indicatorValueType,
  } as any;

  // 根据规则类型设置生产方式和相关字段
  if (ruleType === 'SQL_EXPRESSION') {
    data.indicatorType = 'HOLOGRES';
    data.indicatorValueType = 'STRING';
  } else if (ruleType === 'CAL_ITEM_CODE') {
    data.indicatorType = 'CAL_ITEM_INDICATOR';
    data.indicatorValueType = 'NUMBER';
  } else if (ruleType === 'ODPS_IMPORT') {
    data.indicatorType = 'ODPS';
    data.datasourceId = ruleCondition?.odpsRuleCondition?.datasourceId;
    data.tableColumn = ruleCondition?.odpsRuleCondition?.tableColumn ? [ruleCondition.odpsRuleCondition.tableColumn] : undefined;
  } else if (ruleType === 'LAKE_INDICATOR_CODE') {
    data.indicatorType = 'LAKE_INDICATOR_CODE';
    data.lakeIndicatorCode = ruleCondition?.lakeRuleCondition?.lakeIndicatorCode;
    data.sqlTemplateCode = ruleCondition?.lakeRuleCondition?.sqlTemplateCode;
  } else if (ruleType === 'QL_EXPRESSION') {
    data.indicatorType = 'COMPLEX_INDICATOR';
  } else {
    if (indicatorType === 'ATOMIC_DIC' && sourceType === 'dic') {
      data.indicatorType = 'ODPS';
    } else if (indicatorType === 'ATOMIC_DIC' && sourceType === 'lake') {
      data.indicatorType = 'LAKE_INDICATOR_CODE';
    } else {
      data.indicatorType = indicatorType;
    }
  }
  
  if (!data.datasourceId) data.datasourceId = resultData?.datasourceId;
  if (!data.tableColumn) data.tableColumn = resultData?.tableColumn ? [resultData.tableColumn] : undefined;
  // 公式
  const { expression, lowLimit, upperLimit } = data.expressionObj || {};
  if (expression?.expression.includes('executePeriodVar')) {
    const a = Object.keys(expression.varMap)
      .filter((o) => !!o.replace(/\d/g, ''))
      ?.map((o) => o.split('@')?.[1]);
    let newExpression = expression?.expression.replace(/\'/g, '');
    a.map((i) => {
      newExpression = newExpression.replace(new RegExp(`${i}|@${i}`), `@${i}`);
    });
    data.expressionObj.expression = {
      ...expression,
      expression: newExpression,
    };
  }
  if (lowLimit?.expression.includes('executePeriodVar')) {
    const a = Object.keys(lowLimit.varMap)
      .filter((o) => !!o.replace(/\d/g, ''))
      ?.map((o) => o.split('@')?.[1]);
    let newExpression = lowLimit?.expression.replace(/\'/g, '');
    a.map((i) => {
      newExpression = newExpression.replace(new RegExp(`${i}|@${i}`), `@${i}`);
    });
    data.expressionObj.lowLimit = {
      ...lowLimit,
      expression: newExpression,
    };
  }
  if (upperLimit?.expression.includes('executePeriodVar')) {
    const a = Object.keys(upperLimit.varMap)
      .filter((o) => !!o.replace(/\d/g, ''))
      ?.map((o) => o.split('@')?.[1]);
    let newExpression = upperLimit?.expression.replace(/\'/g, '');
    a.map((i) => {
      newExpression = newExpression.replace(new RegExp(`${i}|@${i}`), `@${i}`);
    });
    data.expressionObj.upperLimit = {
      ...upperLimit,
      expression: newExpression,
    };
  }
  // tableDimColumn
  if (tableDimColumn?.length) {
    data.tableDimColumn = tableDimColumn?.map((item) => `${item?.name}/${item?.field}`);
  }
  // tableExtraColumn
  if (tableExtraColumn?.length) {
    data.tableExtraColumn = tableExtraColumn?.map((item) => `${item?.name}/${item?.field}`);
  }

  if (indicatorType === 'GROUP_INDICATOR') {
    data.groupIndicatorAggType =
      resultData?.indicatorRules?.[0]?.ruleCondition?.groupIndicatorRuleCondition?.groupIndicatorAggType;
    data.sortType =
      resultData?.indicatorRules?.[0]?.ruleCondition?.groupIndicatorRuleCondition?.sortType;
    data.groupRuleDim =
      resultData?.indicatorRules?.[0]?.ruleCondition?.groupIndicatorRuleCondition?.groupRuleDim
        ?.map((item) => item?.dimAliasCode)
        ?.join('&');
    data.groupTargetType =
      resultData?.indicatorRules?.[0]?.ruleCondition?.groupIndicatorRuleCondition?.groupTargetType;
    data.groupValueType =
      resultData?.indicatorRules?.[0]?.ruleCondition?.groupIndicatorRuleCondition?.groupValueType;
  }

  if (indicatorType === 'CAL_ITEM_INDICATOR' || ruleCondition?.ruleType === 'CAL_ITEM_CODE') {
    const calItemRule = firstRule?.ruleCondition?.calItemRuleCondition;
    if (calItemRule) {
      data.itemCode = calItemRule.itemCode?.split(',') || undefined;
      data.staffRelationType = calItemRule.staffRelationType || undefined;
      data.indicatorAggType = calItemRule.indicatorAggType || undefined;
      data.traceCycle = calItemRule.traceCycle;
    }
  }

  // 处理数据湖类型的字段
  if (ruleCondition?.ruleType === 'LAKE_INDICATOR_CODE') {
    const lakeRule = firstRule?.ruleCondition?.lakeRuleCondition;
    if (lakeRule) {
      data.lakeIndicatorCode = lakeRule.lakeIndicatorCode || undefined;
      data.sqlTemplateCode = lakeRule.sqlTemplateCode || undefined;
    }
  }

  if (resultData?.configType) {
    data.configType = resultData.configType;
  }

  return data;
}
