import { OptionItem } from '../formula/type';
import { parseMatrixDataToForm, parseMatrixFormToData } from '../matrix/helper';
import {
  MultiCriteriaRuleConditionDTO,
  MultiCriteriaRuleDTO, StandardExpressionRuleGroupDTO, ValueExpressionDTO,
} from '../../../_docplus/target/types/gateway';
import { DeepPartial } from '../../../_docplus/target/request/client';
import { ConditionRelationships } from '../condition-group';

const expressionToValueExpressionDTO = (expression): DeepPartial<ValueExpressionDTO> => ({
  type: 'EXPRESSION',
  expression,
});

export function parseRules(rules: any[]): DeepPartial<MultiCriteriaRuleConditionDTO[]> {
  if (!rules) return [];

  return rules.map((v): DeepPartial<MultiCriteriaRuleConditionDTO> => ({
    leftLabel: expressionToValueExpressionDTO(v.leftExpression),
    ruleOperator: v.expression,
    rightLabel: expressionToValueExpressionDTO(v.rightExpression),
  }));
}

/** 构建条件数据结构 */
export function parseRuleGroupDataToForm(indicatorRules: any = [], options: OptionItem[]) {
  return indicatorRules?.map((rule, index): DeepPartial<MultiCriteriaRuleDTO> => {
    if (!rule) return {};
    const {
      name,
      resultExpressionType,
      expression,
      lowLimit,
      upperLimit,
      conditionRelationship,
    } = rule;
    const ruleConditions = parseRules(rule.ruleConditions);

    // 规则组的值为：矩阵
    const arrayExpressionObj = rule.arrayExpressionObj
      ? parseMatrixDataToForm(rule.arrayExpressionObj, options)
      : undefined;
    if (arrayExpressionObj) {
      arrayExpressionObj.lowLimit = lowLimit;
      arrayExpressionObj.upperLimit = upperLimit;
    }

    // 规则组的值为：公式
    const expressionRuleGroup: DeepPartial<StandardExpressionRuleGroupDTO> = rule.expression
      ? { expression }
      : undefined;
    if (expressionRuleGroup) {
      expressionRuleGroup.lowLimit = lowLimit;
      expressionRuleGroup.upperLimit = upperLimit;
    }

    const result = {
      name,
      resultExpressionType,
      priority: index,
      ruleConditions,
      expressionRuleGroup,
      arrayRuleGroup: arrayExpressionObj,
      ruleConditionRelationType: conditionRelationship,
    };

    console.log('parseRuleGroupDataToForm', result);

    return result;
  });
}

/** 构建规则组数据结构 */
export function parseRuleGroupFormToData(resultIndicatorRules: Array<DeepPartial<MultiCriteriaRuleDTO>> = []) {
  if (!resultIndicatorRules?.length) {
    return [{ resultExpressionType: 'EXPRESSION' }];
  }

  return resultIndicatorRules
    .map((rule) => {
      if (!rule) return null;

      const result: any = {
        indicatorId: (rule as any).indicatorId, // FIXME anyscript issue
        resultExpressionType: rule.resultExpressionType || 'EXPRESSION',
        name: rule.name,
        conditionRelationship: rule.ruleConditionRelationType || ConditionRelationships.And,
        ruleConditions: rule?.ruleConditions?.map((conditionsData) => ({
          expression: conditionsData.ruleOperator,
          leftExpression: conditionsData.leftLabel.expression,
          rightExpression: conditionsData.rightLabel.expression,
        })),
      };

      if (rule.expressionRuleGroup) {
        result.expression = rule.expressionRuleGroup.expression;
        result.lowLimit = rule.expressionRuleGroup.lowLimit;
        result.upperLimit = rule.expressionRuleGroup.upperLimit;
      }
      if (rule.arrayRuleGroup) {
        result.arrayExpressionObj = parseMatrixFormToData(rule.arrayRuleGroup);
        result.lowLimit = rule.arrayRuleGroup.lowLimit;
        result.upperLimit = rule.arrayRuleGroup.upperLimit;
      }

      return result;
    })
    .filter(Boolean);
}
