import React from 'react';
import { Form, Button, Row, Col, Radio, FormInstance } from 'antd';
import { listNotEmptyValidator, regularCheck, startViewTransitionHelper } from '@/common/utils';
import get from 'lodash.get';
import cloneDeep from 'lodash.clonedeep';
import { PlusOutlined } from '@ant-design/icons';
import set from 'lodash.set';
import { RuleResultTypes } from './constant';
import { OptionItem } from '../formula';
import { FormulaWithSearch } from '../FormulaWithSearch';
import { Matrix } from '../matrix';
import { ConditionGroup } from '../condition-group';
import { GroupTitle } from '../group-title';
import './index.less';

interface IProps {
  formName: any[];
  form: FormInstance;
  options: OptionItem[];
  tracertname: string;
  // value: any
  [key: string]: any;
  defaultFormData: any;
}

export const RuleGroup: React.FC<IProps> = (props: IProps) => (
  <div className="rule-group-component">
    <Form.List
      name={[...props.formName]}
      initialValue={[{}]}
      rules={[{ validator: listNotEmptyValidator }]}
    >
      {(fields, { add, move, remove }, { errors }) => (
        <>
          {fields.map(({ name, ...restField }, index) => {
            const matrixTypeName = [...props.formName, name, 'resultExpressionType'];
            return (
              <div
                className="rule"
                key={restField.key}
                style={{
                  viewTransitionName: `${(props.formName || []).join('_')}_${restField.key}`,
                }}
              >
                {index !== 0 && (
                  <span
                    style={{
                      margin: 20,
                      display: 'inline-block',
                      fontSize: 12,
                    }}
                  >
                    如果上述【规则】均未执行，则执行下个【规则】判断
                  </span>
                )}

                <Form.Item
                  noStyle
                  {...restField}
                  key={`operation-${name}`}
                  preserve={false}
                  name={[name, 'name']}
                  style={{ marginBottom: 0 }}
                  fieldKey={[name, 'operation']}
                  initialValue={`规则${index + 1}`}
                >
                  <GroupTitle
                    placeholder="规则组"
                    onDelete={() => startViewTransitionHelper(() => remove(name))}
                    onDown={() => startViewTransitionHelper(() => move(name, name + 1))}
                    onUp={() => startViewTransitionHelper(() => move(name, name - 1))}
                    onCopy={() => {
                      const indicatorRulesFormData = props.form.getFieldValue(props.formName);
                      add(cloneDeep(indicatorRulesFormData[index]));
                    }}
                  />
                </Form.Item>

                <div className="ruleConditions-container" key={`ruleConditions-container-${name}`}>
                  <ConditionGroup
                    tracertInfo={props?.tracertInfo}
                    key={`ruleConditions-${name}`}
                    formName={[name, 'ruleConditions']}
                    conditionRelationshipFormName={[name, 'conditionRelationship']}
                    options={props.options}
                    tracertname={`${props?.tracertname}/条件组`}
                    periodTypeWatch={props?.periodTypeWatch}
                    periodValueWatch={props?.periodValueWatch}
                    periodValueMultiWatch={props?.periodValueMultiWatch}
                    businessGroup={props?.businessGroup}
                    calendarMode={props?.calendarMode}
                  />
                </div>

                <Form.Item
                  label="下限"
                  name={[name, 'lowLimit']}
                  style={{ margin: '10px 10px 10px 18px' }}
                  rules={[
                    {
                      validator: (_, value) => regularCheck(props.options, value),
                    },
                  ]}
                >
                  <FormulaWithSearch
                    tracertInfo={props?.tracertInfo}
                    dropdownWidth={910}
                    options={props.options}
                    name={`${props?.tracertname}/下限`}
                    periodTypeWatch={props?.periodTypeWatch}
                    periodValueWatch={props?.periodValueWatch}
                    periodValueMultiWatch={props?.periodValueMultiWatch}
                    businessGroup={props?.businessGroup}
                    calendarMode={props?.calendarMode}
                  />
                </Form.Item>

                <Form.Item
                  label="上限"
                  name={[name, 'upperLimit']}
                  style={{ margin: '10px 10px 10px 18px' }}
                  rules={[
                    {
                      validator: (_, value) => regularCheck(props.options, value),
                    },
                  ]}
                >
                  <FormulaWithSearch
                    tracertInfo={props?.tracertInfo}
                    dropdownWidth={910}
                    options={props.options}
                    name={`${props?.tracertname}/上限`}
                    periodTypeWatch={props?.periodTypeWatch}
                    periodValueWatch={props?.periodValueWatch}
                    periodValueMultiWatch={props?.periodValueMultiWatch}
                    businessGroup={props?.businessGroup}
                    calendarMode={props?.calendarMode}
                  />
                </Form.Item>

                <Form.Item
                  preserve={false}
                  key={`resultExpressionType-${name}`}
                  label="结果"
                  name={[name, 'resultExpressionType']}
                  rules={[{ required: true, message: '结果必选' }]}
                  style={{ margin: '10px' }}
                  initialValue="EXPRESSION"
                >
                  <Radio.Group
                    key={`resultExpressionType-radio-${name}`}
                    onChange={(e) => e.stopPropagation()}
                  >
                    {RuleResultTypes.map((item) => (
                      <Radio value={item.value} key={item.key}>
                        {item.text}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Form.Item>

                <Form.Item
                  className="result-mix-container"
                  noStyle
                  // eslint-disable-next-line max-len
                  shouldUpdate={(previous, currnet) =>
                    get(previous, matrixTypeName) !== get(currnet, matrixTypeName)
                  }
                >
                  {(form) =>
                    form.getFieldValue(matrixTypeName) === 'ARRAY' ? (
                      <Matrix
                        tracertname={`${props?.tracertname}/矩阵`}
                        tracertInfo={props?.tracertInfo}
                        form={props.form}
                        formName={[name, 'arrayExpressionObj']}
                        parentFormName={props.formName}
                        options={props.options}
                        removeInvalidFormData={(newMatrixData) => {
                          const oldFormData = props.form.getFieldValue(props.formName);
                          oldFormData[name] = {
                            ...oldFormData[name],
                            arrayExpressionObj: cloneDeep(newMatrixData),
                          };
                          const newFormData = {};

                          set(newFormData, props.formName, oldFormData);
                          console.log('Matrix oldFormData', newFormData);

                          props.form.setFieldsValue(newFormData);
                        }}
                        onQuickImport={(v) => {
                          // eslint-disable-next-line max-len
                          const indicatorRulesFormData = props.form.getFieldValue(props.formName);
                          if (!indicatorRulesFormData) return;
                          const o = {
                            indicatorRules: indicatorRulesFormData,
                          };
                          o.indicatorRules[name].arrayExpressionObj.quickContent = '';
                          // eslint-disable-next-line max-len
                          o.indicatorRules[name].arrayExpressionObj.arrayExpressions.expression = {
                            ...v,
                          };
                          if (props.form) {
                            console.log('快速录入--合并之后--', o);
                            props.form.setFieldsValue(o);
                          }
                        }}
                        periodTypeWatch={props?.periodTypeWatch}
                        periodValueWatch={props?.periodValueWatch}
                        periodValueMultiWatch={props?.periodValueMultiWatch}
                        calendarMode={props?.calendarMode}
                      />
                    ) : (
                      <Form.Item
                        preserve={false}
                        label="公式"
                        key={`expression-${name}`}
                        style={{ padding: '10px', width: '100%' }}
                        name={[name, 'expression']}
                        // validateTrigger={['onBlur']}
                        rules={[
                          {
                            required: true,
                            // message: '公式必填',
                            validator: (_, value) => {
                              if (value && value.expression !== '') {
                                return regularCheck(props.options, value);
                              }
                              return Promise.reject(new Error('公式必填'));
                            },
                          },
                        ]}
                      >
                        <FormulaWithSearch
                          tracertInfo={props?.tracertInfo}
                          dropdownWidth={910}
                          options={props.options}
                          name={`${props?.tracertname}/公式`}
                          periodTypeWatch={props?.periodTypeWatch}
                          periodValueWatch={props?.periodValueWatch}
                          periodValueMultiWatch={props?.periodValueMultiWatch}
                          businessGroup={props.businessGroup}
                          calendarMode={props?.calendarMode}
                        />
                      </Form.Item>
                    )
                  }
                </Form.Item>
              </div>
            );
          })}

          <Form.ErrorList errors={errors} />

          <Form.Item>
            <Row gutter={24} justify="start">
              <Col span={8}>
                <Button onClick={() => add()} block icon={<PlusOutlined />}>
                  添加规则
                </Button>
              </Col>
            </Row>
          </Form.Item>
        </>
      )}
    </Form.List>
  </div>
);

export default RuleGroup;
