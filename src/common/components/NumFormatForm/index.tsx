import React from 'react';
import { Form, Radio, InputNumber, ColProps } from 'antd';
import { createEnumObject } from 'ts-enum-object';
import { NamePath } from 'antd/es/form/interface';
import { formatEnumToOptions } from '@/common/utils';
import './style.less';

/** 单位 */
export const UnitEnum = createEnumObject([
  // { name: 'NOTHING', label: '无', value: 'NOTHING' },
  // { name: 'JIA', label: '家', value: 'JIA' },
  // { name: 'GE', label: '个', value: 'GE' },
  // { name: 'YUAN', label: '元', value: 'YUAN' },
  // { name: 'FEN', label: '分', value: 'FEN' },
  // { name: 'CUSTOM', label: '其他', value: 'CUSTOM' },
  { label: '无', value: '无', name: 'none' },
  { label: '家', value: '家', name: 'home' },
  { label: '个', value: '个', name: 'individual' },
  { label: '元', value: '元', name: 'element' },
  { label: '分', value: '分', name: 'branch' },
  { label: '其它', value: '其它', name: 'other' },
] as const);

/** 数值类型枚举 */
export const NumFormatTypeEnum = createEnumObject([
  {
    name: 'NUMBER',
    value: 'NUMBER',
    label: '数值',
  },
  {
    name: 'PERCENT',
    value: 'PERCENT',
    label: '百分比',
  },
] as const);

/** 使用千分符选项枚举 */
export const UseThousandthEnum = createEnumObject([
  {
    name: 'true',
    value: true,
    label: '是',
  },
  {
    name: 'false',
    value: false,
    label: '否',
  },
] as const);

/** 默认数据 */
export const NUM_FORMAT_DEFAULT_VALUE = {
  formatType: NumFormatTypeEnum.NUMBER,
  precision: 2,
};

/** 数字格式化表单区域 */
export const NumFormatFormSection: React.FC<{ name?: NamePath; labelCol?: ColProps }> = (props) => {
  const form = Form.useFormInstance();
  const formatType = Form.useWatch([].concat(props.name, 'formatType'));
  const unitValue = Form.useWatch([].concat(props.name, 'unitName'));

  return (
    <>
      <Form.Item
        labelCol={props.labelCol}
        label="数值类型"
        name={[].concat(props.name, 'formatType')}
        colon={false}
        rules={[{ required: true }]}
      >
        <Radio.Group
          options={formatEnumToOptions(NumFormatTypeEnum)}
          onChange={(evt) => {
            if (evt.target.value !== NumFormatTypeEnum.NUMBER) {
              const formData = form.getFieldValue(props.name);
              formData.unitName = UnitEnum.none;
              formData.precision = 2;
              form.setFieldValue(props.name, formData);
            }
          }}
        />
      </Form.Item>

      <Form.Item
        labelCol={props.labelCol}
        label="数据精度"
        name={[].concat(props.name, 'precision')}
        colon={false}
        rules={[{ required: true }]}
      >
        <InputNumber step={1} min={0} />
      </Form.Item>
    </>
  );
};

NumFormatFormSection.defaultProps = {
  name: [],
};
