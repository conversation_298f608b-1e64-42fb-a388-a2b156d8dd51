import { FoldableSection } from '@/common/components/FoldableSection';
import { NumFormatFormSection, NUM_FORMAT_DEFAULT_VALUE } from '@/common/components/NumFormatForm';
import {
  IndicatorItemType,
  RelatedIndicator,
  RelatedIndicatorRef,
} from '@/common/components/RelatedIndicator';
import { Button, ColProps, Drawer, Form, Input, message, Radio, Row, Select, Space } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { AssessResultMap, tracertInfo } from '@/common/Types';
import TextArea from 'antd/lib/input/TextArea';
import { handleRuleDataToForm, handleRuleFormToData, regularCheck } from '@/common/utils';
import { pick, set } from 'lodash';
import JSCookie from 'js-cookie';
import { DomainCalendarType } from '@/common/components/formula/constant';
import { useSetState } from 'ahooks';
import AutoGenerateFormula from '../FormulaAutoGen';
import { FormulaWithSearch } from '../FormulaWithSearch';
import Matrix from '../matrix';
import RuleGroup from '../rule-group';
import { COST_LIST } from './constant';
import './index.less';

export const INDICATOR_FORM_DEFAULT_VALUE = {
  name: null,
  itemCode: null,
  onlySelfIndicator: false,
  ...NUM_FORMAT_DEFAULT_VALUE,
  extraIndicators: null,
  description: null,
  ruleGroup: null,
};
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 15 },
};

const matrixLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 16 },
};
const sectionLayout = {
  labelCol: { span: 3 },
};

const LABEL_COL: ColProps = { span: 3 };
const title = {
  appraisalBlock: '考核模块配置',
  appraisalItem: '考核项配置',
};

interface IndicatorFormModalProps {
  drawerType: 'appraisalBlock' | 'appraisalItem';
  open: boolean;
  formData?: any;
  onConfirm?: (newFormData: IndicatorItemType) => void;
  onCancel?: () => void;
  options: any[];
  business?: any;
  selectProduct?: any;
  tracertInfo: tracertInfo; // 埋点基本数据
  businessGroup: string;
  role: string;
}

export const AppraisalFormModal: React.FC<IndicatorFormModalProps> = (props) => {
  const {
    drawerType = 'appraisalItem',
    options = [],
    selectProduct = null,
    business = [],
    tracertInfo,
    businessGroup,
  } = props;

  const [resultFormulaType, setResultFormulaType] = useState('公式');
  const [state, setState] = useSetState({
    itemOptions: [],
    itemData: null,
    isRemoveState: false,
  });
  const [form] = Form.useForm();
  const showTypeCur = Form.useWatch(['ruleGroup', 'showType'], form);
  const expressionType = Form.useWatch(['resultFormat', 'expressionType'], form);
  const relatedIndicatorRef = useRef<RelatedIndicatorRef>();

  // 考核项枚举值
  const itemCodeList = useMemo(() => {
    if (businessGroup === 'C33_DIRECT') {
      // C33 直营
      return COST_LIST;
    } else {
      // 其他
      return [
        {
          label: '年费佣金',
          value: 'SALES',
        },
        {
          label: '广告佣金',
          value: 'ADS',
        },
        {
          label: 'CPS佣金',
          value: 'CPS',
        },
      ];
    }
  }, [businessGroup]);

  const formulaTypeOnChange = (val: 'auto' | 'custom' | 'single') => {
    form.setFieldsValue({
      resultFormat: {
        expressionType: val === 'custom' || val === 'single' ? 0 : 1,
      },
    });
  };
  useEffect(() => {
    form.setFieldsValue(INDICATOR_FORM_DEFAULT_VALUE);
  }, []);

  useEffect(() => {
    if (showTypeCur) {
      const curRule = AssessResultMap[selectProduct]?.filter((it) => it.value === showTypeCur)?.[0];
      setResultFormulaType(curRule?.text || '');
    }
  }, [showTypeCur]);

  useEffect(() => {
    if (props.formData && props.open) {
      console.log('%c Line:92 🥤 props.formData', 'color:#fca650', props.formData);
      if (props.formData?.modules?.length) {
        setState({
          itemOptions: props.formData?.modules?.map((it, idx) => ({
            configType: 'NUMBERIC',
            label: it?.name || `未命名考核项${idx + 1}`,
            type: 'CheckItem',
            value: it?.id || '',
            weight: it?.ruleGroup?.weight?.textExpression || '',
          })),
          itemData: props.formData?.modules?.map((it, idx) => ({
            type: it?.moduleType,
            id: it?.id ? `$${it?.id}` : '',
            name: it?.name || `未命名考核项${idx + 1}`,
            weight: it?.ruleGroup?.weight?.textExpression || '',
            configType: it?.formatType === 'NUMBER' ? 'NUMBERIC' : it?.formatType,
          })),
        });
      } else {
        setState({ itemOptions: [], itemData: null });
      }
      form.setFieldsValue({
        ...pick(props.formData, [
          'name',
          'itemCode',
          'id',
          'sortNum',
          'level',
          'moduleType',
          'resultFormat',
          'extraIndicators',
          'description',
          'onlySelfIndicator',
        ]),
        ruleGroup: handleRuleDataToForm(props.formData.ruleGroup),
      });
    } else {
      form.setFieldsValue(INDICATOR_FORM_DEFAULT_VALUE);
    }
  }, [JSON.stringify(props.formData), props.open]);

  const footer = (
    <Row justify="end">
      <Space>
        <Button disabled={false} onClick={props.onCancel}>
          取消
        </Button>
        <Button
          type="primary"
          onClick={() =>
            form
              .validateFields()
              .then(() => {
                const newFormData = form.getFieldsValue();
                props.onConfirm({
                  ...newFormData,
                  ruleGroup: handleRuleFormToData(newFormData.ruleGroup, props.options),
                });
              })
              .catch((e) => {
                console.log('onFinishFailed', e);
                message.error({ content: '请检查表单校验报错' });
              })
          }
        >
          确认
        </Button>
      </Space>
    </Row>
  );
  return (
    <Drawer
      open={props.open}
      title={title[drawerType]}
      keyboard={false}
      closable={false}
      maskClosable={false}
      destroyOnClose
      width={1184}
      // style={{ top: 25 }}
      footer={footer}
      onOk={() => props.onConfirm(form.getFieldsValue())}
      onClose={props.onCancel}
      className="appraisal-module"
    >
      <Form
        form={form}
        onValuesChange={(val) => {
          console.log(val);
        }}
        labelCol={LABEL_COL}
        colon={false}
      >
        <Form.Item
          name="name"
          label={drawerType !== 'appraisalItem' ? '考核模块名称' : '考核项名称'}
          rules={[{ required: true }]}
        >
          <Input allowClear style={{ width: 328 }} />
        </Form.Item>

        <Form.Item
          name="itemCode"
          label="费用项"
          extra={
            businessGroup !== 'C33_DIRECT' && (
              <div style={{ paddingTop: 10 }}>
                <div>
                  1.「考核模块」和「考核项」，若都选择了费用项，则两个地方的费用项一般需保持一致。
                </div>
                <div>2. 若两个地方费用项不一致，以「考核模块」选项为准.</div>
                <div>3.「考核模块」的费用项未填写，则以「考核项」选项为准</div>
              </div>
            )
          }
        >
          <Select style={{ width: 328 }} options={itemCodeList} allowClear placeholder="请选择" />
        </Form.Item>
        {drawerType === 'appraisalBlock' && props.role === 'direct' && (
          <Form.Item name="onlySelfIndicator" label="只查询本人指标">
            <Select
              style={{ width: 328 }}
              options={[
                { label: '是', value: true },
                { label: '否', value: false },
              ]}
              allowClear
              placeholder="请选择"
            />
          </Form.Item>
        )}

        <Form.Item name="id" label="id" rules={[{ required: true }]} hidden>
          <Input allowClear style={{ width: 328 }} />
        </Form.Item>
        <Form.Item name="sortNum" label="排序编号" rules={[{ required: true }]} hidden>
          <Input allowClear style={{ width: 328 }} />
        </Form.Item>
        {/* {drawerType === 'appraisalBlock' && */}
        <Form.Item name={['resultFormat', 'expressionType']} hidden>
          <Radio.Group>
            <Radio value={0}>自定义</Radio>
            <Radio value={1}>自动生成</Radio>
          </Radio.Group>
        </Form.Item>
        {/* } */}
        <Form.Item name="level" label="层级" hidden>
          <Input allowClear style={{ width: 328 }} />
        </Form.Item>
        <Form.Item name={['ruleGroup', 'showType']} label="计算方式">
          <Radio.Group>
            {AssessResultMap[selectProduct]?.map((it) => (
              <Radio value={it.value} key={`show-type-${it.key}`}>
                {it.text}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(o, n) => o?.ruleGroup?.showType !== n?.ruleGroup?.showType}
        >
          {() => {
            if (showTypeCur === 'EXPRESSION') {
              return (
                <>
                  <Form.Item
                    label="考核结果"
                    name={['expressionCheck']} // 这里的name用来执行validator
                    wrapperCol={{ span: 20 }}
                    rules={[
                      {
                        required: true,
                        validator: (_, value) =>
                          regularCheck([...state.itemOptions, ...options], value),
                      },
                    ]}
                    style={{ marginBottom: 0 }}
                    className="wrap_result"
                  >
                    <AutoGenerateFormula
                      options={[...state.itemOptions, ...options]}
                      formName={['ruleGroup', 'expression', 'expression']}
                      moduleData={state.itemData}
                      usedPosition="SecAppraisal"
                      disabled={expressionType === 1}
                      resultType={expressionType === 0 ? 'auto' : 'custom'}
                      tracertInfo={tracertInfo}
                      curComeponetName={`${title[drawerType]}/${resultFormulaType}/考核结果`}
                      formulaTypeOnChange={formulaTypeOnChange}
                      productDomain={selectProduct}
                      businessLevel={business}
                      periodTypeWatch={props?.periodTypeWatch}
                      periodValueWatch={props?.periodValueWatch}
                      periodValueMultiWatch={props?.periodValueMultiWatch}
                      businessGroup={businessGroup}
                      calendarMode={props?.calendarMode}
                    />
                  </Form.Item>
                </>
              );
            } else if (showTypeCur === 'ARRAY') {
              return (
                <>
                  <Form.Item
                    name={['ruleGroup', 'arrayExpressionObj', 'lowLimit']}
                    label="下限"
                    {...layout}
                    rules={[
                      {
                        validator: (_, value) => regularCheck(options, value),
                      },
                    ]}
                  >
                    <FormulaWithSearch
                      options={[...state.itemOptions, ...options]}
                      tracertInfo={tracertInfo}
                      name={`${title[drawerType]}/${resultFormulaType}/下限`}
                      periodTypeWatch={props?.periodTypeWatch}
                      periodValueWatch={props?.periodValueWatch}
                      periodValueMultiWatch={props?.periodValueMultiWatch}
                      businessGroup={businessGroup}
                      calendarMode={props?.calendarMode}
                    />
                  </Form.Item>
                  <Form.Item
                    name={['ruleGroup', 'arrayExpressionObj', 'upperLimit']}
                    label="上限"
                    {...layout}
                    rules={[
                      {
                        validator: (_, value) => regularCheck(options, value),
                      },
                    ]}
                  >
                    <FormulaWithSearch
                      options={[...state.itemOptions, ...options]}
                      tracertInfo={tracertInfo}
                      name={`${title[drawerType]}/${resultFormulaType}/上限`}
                      periodTypeWatch={props?.periodTypeWatch}
                      periodValueWatch={props?.periodValueWatch}
                      periodValueMultiWatch={props?.periodValueMultiWatch}
                      businessGroup={businessGroup}
                      calendarMode={props?.calendarMode}
                    />
                  </Form.Item>
                  <Form.Item label=" " {...props} colon={false} {...matrixLayout}>
                    <Matrix
                      formName={['ruleGroup', 'arrayExpressionObj', 'arrayExpressionObj']}
                      defaultData={form.getFieldValue([
                        'ruleGroup',
                        'arrayExpressionObj',
                        'arrayExpressionObj',
                      ])}
                      options={[...state.itemOptions, ...options]}
                      form={form}
                      tracertInfo={tracertInfo}
                      tracertname={`${title[drawerType]}/${resultFormulaType}`}
                      removeInvalidFormData={(newMatrixData) => {
                        const formName = ['ruleGroup', 'arrayExpressionObj', 'arrayExpressionObj'];
                        const formData = form.getFieldsValue();
                        set(formData, formName, newMatrixData);
                        console.log('Matrix formData', formData);

                        form.setFieldsValue(formData);
                      }}
                      periodTypeWatch={props?.periodTypeWatch}
                      periodValueWatch={props?.periodValueWatch}
                      periodValueMultiWatch={props?.periodValueMultiWatch}
                      businessGroup={businessGroup}
                      calendarMode={props?.calendarMode}
                    />
                  </Form.Item>
                </>
              );
            } else if (showTypeCur === 'RULES') {
              return (
                <>
                  <Form.Item {...props} label=" " colon={false} {...matrixLayout}>
                    <RuleGroup
                      formName={['ruleGroup', 'indicatorRules']}
                      options={[...state.itemOptions, ...options]}
                      defaultFormData={[
                        {
                          resultExpressionType: 'EXPRESSION',
                        },
                      ]}
                      form={form}
                      tracertInfo={tracertInfo}
                      tracertname={`${title[drawerType]}/${resultFormulaType}`}
                      periodTypeWatch={props?.periodTypeWatch}
                      periodValueWatch={props?.periodValueWatch}
                      periodValueMultiWatch={props?.periodValueMultiWatch}
                      businessGroup={businessGroup}
                      calendarMode={props?.calendarMode}
                    />
                  </Form.Item>
                </>
              );
            }

            return null;
          }}
        </Form.Item>
        <Form.Item label={<i />}>
          <FoldableSection>
            {showTypeCur === 'EXPRESSION' && (
              <>
                <Form.Item
                  name={['ruleGroup', 'expression', 'lowLimit']}
                  label="下限"
                  rules={[
                    {
                      validator: (_, value) => regularCheck(options, value),
                    },
                  ]}
                  {...sectionLayout}
                >
                  <FormulaWithSearch
                    options={[...state.itemOptions, ...options]}
                    tracertInfo={tracertInfo}
                    name={`${title[drawerType]}/${resultFormulaType}/下限`}
                    periodTypeWatch={props?.periodTypeWatch}
                    periodValueWatch={props?.periodValueWatch}
                    periodValueMultiWatch={props?.periodValueMultiWatch}
                    businessGroup={businessGroup}
                    calendarMode={props?.calendarMode}
                  />
                </Form.Item>
                <Form.Item
                  name={['ruleGroup', 'expression', 'upperLimit']}
                  label="上限"
                  rules={[
                    {
                      validator: (_, value) => regularCheck(options, value),
                    },
                  ]}
                  {...sectionLayout}
                >
                  <FormulaWithSearch
                    options={[...state.itemOptions, ...options]}
                    tracertInfo={tracertInfo}
                    name={`${title[drawerType]}/${resultFormulaType}/上限`}
                    periodTypeWatch={props?.periodTypeWatch}
                    periodValueWatch={props?.periodValueWatch}
                    periodValueMultiWatch={props?.periodValueMultiWatch}
                    businessGroup={businessGroup}
                    calendarMode={props?.calendarMode}
                  />
                </Form.Item>
              </>
            )}

            <NumFormatFormSection name="resultFormat" {...sectionLayout} />
          </FoldableSection>
        </Form.Item>

        {drawerType === 'appraisalItem' && (
          <Form.Item
            name="extraIndicators"
            label="关联指标"
            rules={[
              {
                validator: (_, value) => {
                  if (value?.length > 0 && value.some((v) => !v.indicatorId)) {
                    return Promise.reject(new Error('请选择指标'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <RelatedIndicator
              ref={relatedIndicatorRef}
              isRemoveState={state.isRemoveState}
              indicatorList={options}
              showDatasetButton
              business={business}
              businessGroup={props.businessGroup}
              productDomain={selectProduct}
            />
          </Form.Item>
        )}

        <Form.Item name={['description']} label="说明">
          <TextArea
            showCount
            maxLength={400}
            style={{ height: 120, width: 552 }}
            // onChange={onChange}
            placeholder="请输入内容"
          />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
