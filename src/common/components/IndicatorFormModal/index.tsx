import React, { useEffect } from 'react';
import { FoldableSection } from '@/common/components/FoldableSection';
import { NumFormatFormSection, NUM_FORMAT_DEFAULT_VALUE } from '@/common/components/NumFormatForm';
import { IndicatorItemType, RelatedIndicatorProps } from '@/common/components/RelatedIndicator';
import { Form, Modal, Select } from 'antd';

export const INDICATOR_FORM_DEFAULT_VALUE = {
  expression: null,
  indicatorRename: '',
  resultFormat: NUM_FORMAT_DEFAULT_VALUE,
};

const LABEL_COL = { span: 3 };

interface IndicatorFormModalProps {
  open: boolean;
  indicatorList: RelatedIndicatorProps['indicatorList'];
  formData?: IndicatorItemType;
  /** 产品域，控制图表类型枚举 */
  productDomain?: string;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onConfirm?: (newFormData: IndicatorItemType) => void;
  onCancel?: () => void;
}

export const IndicatorFormModal: React.FC<IndicatorFormModalProps> = (props) => {
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue(INDICATOR_FORM_DEFAULT_VALUE);
  }, []);

  useEffect(() => {
    if (props.formData) {
      form.setFieldsValue({
        ...(props.formData || {}),
        expression: props.formData?.indicatorId?.replace('@', '') || undefined,
        isKey: `${props.formData?.isKey}`,
      });
    } else {
      form.setFieldsValue(INDICATOR_FORM_DEFAULT_VALUE);
    }
    // if (!props.open) {
    //   setState({ isMoreFold: true });
    // }
  }, [props.open]);

  return (
    <Modal
      open={props.open}
      title="指标配置"
      width={905}
      style={{ top: 25 }}
      keyboard={false}
      maskClosable={false}
      onOk={() => {
        form.validateFields()
          .then(() => props.onConfirm(form.getFieldsValue()));
      }}
      onCancel={props.onCancel}
    >
      <Form
        form={form}
        labelCol={LABEL_COL}
        colon={false}
      >
        <Form.Item
          name="expression"
          label="指标"
          rules={[{ required: true }]}
        >
          <Select
            showSearch
            style={{ maxWidth: 712 }}
            className="u-flex-1"
            placeholder="请选择指标"
            options={props.indicatorList}
            filterOption={(input, option) =>
              `${option?.label ?? ''}`.toLowerCase().includes(input.toLowerCase())
            }
            onChange={(value) => {
              const indicator = props.indicatorList.find(o => o.value === value);
              console.log('indicator', indicator);
            }}
          />
        </Form.Item>

        <Form.Item label={<i />}>
          <FoldableSection>
            <NumFormatFormSection labelCol={LABEL_COL} name="resultFormat" />
          </FoldableSection>
        </Form.Item>

        {/* <Form.Item name="rename" label="指标重命名">
          <Input placeholder="请输入" allowClear style={{ width: 328 }} />
        </Form.Item> */}
      </Form>
    </Modal>
  );
};
