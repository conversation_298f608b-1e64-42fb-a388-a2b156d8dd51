import React, { useState } from 'react';
import { DatePicker, Button, message } from 'antd';
import moment, { Moment } from 'moment';
import { ActionTypeEnum } from '@/common/Types';
import './index.less';

interface ModuleVersionSelectorProps {
  editType: string;
  value?: Moment | null;
  versionList: string[];
  onChange?: (date: Moment | null, dateString: string) => void;
  onVersionListChange?: (list: string[]) => void;
  onVersionClick?: (version: string) => void;
  disabled?: boolean;
  disableAddVersion?: boolean; 
}

const ModuleVersionSelector: React.FC<ModuleVersionSelectorProps> = ({
  editType,
  value,
  versionList,
  onChange,
  onVersionListChange,
  onVersionClick,
  disabled,
  disableAddVersion,
}) => {
  const [showVersionPicker, setShowVersionPicker] = useState(false);

  // 从value计算当前选中的版本字符串
  const selectedVersion = value && moment.isMoment(value) ? value.format('YYYY-MM') : '';

  // 禁用已创建的版本日期
  const disabledDate = (current: Moment) => {
    if (!current) return false;
    const currentVersionString = current.format('YYYY-MM');
    return versionList.includes(currentVersionString);
  };

  // 选择新版本
  const handleVersionSelect = (date: Moment | null, dateString: string) => {
    if (dateString && date) {
      if (versionList.includes(dateString)) {
        message.warning(`版本 ${dateString} 已存在，请选择其他版本`);
        return;
      }
      // 添加到版本列表
      if (onVersionListChange) {
        onVersionListChange([...versionList, dateString]);
      }
      setShowVersionPicker(false);
      if (onChange) {
        onChange(date, dateString);
      }
      // message.success(`版本 ${dateString} 添加成功`);
    }
  };

  // 点击已存在版本进行选择
  const handleVersionClick = (version: string) => {
    if (editType === ActionTypeEnum.SEE) return;
    
    if (onVersionClick) {
      onVersionClick(version);
    } else {
      // 默认的版本切换逻辑
      const momentVersion = moment(version, 'YYYY-MM');
      if (onChange) {
        onChange(momentVersion, version);
      }
      message.success(`已选择版本 ${version}`);
    }
  };

  const isAdd = editType === ActionTypeEnum.ADD;
  const isView = editType === ActionTypeEnum.SEE;
  const safeCurrentValue = value && moment.isMoment(value) ? value : null;

  if (isAdd) {
    // 新增时直接显示月份选择组件
    return (
      <DatePicker
        picker="month"
        placeholder="请选择版本"
        format="YYYY-MM"
        value={safeCurrentValue}
        onChange={handleVersionSelect}
        disabled={isView || disabled}
        disabledDate={disabledDate}
        style={{ width: '200px' }}
      />
    );
  }

  // 修改时显示版本列表和添加版本功能
  return (
    <div className="version-picker-container">
      {/* 版本列表 */}
      {versionList.map((version, index) => (
        <div
          key={index}
          className={`version-item ${selectedVersion === version ? 'version-item-selected' : ''} ${isView ? 'version-item-disabled' : ''}`}
          onClick={() => handleVersionClick(version)}
          style={{ cursor: isView ? 'default' : 'pointer' }}
        >
          {version}
        </div>
      ))}
      {/* 添加版本交互 */}
      {showVersionPicker ? (
        <div>
          <DatePicker
            picker="month"
            placeholder="请选择新版本"
            format="YYYY-MM"
            onChange={handleVersionSelect}
            autoFocus
            disabled={isView || disabled}
            disabledDate={disabledDate}
            style={{ width: '200px' }}
          />
        </div>
      ) : (
        <Button
          type="dashed"
          onClick={() => setShowVersionPicker(true)}
          disabled={isView || disabled || disableAddVersion}
        >
          + 添加版本
        </Button>
      )}
    </div>
  );
};

export default ModuleVersionSelector;
