import React from 'react';
import { Result, Spin } from 'antd';
import 'antd/es/spin/style';
import classNames from 'classnames';
import AESPluginEvent from '@ali/aes-tracker-plugin-event';
import './style.less';

/** 全局默认错误标题 */
let globalDefaultErrorTitle: React.ReactNode = '这里出现了些问题';
/** 全局默认错误信息 */
let globalDefaultErrorMessage: React.ReactNode = '请及时联系相关负责人进行沟通';
/** 全局默认错误子组件 */
let globalDefaultErrorComponent = null;
/** 全局默认样式 */
let globalDefaultClassName: string;

/** 更新错误拦截组件全局默认配置 */
export function updateGlobalConfig(config: {
  /** 错误标题 */
  errTitle?: React.ReactNode;
  /** 错误信息 */
  errMessage?: React.ReactNode;
  /** 错误组件子组件 */
  errCompnent?: React.ReactElement;
  /** 全局默认样式 */
  className?: string;
}) {
  if (config.errTitle !== undefined) globalDefaultErrorTitle = config.errTitle;
  if (config.errMessage !== undefined) globalDefaultErrorMessage = config.errMessage;
  if (config.errCompnent !== undefined) globalDefaultErrorComponent = config.errCompnent;
  if (config.className !== undefined) globalDefaultClassName = config.className;
}

enum IconEnum {
  Empty = 'https://gw.alicdn.com/imgextra/i3/O1CN01OkosXY1lKRu9r0k6Y_!!6000000004800-55-tps-320-320.svg',
  Error = 'https://gw.alicdn.com/imgextra/i4/O1CN01hnrYiE1D12Av7pU5H_!!6000000000155-55-tps-320-320.svg',
}

export class CommonBlockWrap extends React.Component<{
  className?: string;
  /** 尺寸 */
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
  /** 主题 */
  theme?: 'default';
  /** 空状态 */
  isEmpty?: boolean;
  /** 加载提示文本 */
  isLoading?: boolean;
  /** 加载提示文本 */
  loadingText?: string;
  /** 错误状态的标题 */
  errTitle?: string;
  /** 错误时的图标 */
  errIcon?: string;
  /** 错误状态的描述信息 */
  errDescription?: string;
  /** 错误组件 */
  errComponent?: React.ReactNode;
  /** 空状态的标题 */
  emptyTitle?: string;
  /** 空的时候图标 */
  emptyIcon?: string;
  /** 空状态的描述信息 */
  emptyDescription?: string;
}, {
  isError: boolean;
}> {
  constructor(props) {
    super(props);
    this.state = {
      isError: false,
    };
  }

  componentDidCatch(error, info) {
    console.log('[CommonBlockWrap] 组件出现错误', error, info);
    this.setState({
      isError: true,
    });
    if (AESPluginEvent && window.$ENV && 'production,ppe'.includes(window.$ENV)) {
      AESPluginEvent('componentErrorCatch', {
        c1: error.message,
        c2: info,
      });
    }
  }

  render() {
    const { isError } = this.state;
    const {
      isEmpty = false,
      isLoading = false,
      loadingText = '加载中',
      style = {
        minHeight: '2rem',
      },
      errIcon = IconEnum.Error,
      errTitle = globalDefaultErrorTitle,
      errDescription = globalDefaultErrorMessage,
      errComponent = globalDefaultErrorComponent,
      emptyIcon = IconEnum.Empty,
      emptyTitle = '暂无数据哦',
      emptyDescription = '',
      theme,
    } = this.props;
    return (
      <div className={classNames('common-block-wrap', { [`common-block-wrap__error-${theme}`]: theme && isError }, globalDefaultClassName, this.props.size, this.props.className)}>
        <Spin
          spinning={isLoading}
          tip={loadingText}
        >
          <div className="common-block-wrap__content" style={style}>
            {isError ? (
              <Result
                icon={<img src={errIcon} alt="" />}
                status="error"
                title={errTitle}
                extra={errComponent}
                subTitle={errDescription}
              />
            ) : (
              isEmpty ? (
                <Result
                  icon={<img src={emptyIcon} alt="" />}
                  status="info"
                  title={emptyTitle}
                  subTitle={emptyDescription}
                />
              ) : (
                this.props.children
              )
            )}
          </div>
        </Spin>
      </div>
    );
  }
}
