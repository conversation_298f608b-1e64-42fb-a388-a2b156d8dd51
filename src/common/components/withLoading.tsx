import React, { ComponentType, FC, ReactElement, useEffect, useRef } from 'react';
import { useRequest } from 'ahooks';
import { Button, Result, Skeleton } from 'antd';
import { extractServerErrorMessage } from '../utils';
import styles from './withLoading.module.less';

export interface WithLoadingProps<TData> {
  data: TData;
  refresh: () => any;
}

export interface WithLoadingExtraProps {
  onLoadingChange?: (loading: boolean) => any;
}

type WithoutLoadingProps<TProps extends WithLoadingProps<any>> = Omit<TProps, keyof WithLoadingProps<any>>;

export interface WithLoadingOptions<TProps> {
  renderError?: (error: any, props: TProps, refresh: () => any) => ReactElement;
  renderLoading?: () => ReactElement;
}

export const withLoading = <TData, TProps extends WithLoadingProps<TData>>(
  Component: ComponentType<TProps>,
  request: (props: WithoutLoadingProps<TProps>) => TData | Promise<TData>,
  options?: WithLoadingOptions<WithoutLoadingProps<TProps>>,
) => {
  const ResultComponent: FC<WithoutLoadingProps<TProps> & WithLoadingExtraProps> = (props) => {
    const {
      onLoadingChange,
      ...componentProps
    } = props;
    const {
      data,
      loading,
      error,
      run,
    } = useRequest(async () => await request(props));

    const { renderError, renderLoading } = options || {};

    const latestOnLoadingRef = useRef(onLoadingChange);
    latestOnLoadingRef.current = onLoadingChange;

    useEffect(() => {
      latestOnLoadingRef.current?.(loading);
    }, [loading]);

    if (loading) {
      return typeof renderLoading === 'function' ? (
        renderLoading()
      ) : (
        <div className={styles.loadingWrapper}>
          <Skeleton active />
        </div>
      );
    }

    if (error) {
      return typeof renderError === 'function' ? (
        renderError(error, props, run)
      ) : (
        <div className={styles.errorWrapper}>
          <Result
            title="出错了"
            subTitle={extractServerErrorMessage(error)}
            extra={((error as any)?.canRetry ?? true) && (
              <Button onClick={run}>
                重试
              </Button>
            )}
          />
        </div>
      );
    }

    const resultProps: TProps = {
      ...componentProps as TProps,
      data,
      refresh: run,
    };
    return (
      <Component {...resultProps} />
    );
  };

  ResultComponent.displayName = `withLoading(${
    Component.displayName || Component.name
  })`;

  return ResultComponent;
};
