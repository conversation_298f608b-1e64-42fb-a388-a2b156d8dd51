import React from 'react';
import type { NamePath } from 'antd/es/form/interface';
import { Form, Select, Typography } from 'antd';
import { Condition } from '../condition';
import type { OptionItem } from '../formula';
import styles from './index.module.less';

interface IProps {
  formName: NamePath;
  conditionRelationshipFormName: NamePath;
  options: OptionItem[];
  tracertname: string;
  [x: string]: any;
}

export const enum ConditionRelationships {
  And = 'AND',
  Or = 'OR',
}

export const ConditionGroup: React.FC<IProps> = (props) => (
  <div className={styles.wrapper} key={props.formName.toString()}>
    <div className={styles.title}>
      <Typography.Text type="secondary" className={styles.titleText}>
        条件组
      </Typography.Text>
      <Form.Item
        initialValue={ConditionRelationships.And}
        name={props.conditionRelationshipFormName}
        noStyle
      >
        <Select
          className={styles.select}
          options={[
            { value: ConditionRelationships.And, label: '且' },
            { value: ConditionRelationships.Or, label: '或' },
          ]}
        />
      </Form.Item>
    </div>

    <div className={styles.conditions}>
      <Condition
        tracertInfo={props?.tracertInfo}
        options={props.options}
        formName={[...props.formName]}
        tracertname={props?.tracertname}
        conditionRelationshipFormName={props.conditionRelationshipFormName}
        form={props.form}
        periodTypeWatch={props?.periodTypeWatch}
        periodValueWatch={props?.periodValueWatch}
        periodValueMultiWatch={props?.periodValueMultiWatch}
        businessGroup={props?.businessGroup}
        calendarMode={props?.calendarMode}
      />
    </div>
  </div>
);

export default ConditionGroup;
