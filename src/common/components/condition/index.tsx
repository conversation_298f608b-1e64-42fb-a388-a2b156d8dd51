import React from 'react';
import { ModalProps, Form, Select, Button, Row, Col } from 'antd';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { listNotEmptyValidator } from '@/common/utils';
import { FormulaWithSearch } from '../FormulaWithSearch';

interface IProps extends ModalProps {
  options: Array<{ label: string; value: string }>;
  formName?: any;
  callFun?: (data) => void;
  tracertname?: string;
  [x: string]: any;
}
/** 操作符列表 */
const OPERATORS = [
  { key: 'IN', value: 'in' },
  // { key: 'NOT_IN', value: 'not in' },
  { key: 'EQUAL', value: '=' },
  { key: 'NOT_EQUAL', value: '!=' },
  { key: 'GT', value: '>' },
  { key: 'GTEQ', value: '>=' },
  { key: 'LT', value: '<' },
  { key: 'LTEQ', value: '<=' },
  // { key: 'LIKE', value: 'like' },
]

export const Condition: React.FC<IProps> = (props: IProps) => (
  <Form.List
    name={props.formName || 'form-list-filter'}
    rules={[{ validator: listNotEmptyValidator }]}
  >
    {(fieldes, { add, remove }, { errors }) => (
      <>
        {fieldes.map(({ key, name, ...restField }) => (
          <Row key={key} gutter={[16, 16]} align="top" justify="end">
            <Col flex="1">
              <Form.Item
                key={1}
                {...restField}
                name={[name, 'leftExpression']}
                fieldKey={[name, 'leftExpression']}
                style={{ marginBottom: 5 }}
                rules={[{ required: true, message: '请选择指标' }]}
              >
                <FormulaWithSearch
                  key={`${key}-business`}
                  tracertInfo={props?.tracertInfo}
                  dropdownWidth={910}
                  options={props.options as any}
                  name={`${props?.tracertname}/左值`}
                  periodTypeWatch={props?.periodTypeWatch}
                  periodValueWatch={props?.periodValueWatch}
                  periodValueMultiWatch={props?.periodValueMultiWatch}
                  businessGroup={props?.businessGroup}
                  calendarMode={props?.calendarMode}
                />
              </Form.Item>
            </Col>

            <Col span={3}>
              <Form.Item
                key={2}
                {...restField}
                name={[name, 'expression']}
                style={{ marginBottom: 5 }}
                fieldKey={[name, 'expression']}
                rules={[{ required: true, message: '请选择运算符' }]}
              >
                <Select key={`${key}-expression`} placeholder="请选择">
                  {OPERATORS.map((item) => (
                    <Select.Option id={`${key}-expression`} key={item.key} value={item.key}>
                      {item.value}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col flex="1">
              <Form.Item
                key={3}
                {...restField}
                style={{ marginBottom: 5 }}
                name={[name, 'rightExpression']}
                fieldKey={[name, 'rightExpression']}
                rules={[{ required: true, message: '请输入值' }]}
              >
                <FormulaWithSearch
                  key={`${key}-business`}
                  tracertInfo={props?.tracertInfo}
                  dropdownWidth={910}
                  options={props.options as any}
                  name={`${props?.tracertname}/右值`}
                  periodTypeWatch={props?.periodTypeWatch}
                  periodValueWatch={props?.periodValueWatch}
                  periodValueMultiWatch={props?.periodValueMultiWatch}
                  businessGroup={props?.businessGroup}
                  calendarMode={props?.calendarMode}
                  supportText
                />
              </Form.Item>
            </Col>

            <Col span={1}>
              <DeleteOutlined
                className="delect-icon"
                onClick={() => remove(name)}
                style={{ marginTop: 8 }}
              />
            </Col>
          </Row>
        ))}

        <Form.ErrorList errors={errors} />

        <Form.Item key={4} style={{ marginBottom: 5 }}>
          <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
            添加筛选条件
          </Button>
        </Form.Item>
      </>
    )}
  </Form.List>
);

export default Condition;
