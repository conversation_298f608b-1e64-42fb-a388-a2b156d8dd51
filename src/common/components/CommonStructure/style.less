@color-bg: #f5f5f5;
@color-bg-card: #ffffff;
@size-middle: 20px;
@size-small: 20px;
@size-radius: 5px;

.common-structure {
  padding: 16px 24px;
  background-color: @color-bg;
  min-height: 100vh;

  &__header {
    display: flex;

    .header__title {
      font-size: 20px;
      margin-top: 0;
      color: rgba(0, 0, 0, 85%);
      font-weight: 500;
    }
  }

  &__section {
    background-color: @color-bg-card;
    padding: 16px 24px;
    margin-top: 16px;
    border-radius: @size-radius;
    overflow-x: hidden;

    &.is-folded {
      .section__header {
        margin-bottom: 0;
      }

      .section__body {
        height: 0;
        overflow: hidden;
      }
    }

    .section__header {
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;

      &__title {
        font-size: 16px;
        font-weight: bold;
        display: flex;
        align-items: center;
      }

      &__extra {
        margin-left: auto;
      }
    }
  }

  &__filter {
    position: relative;
    padding: 24px;
    padding-bottom: 0;

    .filter__button-group {
      display: flex;
      justify-content: flex-end;
      min-width: 200px;
      padding-right: 90px !important;
      margin-left: auto;

      button:not(:last-child) {
        margin-right: 10px;
      }
    }

    &--no-fold {
      .filter__button-group {
        padding-right: 0 !important;
      }
    }

    .filter__toggle-handler {
      position: absolute;
      right: @size-middle;
      top: @size-middle;
    }
  }
}
