import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useToggle } from 'ahooks';
import { Button } from 'antd';
import classNames from 'classnames';
import React, { useCallback, useState } from 'react';
import './style.less';

/** 页面框架 */
const StructureBase: React.FC<{ className?: string }> = (props) => (
  <div className={classNames('common-structure', props.className)}>
    {props.children}
  </div>
);


/** 页面框架头部部分 */
const StructureHeader: React.FC<{ title?: string; className?: string }> = (props) => (
  <div className={classNames('common-structure__header', props.className)}>
    {
      props.title
        ? <div className="header__title">{props.title}</div>
        : props.children
    }
  </div>
);

// eslint-disable-next-line @typescript-eslint/no-unused-vars
type FilterRenderFunction = (isFold: boolean, toggle: () => void) => React.ReactNode;

/** 页面框架头部过滤 */
const StructureSectionFilter: React.FC<{
  children: FilterRenderFunction;
  className?: string;
  // handlerStyle?: React.CSSProperties;
}> = (props) => {
  const [isFold, { toggle }] = useToggle(false);
  return (
    <div
      className={classNames('common-structure__filter common-structure__section', props.className, {
        'common-structure__filter--fold': isFold,
      })}
    >
      {props.children(isFold, toggle)}

      {/* <Button
        type="link"
        className="filter__toggle-handler"
        style={props.handlerStyle}
        onClick={() => toggle()}
      >
        {isFold ? <>展开 <DownOutlined /></> : <>折叠 <UpOutlined /></>}
      </Button> */}
    </div>);
};


/** 渲染函数 */
type RenderFuntion = () => React.ReactNode;

/** 页面框架主体中的部分 */
const StructureSection: React.FC<{
  title?: string | RenderFuntion | React.ReactNode;
  extra?: string | RenderFuntion | React.ReactNode;
  /** 显示是否折叠 */
  canFold?: boolean;
  className?: string;
  styles?: React.CSSProperties;
  id?: string;
}> = (props) => {
  const [isFolded, setIsFold] = useState(false);

  const renderTitle = useCallback(() => {
    if (props.title || props.extra) {
      return (
        <div className="section__header">
          {
            typeof props.title === 'function'
              ? props.title()
              : <div className="section__header__title">{props.title}</div>
          }
          {
            props.extra
              ? <div className="section__header__extra">{props.extra}</div>
              : null
          }
          {
            props.canFold
              ? (
                <Button
                  type="link"
                  className="u-pr-0 u-pl-24"
                  onClick={() => setIsFold(!isFolded)}
                >
                  {
                  isFolded
                    ? <span>展开 <DownOutlined /></span>
                    : <span>折叠 <UpOutlined /></span>
                }
                </Button>
              )
              : null
          }
        </div>);
    }
    return null;
  }, [props.title, props.extra, isFolded]);

  return (
    <div
      id={props.id}
      className={classNames('common-structure__section', props.className, { 'is-folded': isFolded })}
      style={props.styles}
    >
      { renderTitle() }

      <div className="section__body">
        {props.children}
      </div>
    </div>);
};

/** 页面框架注脚的部分 */
const StructureFooter: React.FC<{}> = (props) => (
  <div className="common-structure__footer">
    { props.children }
  </div>
);

export type StructureProps = typeof StructureBase & {
  Header: typeof StructureHeader;
  Filter: typeof StructureSectionFilter;
  Section: typeof StructureSection;
  Footer: typeof StructureFooter;
};

const CommonStructure = StructureBase as StructureProps;
CommonStructure.Header = StructureHeader;
CommonStructure.Filter = StructureSectionFilter;
CommonStructure.Section = StructureSection;
CommonStructure.Footer = StructureFooter;

export default CommonStructure;
