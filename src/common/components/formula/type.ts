import { tracertInfo } from '@/common/Types';
import { Moment } from 'moment';
import { ExpressionType, OptionItemTypeEnum } from './constant';

/** 空白对象 */
export interface PlainObject<T = any> {
  [key: string]: T;
}

export interface SelectionData {
  /** 光标所在节点 */
  anchorNode: Node;
  /** 光标所在节点位置 */
  anchorOffset: number;
}

export interface FormulaState {
  /** 下拉是否展示 */
  showSelection: boolean;
  /** 文本输入框是否展示 */
  showInputPopover: boolean;
  /** 下拉输入框 */
  searchText: string;
    /** 文本输入框 */
  inputText: string;
  /** 下拉远程搜索loading */
  searchLoading: boolean;
  /** 显示日历 */
  showCalendar: boolean;
  /** 光标的数据选择的数据 */
  selectionData: SelectionData;
  /** 指标选项 */
  options: OptionItem[];
  /** 指标有种类型 */
  isOptionMutliType: boolean;
  /** 当前的表达式字符串 */
  currentExpression: string;
}

/** 可选项类型 */
export interface OptionItem {
  /** 指标类型 */
  configType?: string;
  /** 指标标题 */
  label: string;
  /** 指标ID */
  value: string;
  /** 指标层级 */
  type: OptionItemTypeEnum | string;
  /** 指标类型 */
  indicatorType: string;
  /** 权重 */
  weight?: string;
  /** 标记是否是实时指标 */
  timeType?: string;
  classificationlabel?: string;
  classificationLabel?: string;
}

/** 表单数据结构 */
export interface FormulaValue {
  /**
   * 表达式
   * @example @1231231231_MONTH_202005_202012/#123123123123
   */
  expression: string;
  /** 表达式类型，包含日期指标则为TIME，其他为COMMON */
  type?: ExpressionType;
  /** 用文本替换ID后的表达式，通常用作文本表达式展示 */
  textExpression?: string;
  /**
   * 表达式中涉及的ID与文本映射关系
   * @notice key需要根据不同维度指标添加Prefix，指标为@，考核项为$，考核模块为#
   */
  varMap?: Record<string, string>;
}

export interface FormulaProps {
  /** 公式超宽换行 */
  wrap?: boolean;
  /** debug模式，输出日志 */
  formulaDebug?: boolean;
  /** 输入框禁用 */
  disabled?: boolean;
  /** 输入框占位符 */
  placeholder?: string;
  /** 公式容器类名 */
  formulaWrapClassName?: string;
  /** 公式容器样式 */
  formulaWrapStyle?: React.CSSProperties;
  /** 公式输入框类名 */
  formulaClassName?: string;
  /** 搜索框占位符 */
  searchInputPlaceholder?: string;
  /** 输入框尺寸 */
  size?: 'large' | 'small' | 'middle';
  /** 下拉宽度设置 */
  dropdownWidth?: number;
  /** 日历模式 */
  calendarMode?: 'month' | 'year' | 'relative';
  /** 指标选项 */
  options: OptionItem[];
  /** 已选择数据 */
  value?: FormulaValue;
  /** 数据变更操作 */
  onChange?: (v: FormulaValue) => void;
  /** 格式化组件数据到表单提交数据 */
  buildDataToForm?: (data: FormulaDataItem[], debug: boolean) => FormulaValue;
  /** 表单提交数据解析成组件数据 */
  parseFormToData?: (
    formula: FormulaValue,
    options: OptionItem[],
    debug: boolean,
  ) => FormulaDataItem[];
  /** 公式改变触发副作用 */
  onChangeSideEffect?: (formula: FormulaValue) => void;
  tracertInfo?: tracertInfo;
  /** 当前组件名称 */
  name?: string;
  /** 组件类名 */
  className?: string;
  /** 搜索远程结果 */
  searchRemoteFunc?: (searchText: string) => Promise<OptionItem[]>;
  periodTypeWatch?: string; // 绩效周期类型 MONTH MULTI_MONTH
  periodValueWatch?: string; // MONTH 单月的值
  periodValueMultiWatch?: string[]; // MULTI_MONTH 多月的区间
  businessGroup?: 'C33_DIRECT' | 'C33_AGENT';
  defaultDate?: string; // 默认的指标时间
  supportText?: boolean; // 是否支持通过！唤起文本输入框，默认false
}

/** 公式内部元素解析类型 */
export interface FormulaDataItem extends Partial<OptionItem> {
  label: string;
  /** 指标绑定日期 */
  date?: string;
}
