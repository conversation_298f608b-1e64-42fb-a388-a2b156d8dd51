@theme-color: #1a66ff;
@input-border: 1px solid #dcdfe6;
@disable: #9097a3;

.formula-wrapper {
  position: relative;

  > .formula-input {
    border-radius: 2px;
    border: 1px solid #dcdfe6;
    white-space: nowrap;
    padding: 4px;
    outline: none;
    font-size: 14px;
    transition: border 0.3s;
    overflow: scroll;
    -ms-overflow-style: none;
    display: flex;
    align-items: center;
    color: @theme-color;
    background-color: white;

    &.wrap {
      flex-wrap: wrap;
    }

    &.large {
      padding: 6.5px 11px;
      font-size: 16px;
    }

    &.small {
      padding: 0 7px;
      font-size: 14px;
    }

    &.show-type {
      i.type {
        display: inline;
      }
    }

    &::-webkit-scrollbar {
      display: none;
    }

    &[contenteditable='false'] {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
      cursor: not-allowed;
    }

    &.error,
    &:focus,
    &.focused {
      border: 1px solid @theme-color;
    }

    &:empty::before {
      content: attr(placeholder);
      color: #c0c4cc;
    }

    br {
      display: none;
    }

    span {
      padding: 0 2px;
      height: 22px;
      line-height: 22px;
    }

    // > span:last-child {
    //   flex: 1;
    // }

    div {
      padding: 0 5px;
      border-radius: 2px;
      background: #f5f5f5;
      color: #333 !important;
      margin: 0 3px;
      border: 1px solid #eee;

      p {
        margin-bottom: 0;
        line-height: 20px;
        font-size: 14px;
      }

      span,
      em {
        display: none;
      }

      i {
        color: #8c8c8c;
        font-size: 12px;
        line-height: 17px;
        font-style: normal;

        &:not(.date) {
          border: 1px solid #ccc;
          padding: 0 3px;
          border-radius: 2px;
        }

        &.type {
          display: none;
        }
      }
    }
  }
}

.formula-popover {
  display: flex;
  width: 800px;
  box-sizing: border-box;

  .popover__left {
    flex: 1;
    width: 10px;

    .ant-list {
      flex: 1;

      .ant-list-header {
        padding-top: 5px;
      }

      .ant-list-items {
        max-height: 270px;
        overflow: auto;
        margin-bottom: 10px;
      }

      .ant-list-item {
        // padding: 8px 0;
        cursor: pointer;
        display: flex;
        justify-content: flex-start;

        .ant-tag-processing {
          width: 50px;
          color: #1890ff;
          background: #e6f7ff;
          border-color: #91d5ff;
          margin-left: 3px;
        }

        .ant-btn {
          margin: -10px;
          margin-left: auto;
          background: transparent;
          border: none;
          display: none;

          &:hover {
            background: rgba(0, 0, 0, 5%);
          }
        }
        .datepicker-wrapper {
          display: none;
          margin-left: auto;
        }

        &.active {
          background-color: #fff2f0 !important;
        }

        &.disabled {
          color: @disable;
          padding-left: 0;
          font-size: 12px;

          &:hover {
            background: white !important;
          }
        }

        &:hover {
          background-color: #f5f5f5;
        }

        &:hover .ant-btn,
        &.active .ant-btn {
          display: inline-block;
        }
        &:hover .datepicker-wrapper {
          display: block;
        }
      }
    }

    .calendar-tips {
      padding: 8px 16px 0 16px;
      color: #cecece;
      .anticon {
        margin-right: 6px;
      }
    }
  }

  .popover__right {
    display: flex;
    justify-content: flex-end;
    overflow: hidden;
    transition: all 0.15s ease-out;
    flex-direction: column;

    .right__bottom {
      display: flex;
      justify-content: flex-end;
    }

    .ant-btn:last-child {
      margin-left: 16px;
    }
  }

  .ant-picker-calendar {
    width: 280px;
  }

  .rc-virtual-list-holder {
    > div {
      min-height: 300px;
    }
  }
}
