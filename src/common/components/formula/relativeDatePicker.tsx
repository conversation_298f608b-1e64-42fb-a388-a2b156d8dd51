import React, { useState, useCallback } from "react";
import { Popover } from "antd";
import {
    RELATIVE_DATE_OPTIONS,
} from './constant';
import './relativeDatePicker.less';

// 定义时间范围值的数据结构
interface RelativeDateValue {
  start?: string;
  end?: string;
}

// 定义组件 Props 接口
interface RelativeDatePickerProps {
  value?: RelativeDateValue;
  onChange?: (value: RelativeDateValue) => void;
}

// 选择步骤类型
type SelectStep = 'start' | 'end' | 'complete';

const RelativeDatePicker: React.FC<RelativeDatePickerProps> = ({ 
  value, 
  onChange 
}) => {
  // 内部状态管理
  const [internalValue, setInternalValue] = useState<RelativeDateValue>({
    start: undefined,
    end: undefined
  });
  
  // 面板显示状态
  const [showPanel, setShowPanel] = useState<boolean>(false);
  
  // 选择步骤状态
  const [selectStep, setSelectStep] = useState<SelectStep>('start');

  // 判断是否为受控组件
  const isControlled = value !== undefined;
  const currentValue = isControlled ? value : internalValue;

  // 显示当前选择的时间范围文本
  const displayText = useCallback(() => {
    const { start, end } = currentValue;
    if (start && end) {
      return `${start} ～ ${end}`;
    } else if (start) {
      return `${start} ～ `;
    } else {
      return '请选择时间范围';
    }
  }, [currentValue]);

  // 处理显示区域的点击事件
  const handleDisplayClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setShowPanel(true);
    setSelectStep('start');
  }, []);

  // 根据选项值获取在数组中的索引位置
  const getOptionIndex = useCallback((optionValue: string): number => {
    return RELATIVE_DATE_OPTIONS.findIndex(option => option.value === optionValue);
  }, []);

  // 根据已选值动态生成禁用选项的数组
  const generateOptions = useCallback((type: 'start' | 'end') => {
    const { start, end } = currentValue;
    
    return RELATIVE_DATE_OPTIONS.map(option => {
      let disabled = false;
      let selected = false;
      
      if (type === 'start') {
        selected = start === option.value;
        if (end) {
          // 开始时间不能晚于结束时间（索引不能小于结束时间索引）
          const endIndex = getOptionIndex(end);
          const currentIndex = getOptionIndex(option.value);
          disabled = currentIndex < endIndex;
        }
      } else if (type === 'end') {
        selected = end === option.value;
        if (start) {
          // 结束时间不能早于开始时间（索引不能大于开始时间索引）
          const startIndex = getOptionIndex(start);
          const currentIndex = getOptionIndex(option.value);
          disabled = currentIndex > startIndex;
        }
      }
      
      return {
        ...option,
        disabled,
        selected
      };
    });
  }, [currentValue, getOptionIndex]);

  // 处理面板中选项的点击事件
  const handleOptionClick = useCallback((optionValue: string, type: 'start' | 'end') => {
    const newValue = { ...currentValue };
    
    if (type === 'start') {
      newValue.start = optionValue;
      // 如果选择的开始时间晚于已选的结束时间，清空结束时间
      if (newValue.end) {
        const startIndex = getOptionIndex(optionValue);
        const endIndex = getOptionIndex(newValue.end);
        if (startIndex < endIndex) {
          newValue.end = undefined;
        }
      }
      setSelectStep('end');
    } else {
      newValue.end = optionValue;
      setSelectStep('complete');
    }
    
    // 只有当开始时间和结束时间都选择了，才关闭面板并触发onChange
    if (newValue.start && newValue.end) {
      setTimeout(() => setShowPanel(false), 100);
      onChange?.(newValue);
    }
    
    if (!isControlled) {
      setInternalValue(newValue);
    }
  }, [currentValue, isControlled, onChange, getOptionIndex]);

  // 渲染单个选项项
  const renderOptionItem = useCallback((option: any, type: 'start' | 'end') => {
    const handleClick = () => {
      if (!option.disabled) {
        handleOptionClick(option.value, type);
      }
    };

    return (
      <div
        key={`${type}-${option.value}`}
        className={`relative-date-option ${option.selected ? 'selected' : ''} ${option.disabled ? 'disabled' : ''}`}
        onClick={handleClick}
      >
        {option.label}
      </div>
    );
  }, [handleOptionClick]);

  // 渲染面板的双列布局内容
  const popoverContent = useCallback(() => {
    const startOptions = generateOptions('start');
    const endOptions = generateOptions('end');

    return (
      <div className="relative-date-panel">
        <div className="relative-date-columns">
          <div className="relative-date-column">
            <div className="column-header">开始时间</div>
            <div className="column-options">
              {startOptions.map(option => renderOptionItem(option, 'start'))}
            </div>
          </div>
          <div className="column-divider"></div>
          <div className="relative-date-column">
            <div className="column-header">结束时间</div>
            <div className="column-options">
              {endOptions.map(option => renderOptionItem(option, 'end'))}
            </div>
          </div>
        </div>
      </div>
    );
  }, [generateOptions, renderOptionItem]);

  return (
    <Popover
      content={popoverContent}
      open={showPanel}
      onOpenChange={setShowPanel}
      placement="bottomLeft"
      trigger="click"
      overlayClassName="relative-date-popover"
    >
      <div
        className="relative-date-display"
        onClick={handleDisplayClick}
      >
        {displayText()}
      </div>
    </Popover>
  );
};

export default RelativeDatePicker;