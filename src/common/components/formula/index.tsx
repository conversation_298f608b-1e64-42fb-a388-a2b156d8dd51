import React, { useCallback, useEffect, useRef } from 'react';
import { Input, InputRef, List, message, Popover, Tag, DatePicker, Button, Select } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import AESPluginEvent from '@ali/aes-tracker-plugin-event';
import classNames from 'classnames';
import { useDebounceFn, useSetState } from 'ahooks';
import VirtualList from 'rc-virtual-list';
import { CommonBlockWrap } from '@/common/components/CommonBlockWrap';
import {
  VALID_KEYS,
  INIT_SELECTOR_VALUE,
  REG_EXPRESSION,
  FORMULA_POPOVER_CLASSNAME,
  ExpressionType,
  OptionItemTypeText,
  OptionItemTypeColor,
  RELATIVE_DATE_OPTIONS,
} from './constant';
import { FormulaProps, FormulaState, FormulaValue, OptionItem } from './type';
import {
  buildGroupedOptions,
  clearChineseText,
  defaultParseDataToForm,
  defaultParseFormToData,
  formulaItemToDOM,
  textToDOM,
  formulaItemToDomStr,
  getFormulaDataFromDom,
  insertNewFormulaItem,
  insertNewText,
  isFormulaEqual,
  preventDefault,
  removeInvalidNode,
  replaceFormulaKeyChar,
  sortOptions,
  stopPropagation,
} from './helper';
import RelativeDatePicker from './relativeDatePicker';
import './index.less';

export * from './type';
export * from './helper';
export * from './constant';

/** 公式组件 */
export const Formula: React.FC<FormulaProps> = (props) => {
  const defaultValue: FormulaValue = {
    expression: '',
    textExpression: '',
    type: ExpressionType.Common,
    varMap: {},
  };
  const {
    disabled = false,
    wrap = true,
    placeholder = '输入「@」后选择指标',
    size = 'middle',
    dropdownWidth = 800,
    options = [],
    value = defaultValue,
    onChange,
    calendarMode = 'year',
    formulaDebug = !!localStorage.getItem('formula-formulaDebug'),
    buildDataToForm = defaultParseDataToForm,
    parseFormToData = defaultParseFormToData,
    tracertInfo = null,
    name = null,
    className = null,
    supportText = false,
  } = props;

  if (formulaDebug) console.log('Formula props');

  /** 组件最外层Div引用 */
  const wrapperRef = useRef<HTMLDivElement>();
  /** 公式输入框Div引用 */
  const formulaRef = useRef<HTMLDivElement>();
  /** 列表弹窗搜索框 */
  const searchInputRef = useRef<InputRef>();
  /** 文本输入框 */
  const textInputRef = useRef<InputRef>();
  // const selectionRef: any = useRef()
  // 埋点初始化时间
  const initTime = useRef(null);

  /** 状态数据 */
  const [state, setState] = useSetState<FormulaState>({
    showSelection: false,
    showInputPopover: false,
    searchText: '',
    inputText: '',
    searchLoading: false,
    showCalendar: false,
    options: [],
    isOptionMutliType: null,
    currentExpression: '',
    selectionData: {
      anchorNode: null,
      anchorOffset: null,
    },
  });
  if (formulaDebug) console.log('Formula props', props);

  /** 初始化输入框内容 */
  const initFormulaInput = useCallback(() => {
    const { expression = '', varMap = {} } = value || {};
    const formulaData = parseFormToData({ expression, varMap }, options || [], formulaDebug);

    if (formulaDebug)
      console.log('Formula initFormulaInput', value, formulaData, state.isOptionMutliType);

    /** 光标处理：如果以公式开头，在开头加上span使得光标能移到前面 */
    const spanPrefix = formulaData.length && formulaData[0].value ? '<span></span>' : '';

    formulaRef.current.innerHTML = `${spanPrefix}${formulaData.reduce((prev, next) => {
      /** 为文本节点 */
      if (!next.value) {
        const reg = /^'([^']*)'$/;
        if (reg.test(next.label)) {
          // 如果是单引号括起来的，则是普通文本，采用指标的样式来渲染
          return `${prev}${formulaItemToDomStr(next)}<span></span>`;
        } else {
          // 否则就是数字
          return prev + next.label;
        }
      }

      return `${prev}${formulaItemToDomStr(next)}<span></span>`;
    }, '')}`;
    setState({ currentExpression: expression });
  }, [options, value, state.isOptionMutliType]);

  /** 将输入框内容同步到表单 */
  const setFormValue = useCallback(() => {
    const formulaData = getFormulaDataFromDom(formulaRef.current.childNodes);
    if (typeof onChange === 'function') {
      const newFormValue = buildDataToForm(formulaData, formulaDebug);
      
      /** 当表达式不一致时才去替换 */
      if (
        newFormValue?.expression !== value?.expression ||
        newFormValue?.textExpression !== value?.textExpression ||
        !value?.expression
      ) {

        if (formulaDebug) console.log('Formula setFormValue', formulaData);

        setState({ currentExpression: newFormValue?.expression });
        onChange(newFormValue?.expression ? newFormValue : null);
        if (typeof props.onChangeSideEffect === 'function') {
          props.onChangeSideEffect(newFormValue?.expression ? newFormValue : null);
        }
      }
    }
  }, [value]);

  /** 控制可输入字符及特定输入操作 */
  const onKeyDown = useCallback((evt) => {
    if (formulaDebug) console.log('Formula onKeyDown', evt.key);
    if (evt.key === '@' && evt.nativeEvent?.code === 'Digit2') {
      initTime.current = Date.parse(new Date() as any);
    }
    const selection = window.getSelection();
    switch (true) {
      /** 输入@触发选择项目 */
      case evt.key === '@':
      case evt.nativeEvent?.code === 'Digit2' && evt.shiftKey: // 中文输入法的 @
        console.log('Formula set anchorNode', selection.anchorNode);

        setState({
          showSelection: true,
          searchText: '',
          selectionData: {
            anchorNode: selection.anchorNode,
            anchorOffset: selection.anchorOffset,
          },
        });
        preventDefault(evt);
        break;
      /** 输入!文本输入 */
      case evt.key === '!':
        case evt.nativeEvent?.code === 'Digit1' && evt.shiftKey: // 中文输入法的 ！
          console.log('Formula set anchorNode', selection.anchorNode);
          if (!supportText) {
            // 如果不支持文本输入，则不能唤起文本输入框
            break;
          }
          
          setState({
            showInputPopover: true,
            inputText: '',
            selectionData: {
              anchorNode: selection.anchorNode,
              anchorOffset: selection.anchorOffset,
            },
          });
          preventDefault(evt);
          break;
      /** 以下按键为可输入 */
      case VALID_KEYS.includes(evt.key):
      case evt.key === 'Backspace':
      case evt.key === 'ArrowLeft':
      case evt.key === 'ArrowRight':
      case evt.key === 'Tab':
        break;
      case evt.key === 'Escape':
        setState({ showSelection: false, showInputPopover: false, searchText: '', inputText: '' });
        break;
      default:
        preventDefault(evt);
        break;
    }
  }, []);

  const onKeyUp = useCallback(() => {
    /** 清除输入框中的中文 */
    clearChineseText();
   
    /** 清除无用节点 */
    removeInvalidNode(formulaRef.current);
    // setFormValue();
  }, []);

  /** 光标处理：聚焦时如果最后一个元素是指标且没有SPAN结尾添加一个SPAN来显示光标 */
  const onFocus = useCallback(() => {
    initTime.current = Date.parse(new Date() as any);
    // console.log('Formula onFocus', formulaRef.current.lastChild.nodeName)
    if (formulaRef.current.lastChild && formulaRef.current.lastChild.nodeName === 'DIV') {
      if (formulaDebug) console.log('Formula onFocus', formulaRef.current.lastChild);
      formulaRef.current.appendChild(document.createElement('span'));
    }
  }, []);

  /** 在失焦的时候将数据同步带外层的表单数据中 */
  const onBlur = useCallback(() => {
    if (!state.showSelection) {
      if (formulaDebug) console.log('Formula onBlur');
      setFormValue();
    }
  }, [state.showSelection]);

  /** 指标选择 */
  const optionClick = useCallback(
    (data: { item: OptionItem; date: string }) => {
      if (tracertInfo && initTime.current) {
        const gapTime = (Date.parse(new Date() as any) - initTime.current) / 1000;
        // 埋点TODO
        AESPluginEvent('expAssessPlanInput', {
          et: 'INPUT',
          c1: tracertInfo.domain,
          c2: tracertInfo.businessLine,
          c3: tracertInfo.planId,
          c4: { ...tracertInfo.othersInfo, componentName: name },
          c5: tracertInfo.operateType,
          c6: gapTime,
        });
      }

      const newFormulaItem = formulaItemToDOM({
        ...data.item,
        date: data.date,
        classificationlabel: data.item?.classificationLabel,
      });
      insertNewFormulaItem(formulaRef.current, newFormulaItem, state.selectionData);
      setFormValue();
    },
    [formulaRef.current, state.selectionData],
  );

  const handleTextAdd = () => {
    if (!state.inputText) {
      return;
    }
    const newTextItem = textToDOM({
      label: `'${state.inputText}'`,
    });
    insertNewText(formulaRef.current, newTextItem, state.selectionData);
    setFormValue();

    setState({
      showInputPopover: false,
    });
  };

  /** 下拉选项存在多中类型 */
  useEffect(() => {
    const typeSet = options.reduce(
      (prev, next) => prev.add(next.type),
      new Set<OptionItem['type']>(),
    );
    if (formulaDebug) console.log('Formula isOptionMutliType', typeSet.size);
    setState({ isOptionMutliType: typeSet.size >= 2 });
  }, [options]);

  /** 下拉选项 */
  useEffect(() => {
    setState({ options: sortOptions(options) });
  }, [options]);

  useEffect(() => {
    if (formulaDebug) {
      console.log('Formula initFormulaInput adjust', {
        value,
        'state.currentExpression': state.currentExpression,
        newFormulaData: value?.expression ? parseFormToData(value, options, formulaDebug) : '',
        domFormulaData: getFormulaDataFromDom(formulaRef.current.childNodes),
      });
    }

    /**
     * 当表达式不为空：检查中没有游离的@符号和表达式有变动再做init且options有数据
     * 当表达式为空：直接初始化清空表达式
     */
    if (
      !value?.expression ||
      (value?.expression &&
        !value?.expression?.match(REG_EXPRESSION.KeyChar) &&
        options?.length &&
        (value.expression !== state.currentExpression ||
          /** 如果表达式内容与DOM内容不一致重新渲染 */
          !isFormulaEqual(
            parseFormToData(value, options, formulaDebug),
            getFormulaDataFromDom(formulaRef.current.childNodes),
          )))
    ) {
      if (formulaDebug) console.log('Formula initFormulaInput invoke', value);
      initFormulaInput();
    } else if (formulaDebug) console.log('Formula initFormulaInput skipped', value);
  }, [value?.expression, options, state.currentExpression, state.isOptionMutliType]);

  useEffect(() => {
    setFormValue();
  }, [options]);

  useEffect(() => {
    /** 点击非弹窗内容关闭弹窗 */
    const onClickOutside = (evt: any) => {
      if (!state.showSelection && !state.showInputPopover) return;

      /** 当前的点击在弹窗内 */
      const eventPath: HTMLElement[] = evt.path || evt?.composedPath() || [];
      /** 当前的点击在弹窗内 */
      const isClickInside = eventPath.some((item) => {
        const clasName = typeof item.className === 'string' ? item.className : '';
        return clasName.includes(FORMULA_POPOVER_CLASSNAME);
      });
      if (!isClickInside) {
        if (formulaDebug) console.log('Formula onClickOutside', evt);
        /** 点击弹窗外清除输入的@标识并重置数据 */
        setTimeout(() => {
          setState(INIT_SELECTOR_VALUE as any);
          replaceFormulaKeyChar(formulaRef.current, '');
        }, 50);
      }
    };

    window.addEventListener('click', onClickOutside);
    return () => window.removeEventListener('click', onClickOutside);
  }, [state.showSelection, state.showInputPopover]);

  const { run: onSearchChange } = useDebounceFn((searchText) => {
    /** 远程搜索 */
    if (searchText && props.searchRemoteFunc) {
      props
        .searchRemoteFunc(searchText)
        .then((options) => {
          setState({
            options: buildGroupedOptions(options),
            searchLoading: false,
          });
        })
        .catch(() => {
          setState({ options, searchLoading: false });
          message.error('搜索失败，请稍后再试');
        });
      /** 本地搜索 */
    } else if (searchText) {
      const newOptions = options.filter(({ label, value: optionValue }) => {
        const isInLabel = label.toLocaleLowerCase().includes(searchText?.toLowerCase());
        const isInValue = `${optionValue}`.toLocaleLowerCase().includes(searchText?.toLowerCase());
        return isInLabel || isInValue;
      });
      setState({
        options: sortOptions(newOptions),
        searchLoading: false,
      });
    } else {
      setState({
        options: sortOptions(options),
        searchLoading: false,
      });
    }
  });

  const calendarContent = (item) => {
    if (item.type !== 'Indicator' || !calendarMode) {
      return null;
    }
    if (calendarMode === 'year' || calendarMode === 'month') {
      return (
       <div
        className="datepicker-wrapper"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <DatePicker.RangePicker
          size="small"
          picker="month"
          value={null}
          style={{ width: 200 }}
          getPopupContainer={(trigger) => trigger.parentNode}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onChange={(date) => {
            // 如果开始和结束月份一致，则只取开始月份,如202405
            // 否则取范围，如202405-202409
            const range = date.map((item) => item.format('YYYYMM'));
            optionClick({
              item,
              date: range[0] === range[1] ? range[0] : range.join('_'),
            });
            setState(INIT_SELECTOR_VALUE as any);
          }}
        />
      </div>
      );
    } else if (calendarMode === 'relative') {
      return (
      <div
        className="datepicker-wrapper"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <RelativeDatePicker
          onChange={(val) => {
            optionClick({
              item,
              date: val.start === val.end ? val.start : `${val.start}_${val.end}`,
            });
            setState(INIT_SELECTOR_VALUE as any);
          }}
        >
        </RelativeDatePicker>
      </div>
      );
    }
    return null;
  };

  /** 弹窗内容 */
  const popoverContent = React.useCallback(
    () => (
      <div className="formula-popover" style={{ width: dropdownWidth - 32 }}>
        <div className="popover__left">
          <List
            size="small"
            header={
              <div>
                <Input
                  ref={searchInputRef}
                  value={state.searchText}
                  allowClear
                  placeholder="输入关键字筛选"
                  onChange={(evt) => {
                    setState({
                      searchText: evt.target.value,
                      searchLoading: true,
                    });
                    onSearchChange(evt.target.value);
                    evt.stopPropagation();
                  }}
                />
                {calendarMode && (
                  <div className="calendar-tips">
                    <ExclamationCircleOutlined />
                    若不选择月份范围，则默认指标的【考核周期】与绩效周期相同
                  </div>
                )}
              </div>
            }
          >
            <CommonBlockWrap
              size="middle"
              isLoading={state.searchLoading}
              isEmpty={!state.options?.length}
              emptyDescription="数据还未更新，请等待"
            >
              <VirtualList data={state.options} height={300} itemHeight={39} itemKey="value">
                {(item) => (
                  <List.Item
                    className={classNames({
                      disabled: item.type === 'group',
                    })}
                    onClick={() => {
                      if (item.type !== 'group') {
                        optionClick({ item, date: '' });
                        setState(INIT_SELECTOR_VALUE as any);
                      }
                    }}
                    extra={calendarContent(item)}
                  >
                    {
                      // 指标类型为排名
                      item?.classificationLabel === 'RANK' ? <Tag color="gold">排名</Tag> : null
                    }
                    {state.isOptionMutliType && !state.searchText ? (
                      <Tag color={OptionItemTypeColor[item.type]}>
                        {OptionItemTypeText[item.type]}
                      </Tag>
                    ) : null}
                    {item?.timeType === '2' ? <Tag color="processing">准实时</Tag> : null}
                    {item.label}
                  </List.Item>
                )}
              </VirtualList>
            </CommonBlockWrap>
          </List>
        </div>
      </div>
    ),
    [
      calendarMode,
      state.isOptionMutliType,
      state.options,
      state.selectionData,
      state.showCalendar,
      state.searchText,
    ],
  );

  const inputPopoverContent = () => (
    <div className="formula-popover" style={{ width: 280 }}>
      <Input
        ref={textInputRef}
        value={state.inputText}
        onChange={(evt) => {
          setState({ inputText: evt.target.value });
          evt.stopPropagation();
        }}
        allowClear
        placeholder="请输入"
        max={100}
      />
      <Button type="primary" onClick={handleTextAdd}>确定</Button>
    </div>
  );

  return (
    <div
      ref={wrapperRef}
      className={classNames('formula-wrapper', className, props.formulaWrapClassName)}
      style={props.formulaWrapStyle}
    >
      {/* 公式编辑器输入框 */}
      <div
        ref={formulaRef}
        className={classNames('formula-input', props.formulaClassName, size, {
          disabled,
          focused: state.showSelection,
          wrap,
          'show-type': state.isOptionMutliType,
        })}
        contentEditable={!disabled}
        placeholder={placeholder}
        onClick={stopPropagation}
        onKeyDown={onKeyDown}
        onKeyUp={onKeyUp}
        onFocus={onFocus}
        onBlur={onBlur}
        onCut={preventDefault}
        onPaste={preventDefault}
        // onCopy={preventDefault}
      />

      {/* 指标下拉选择面板 */}
      <Popover
        autoAdjustOverflow={false}
        overlayClassName={FORMULA_POPOVER_CLASSNAME}
        open={state.showSelection}
        placement="bottomLeft"
        content={popoverContent}
        afterOpenChange={(visible) => {
          if (visible) setTimeout(() => searchInputRef.current.focus(), 300);
        }}
      />

      {/* 字符串输入面板 */}
      <Popover
        autoAdjustOverflow={false}
        overlayClassName={FORMULA_POPOVER_CLASSNAME}
        open={state.showInputPopover}
        placement="bottomLeft"
        content={inputPopoverContent}
        afterOpenChange={(visible) => {
          if (visible) setTimeout(() => textInputRef.current.focus(), 300);
        }}
      />
    </div>
  );
};

// export const Formula = React.memo(InternalFormula)
