import moment from 'moment';
import { monthListToStr } from '@/common/utils';
import { message } from 'antd';
import { groupBy } from 'lodash';
import {
  ExpressionType,
  GROUP_INDICATOR_TIME_PERIOD,
  OptionItemTypeEnum,
  OptionItemTypePrefix,
  OptionItemTypeText,
  REG_EXPRESSION,
  VALID_KEYS,
} from './constant';
import { FormulaDataItem, FormulaValue, OptionItem, PlainObject, SelectionData } from './type';

/** 当前节点是文本节点 */
export function isFormulaTextNode(el: HTMLElement) {
  return '#text,SPAN'.includes(el.nodeName);
}

/** 获取文本节点文本内容 */
export function getTextNodeContent(el: HTMLElement) {
  return el.nodeName === '#text' ? el.nodeValue : el.innerText.replace(/<br>/g, '');
}

/** 设置文本节点文本内容 */
export function setTextNodeContent(el: HTMLElement, content: string) {
  if (el.nodeName === '#text') {
    // eslint-disable-next-line no-param-reassign
    el.nodeValue = content;
  } else {
    // eslint-disable-next-line no-param-reassign
    el.innerHTML = content;
  }
}

/** 当前节点是空的文本节点 */
export function isFormulaEmptyNode(el: HTMLElement) {
  if (isFormulaTextNode(el)) {
    return !getTextNodeContent(el);
  }

  return false;
}

/** 当前是一个有效的公式元素节点 */
export function isValidFormulaNode(el: HTMLElement) {
  if ('#text,SPAN,DIV'.includes(el.nodeName)) {
    return !isFormulaEmptyNode(el);
  }
  return false;
}

export function getCurrentNodeContent(el: HTMLElement) {
  if (el.parentNode.nodeName === 'DIV') {
    return getTextNodeContent(el);
  }
  return getCurrentNodeContent(el.parentNode as HTMLElement);
}

export function getFomulaChild(el: HTMLElement) {
  if (el.parentNode.nodeName === 'DIV') {
    return el;
  }
  return getFomulaChild(el.parentNode as HTMLElement);
}

/** 将内部表达式结构转成表单数据 */
export function defaultParseDataToForm(formulaData: FormulaDataItem[], debug: Boolean) {
  let type = ExpressionType.Common;
  let expression = '';
  let textExpression = '';

  if (debug) {
    console.log('Formula defaultBuildDataToForm', formulaData);
  }

  /** 数据对象 */
  const varMap = formulaData.reduce((prev, next) => {
    if (next.value) {
      const arrDate = next.date.split('_');
      // 如果起止日期是一样的，当成单月来处理，否则当成多月
      const periodTypeWatch = arrDate.length > 1 ? 'MULTI_MONTH' : 'MONTH';

      const prefix = OptionItemTypePrefix[next.type] || '';
      const key = next.date
        ? `${prefix}${next.value}_${periodTypeWatch}_${next.date}`
        : `${prefix}${next.value}`;
      const label = next.date ? `${next.label}(${arrDate.join('~')})` : next.label;
      // eslint-disable-next-line no-param-reassign
      prev[`${key}`] = label;
    }
    return prev;
  }, {});

  formulaData.forEach((item) => {
    if (item.value) {
      const prefix = OptionItemTypePrefix[item.type] || '';

      const arrNow = item.date.split('_');
      // 如果起止日期是一样的，当成单月来处理，否则当成多月
      const periodTypeWatch = arrNow.length > 1 ? 'MULTI_MONTH' : 'MONTH';

      const periodValue = item.date;

      expression += item.date
        ? `${prefix}${item.value}_${periodTypeWatch}_${periodValue}`
        : `${prefix}${item.value}`;
      textExpression += item.date ? `${item.label},${item.date}` : item.label;
    } else {
      expression += item.label.replace(/\s/g, '');
      textExpression += item.label.replace(/\s/g, '');
    }
  });

  if (debug) {
    console.log('Formula defaultBuildDataToForm', {
      expression,
      type,
      varMap,
      textExpression,
    });
  }

  if (expression) {
    return {
      expression,
      type,
      varMap,
      textExpression,
    };
  }
  return undefined;
}

/**
 * 将表单数据转成内部表达式结构
 * @example expression: @123_MONTH_202205_202206/@123'
 */
export function defaultParseFormToData(
  formData: FormulaValue,
  indicatorData: OptionItem[],
  debug?: boolean,
) {
  /** 匹配ID的正则 */
  const idMatchExp = Object.keys(formData.varMap)
    .filter((o) => !!o.replace(/\d/g, ''))
    .reduce((prev, next) => {
      const curItem = next.includes('$') ? `\\${next}` : next;
      return prev.concat([
        `${curItem}_MULTI_MONTH_\\d{6}_\\d{6}`,
        `${curItem}_MULTI_MONTH_CURMONTH(?:-\\d+)?_CURMONTH(?:-\\d+)?`,
        `${curItem}_MONTH_\\d{6}`,
        `${curItem}_MONTH_CURMONTH(?:-\\d+)?`,
        curItem,
      ]);
    }, [])
    .join('|');

  /** 公式中的指标 */
  const indicatorList = formData?.expression
    ?.match(new RegExp(`(${idMatchExp})`, 'g'))
    ?.filter((o) => !!o);
  if (!indicatorList?.length) {
    /** 如果没有变量则直接返回文本表达式 */
    if (formData.expression) return [{ label: formData.expression }];
    return [];
  }
  // indicatorList ['@2402260001940012001', '@2402270002040011148', '@2402270002040011148']

  const result: FormulaDataItem[] = [];
  /** 待处理公式 */
  let remainFormula = formData.expression;

  indicatorList.forEach((item, index) => {
    /** 指标元素结果数据 */
    const resultItem: FormulaDataItem = { label: '' };
    /** 当前指标所在开始索引 */
    const itemStartIndex = remainFormula.indexOf(item);
    /** 替换掉当前指标前缀字符串 */
    const prefix = remainFormula.substring(0, itemStartIndex);
    /** 替换掉当前指标后缀字符串 */
    let subfix = remainFormula.substring(itemStartIndex + item.length);

    if (prefix) {
      result.push({ label: prefix });
    }

    /** 通过正常匹配出ID */
    const idSection = item.match(REG_EXPRESSION.IdWithPrefix)?.[0] || '';
    resultItem.value = idSection.slice(1);
    resultItem.type =
      Object.entries(OptionItemTypePrefix).find((o) => o[1] === idSection[0])?.[0] || '';

    // 如果是异步查询出来（即在输入框里手动搜索出来）的指标，这种指标不在首屏的列表内
    // 由于上游组件的逻辑缺陷，只保存了首屏的指标，这种指标也就不在这里传入的 `indicatorData` 里
    // 那么这里就会出现找不到指标 ID 对应的指标名称的情况（`matchedIndicator` 为 null）
    // 这里简单兼容一下，把表达式内的 `varMap` 拿出来，如果在 `indicatorData` 里没有
    // 就用 `varMap` 里保存的名称
    const matchedIndicator = indicatorData.find((o) => o.value === resultItem.value);
    const matchedVarName = formData?.varMap?.[item];
    resultItem.label = matchedIndicator?.label || matchedVarName || '';
    resultItem.weight = matchedIndicator?.weight || '';

    const dateMatch = item.match(REG_EXPRESSION.DateFromExp);
    if (dateMatch) {
      resultItem.date = dateMatch?.pop()?.substring(1);
    }

    const match = subfix.match(/^\[\w+\|[,\d]+\]/);
    if (match) {
      subfix = subfix.replace(/^\[\w+\|[,\d]+\]/, '');

      resultItem.classificationlabel = 'RANK';

      result.push(resultItem);
      // }
    } else {
      result.push(resultItem);
    }

    remainFormula = subfix || '';

    if (index === indicatorList.length - 1 && subfix) {
      result.push({ label: subfix });
    }
  });

  if (debug) console.log('Formula defaultParseFormToData', result);

  return result;
}

export const isNumber = (val) => {
  const regPos = /^[0-9]+.?[0-9]*/;
  if (regPos.test(val)) {
    return true;
  }
  return false;
};

export function formulaItemToDomStr(data: FormulaDataItem) {
  return `<div
    contenteditable="false"
    data-label="${data.label}"
    ${`data-value="${data.value || ''}"`}
    ${`data-type="${data.type || ''}"`}
    ${`data-weight="${data.weight || ''}"`}
    ${`data-date="${data.date || ''}"`}
    ${`data-classificationLabel="${data?.classificationlabel || ''}"`}
  >
    <p>${data.label || data.value}</p>
    ${data.type ? `<i class="type">${OptionItemTypeText[data.type]}</i>` : ''}
    ${data.weight ? `<i>${data.weight}${isNumber(data.weight) ? '%' : ''}</i>` : ''}
    ${data.date ? `<i class="date">${data.date}</i>` : ''}
    ${data.timeType === '2' ? '<i>准实时</i>' : ''}

  </div>`.replace(/\n/g, '');
}

/** 公式选择元素数据转DOM字符串 */
export function formulaItemToDOM(data: FormulaDataItem) {
  const div = document.createElement('div');
  div.innerHTML = formulaItemToDomStr(data);
  return div.firstChild as HTMLDivElement;
}

/** 文本转DOM字符串 */
export function textToDOM(data: FormulaDataItem) {
  const div = document.createElement('div');
  div.innerHTML = formulaItemToDomStr(data);
  return div.firstChild as HTMLDivElement;
}

/** 阻止不支持默认事件 */
export function preventDefault(evt: any) {
  if (evt) evt.preventDefault();
}

/** 阻止默认冒泡事件 */
export function stopPropagation(evt: any) {
  if (evt) evt.stopPropagation();
}

/** 将表达式DOM内容关键词替换成目标字符串 */
export function replaceFormulaKeyChar(formulaDom: HTMLDivElement, targetContent: string) {
  /** 公式输入HTML */
  const formulaHTML = formulaDom?.innerHTML || '';
  /** 配游离的@符号 */
  const keyCharList = formulaHTML.match(REG_EXPRESSION.KeyChar);
  if (keyCharList?.length) {
    // eslint-disable-next-line no-param-reassign
    formulaDom.innerHTML = formulaHTML.replace(REG_EXPRESSION.KeyChar, () => targetContent);
    return true;
  }
  return false;
}

/**
 * 将新的指标插入公式中
 * @param formulaDom 当前公式输入框DOM
 * @param formulaItemDom 当前需要插入到公式中的新指标节点
 * @param selection 当前选择指标需要插入的光标所在位置
 */
export function insertNewFormulaItem(
  formulaDom: HTMLDivElement,
  formulaItemDom: HTMLElement,
  selection: SelectionData,
) {
  console.log('Formula insertNewFormulaItem', selection);
  try {
    /** 当前光标所在位置 */
    const windowSelection = window.getSelection();
    const range = document.createRange();
    /** 给插入的指标添加SPAN来显示光标 */
    const newSpaceItem = document.createElement('span');

    /** 当前光标所在节点 */
    const cursorNode = selection.anchorNode;

    /** 光标目标所在节点 */
    let curorPositionNode: Text | HTMLSpanElement = newSpaceItem;

    /** 当前的光标在文本上 */
    if ('#text,SPAN'.includes(cursorNode.nodeName)) {
      console.log('当前的光标在文本上');
      /** 当前光标在文本节点开头 */
      if (selection.anchorOffset === 0) {
        /** 当前节点在 */
        const isEmptyNode =
          cursorNode.nodeName === 'SPAN'
            ? (cursorNode as HTMLSpanElement).innerHTML === ''
            : cursorNode.nodeValue === '';

        if (isEmptyNode) {
          formulaDom.insertBefore(newSpaceItem, cursorNode.nextSibling);
          formulaDom.insertBefore(formulaItemDom, newSpaceItem);
          /**
           * 未知错误处理
           * ![未知错误，节点被修改成spanstyle错误节点](https://img.alicdn.com/imgextra/i3/O1CN01N8kU5g24MG9WWJzrg_!!6000000007376-2-tps-1544-440.png)
           */
        } else if (cursorNode.parentNode.nodeName.length > 10) {
          const newSpanNode = document.createElement('span');
          newSpanNode.innerText = getTextNodeContent(cursorNode as HTMLElement);
          formulaDom.replaceChild(newSpanNode, cursorNode.parentNode);
          formulaDom.insertBefore(newSpaceItem, newSpanNode);
          formulaDom.insertBefore(formulaItemDom, newSpaceItem);
        } else if (cursorNode.parentNode.parentNode.nodeName === 'SPAN') {
          const newSpanNode = document.createElement('span');
          // eslint-disable-next-line max-len
          newSpanNode.innerText = getTextNodeContent(
            cursorNode.parentNode.parentNode as HTMLElement,
          );
          formulaDom.replaceChild(newSpanNode, cursorNode.parentNode.parentNode);
          formulaDom.insertBefore(newSpaceItem, newSpanNode);
          formulaDom.insertBefore(formulaItemDom, newSpaceItem);
        } else {
          const newSpanNode = document.createElement('span');
          newSpanNode.innerText = getTextNodeContent(cursorNode as HTMLElement);
          formulaDom.replaceChild(newSpanNode, getFomulaChild(cursorNode as HTMLElement));
          formulaDom.insertBefore(newSpaceItem, newSpanNode);
          formulaDom.insertBefore(formulaItemDom, newSpaceItem);
        }

        /** 当前光标在文本节点结尾 */
      } else if (selection.anchorOffset === cursorNode.nodeValue.length) {
        if (cursorNode === formulaDom.lastChild) {
          formulaDom.appendChild(newSpaceItem);
          formulaDom.insertBefore(formulaItemDom, newSpaceItem);
        } else {
          formulaDom.insertBefore(newSpaceItem, cursorNode.nextSibling);
          formulaDom.insertBefore(formulaItemDom, newSpaceItem);
        }
        /** 当前光标在文本节点中间 */
      } else {
        const textNodeContent = cursorNode.nodeValue;
        const prevText = textNodeContent.substring(0, selection.anchorOffset);
        const prev = document.createTextNode(prevText);
        const next = document.createTextNode(textNodeContent.substring(selection.anchorOffset));
        formulaDom.insertBefore(next, cursorNode.nextSibling);
        formulaDom.insertBefore(formulaItemDom, next);
        formulaDom.insertBefore(prev, formulaItemDom);
        cursorNode.nodeValue = '';

        curorPositionNode = next;
      }
    } else {
      formulaDom.appendChild(newSpaceItem);
      formulaDom.insertBefore(formulaItemDom, newSpaceItem);
      formulaDom.insertBefore(newSpaceItem.cloneNode(), formulaItemDom);
    }

    range.selectNodeContents(curorPositionNode);
    range.setStart(curorPositionNode, 0);
    range.setEnd(curorPositionNode, 0);
    range.collapse(true);
    windowSelection.removeAllRanges();
    windowSelection.addRange(range);
    console.log('Formula insertNewFormulaItem range', curorPositionNode, range);
  } catch (err) {
    console.log(err);
    message.warn('指标未成功选择，请重试');
  }
}

/**
 * 将新的文本插入公式中
 * @param formulaDom 当前公式输入框DOM
 * @param label 当前需要插入到公式中的新文本
 * @param selection 当前选择指标需要插入的光标所在位置
 */
export function insertNewText(
  formulaDom: HTMLDivElement,
  labelDom: HTMLElement,
  selection: SelectionData,
) {
  console.log('Formula insertNewFormulaItem', selection);
  try {
    /** 当前光标所在位置 */
    const windowSelection = window.getSelection();
    const range = document.createRange();
    /** 给插入的指标添加SPAN来显示光标 */
    const newSpaceItem = document.createElement('span');

    /** 当前光标所在节点 */
    const cursorNode = selection.anchorNode;

    /** 光标目标所在节点 */
    let curorPositionNode: Text | HTMLSpanElement = newSpaceItem;

    /** 当前的光标在文本上 */
    if ('#text,SPAN'.includes(cursorNode.nodeName)) {
      console.log('当前的光标在文本上');
      /** 当前光标在文本节点开头 */
      if (selection.anchorOffset === 0) {
        /** 当前节点在 */
        const isEmptyNode =
          cursorNode.nodeName === 'SPAN'
            ? (cursorNode as HTMLSpanElement).innerHTML === ''
            : cursorNode.nodeValue === '';

        if (isEmptyNode) {
          formulaDom.insertBefore(newSpaceItem, cursorNode.nextSibling);
          formulaDom.insertBefore(labelDom, newSpaceItem);
          /**
           * 未知错误处理
           * ![未知错误，节点被修改成spanstyle错误节点](https://img.alicdn.com/imgextra/i3/O1CN01N8kU5g24MG9WWJzrg_!!6000000007376-2-tps-1544-440.png)
           */
        } else if (cursorNode.parentNode.nodeName.length > 10) {
          const newSpanNode = document.createElement('span');
          newSpanNode.innerText = getTextNodeContent(cursorNode as HTMLElement);
          formulaDom.replaceChild(newSpanNode, cursorNode.parentNode);
          formulaDom.insertBefore(newSpaceItem, newSpanNode);
          formulaDom.insertBefore(labelDom, newSpaceItem);
        } else if (cursorNode.parentNode.parentNode.nodeName === 'SPAN') {
          const newSpanNode = document.createElement('span');
          // eslint-disable-next-line max-len
          newSpanNode.innerText = getTextNodeContent(
            cursorNode.parentNode.parentNode as HTMLElement,
          );
          formulaDom.replaceChild(newSpanNode, cursorNode.parentNode.parentNode);
          formulaDom.insertBefore(newSpaceItem, newSpanNode);
          formulaDom.insertBefore(labelDom, newSpaceItem);
        } else {
          const newSpanNode = document.createElement('span');
          newSpanNode.innerText = getTextNodeContent(cursorNode as HTMLElement);
          formulaDom.replaceChild(newSpanNode, getFomulaChild(cursorNode as HTMLElement));
          formulaDom.insertBefore(newSpaceItem, newSpanNode);
          formulaDom.insertBefore(labelDom, newSpaceItem);
        }

        /** 当前光标在文本节点结尾 */
      } else if (selection.anchorOffset === cursorNode.nodeValue.length) {
        if (cursorNode === formulaDom.lastChild) {
          formulaDom.appendChild(newSpaceItem);
          formulaDom.insertBefore(labelDom, newSpaceItem);
        } else {
          formulaDom.insertBefore(newSpaceItem, cursorNode.nextSibling);
          formulaDom.insertBefore(labelDom, newSpaceItem);
        }
        /** 当前光标在文本节点中间 */
      } else {
        const textNodeContent = cursorNode.nodeValue;
        const prevText = textNodeContent.substring(0, selection.anchorOffset);
        const prev = document.createTextNode(prevText);
        const next = document.createTextNode(textNodeContent.substring(selection.anchorOffset));
        formulaDom.insertBefore(next, cursorNode.nextSibling);
        formulaDom.insertBefore(labelDom, next);
        formulaDom.insertBefore(prev, labelDom);
        cursorNode.nodeValue = '';

        curorPositionNode = next;
      }
    } else {
      formulaDom.appendChild(newSpaceItem);
      formulaDom.insertBefore(labelDom, newSpaceItem);
      formulaDom.insertBefore(newSpaceItem.cloneNode(), labelDom);
    }

    range.selectNodeContents(curorPositionNode);
    range.setStart(curorPositionNode, 0);
    range.setEnd(curorPositionNode, 0);
    range.collapse(true);
    windowSelection.removeAllRanges();
    windowSelection.addRange(range);
    console.log('Formula insertNewFormulaItem range', curorPositionNode, range);
  } catch (err) {
    console.log(err);
    message.warn('指标未成功选择，请重试');
  }
}

/** 对象Key映射 */
export function keyMap<T = PlainObject>(array: any[], keyMapObj: PlainObject<string>): T[] {
  const keys = Object.keys(keyMapObj);
  return array.map((item) =>
    keys.reduce((prev, next) => {
      // eslint-disable-next-line no-param-reassign
      prev[keyMapObj[next]] = item[next];
      return prev;
    }, {} as T),
  );
}

/** 移除无用节点 */
export function removeInvalidNode(el: HTMLElement) {
  if (el.childNodes.length === 1 && isFormulaEmptyNode(el.firstChild as HTMLElement)) {
    el.removeChild(el.firstChild);
  } else {
    el.childNodes.forEach((child) => {
      if (child.nodeName === 'BR') {
        el.removeChild(child);
      } else if (child.nodeName === 'SPAN') {
        removeInvalidNode(child as HTMLElement);
      }
    });
  }
}

/** 清除中文 */
export function clearChineseText() {
  const selection = window.getSelection();

  try {
    /** 编辑的是输入框直接SPAN Node */
    const isInputSpanNode =
      selection.anchorNode.parentNode &&
      selection.anchorNode.parentElement.className.includes('formula-input');
    /** 编辑的是输入框直接SPAN Node的文本节点 */
    const isSpanInnerTextNode =
      selection.anchorNode.parentNode &&
      selection.anchorNode.parentNode.nodeName === 'SPAN' &&
      selection.anchorNode.parentNode?.parentElement?.className?.includes('formula-input');

    /** 当前选中的节点是文本输入的时候才会清除，防止鼠标在指标节点时也被清除了 */
    if (
      isFormulaTextNode(selection.anchorNode as HTMLElement) &&
      (isInputSpanNode || isSpanInnerTextNode)
    ) {
      const nodeContent = getTextNodeContent(selection.anchorNode as HTMLElement);
      const newNodeContent = nodeContent.replace(/[\s\S]/g, (item) => {
        if (VALID_KEYS.includes(item)) return item;
        return '';
      });
      console.log('Formula clearChineseText', nodeContent);

      if (nodeContent !== newNodeContent) {
        setTextNodeContent(selection.anchorNode as HTMLElement, newNodeContent);
        const range = document.createRange();
        range.setStart(selection.anchorNode, newNodeContent.length);
        range.setEnd(selection.anchorNode, newNodeContent.length);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  } catch {
    // Don't care
  }
}

/**
 * 通过公式组件转成公式数据
 * @param nodes 公式组件子集节点
 */
export function getFormulaDataFromDom(nodes: NodeListOf<ChildNode>) {
  return Array.from(nodes)
    .filter(isValidFormulaNode)
    .map((item: HTMLDivElement | HTMLElement) => {
      let label = '';
      if (item.nodeName === '#text') {
        label = item.nodeValue;
        /**
         * @description 因为如果contenteditable=true的子元素出现contenteditable=false
         * 就会出现contenteditable=false后面的光标无法显示，所以会在contenteditable=false
         * 元素后面添加SPAN元素，在编辑contenteditable=false后面的内容时，会出现输入内容出现在
         * SPAN元素内所以需要把这部分内容取出
         */
      } else if (item.nodeName === 'SPAN') {
        label = item.innerHTML.replace(/<br>/g, '');
      }
      return Object.assign({ label }, item.dataset);
    });
}

/** 判断公式数据是否一致 */
export function isFormulaEqual(value: FormulaDataItem[], other: FormulaDataItem[]) {
  return value.every((valueItem, index) => {
    /** 其他的不存在则返回不相同 */
    if (!other[index]) return false;
    const otherItem = other[index];
    /** 两个对象的所有值相等 */
    return Object.keys(valueItem).every((itemKey) => valueItem[itemKey] === otherItem[itemKey]);
  });
}

export function sortOptions(options: OptionItem[]) {
  /** 优先放在前面展示的类型 */
  const frontTypesMap = {
    [OptionItemTypeEnum.CheckResult]: 4,
    [OptionItemTypeEnum.CheckModule]: 3,
    [OptionItemTypeEnum.CheckItem]: 2,
    [OptionItemTypeEnum.Indicator]: 1,
  };
  return options.sort((a, b) => (frontTypesMap[b.type] || 0) - (frontTypesMap[a.type] || 0));
}

/** 对可选项进行分组 */
export function buildGroupedOptions(options: OptionItem[]) {
  /** 优先放在前面展示的分组 */
  const frontTypes = [
    OptionItemTypeEnum.CheckResult,
    OptionItemTypeEnum.CheckModule,
    OptionItemTypeEnum.CheckItem,
  ].join();
  const groupOptions = groupBy(options, 'type');
  return Object.keys(groupOptions)
    .sort((key) => {
      if (frontTypes.includes(key)) {
        return -1;
      } else if (key === 'null') {
        return 1;
      }
      return 0;
    })
    .reduce(
      (prev, next) =>
        prev.concat(
          [
            {
              label: next === 'null' ? '未关联数据集' : OptionItemTypeText[next] || next,
              type: 'group',
              value: next,
            },
          ],
          groupOptions[next].map((o) => Object.assign(o, { type: OptionItemTypeEnum.Indicator })),
        ),
      [],
    );
}
