import { FormulaState, PlainObject } from './type';

/** 可输入的字符 */
export const VALID_KEYS = '0123456789+-*/.()';

/** 初始化选择数据 */
export const INIT_SELECTOR_VALUE: Partial<FormulaState> = {
  showCalendar: false,
  showInputPopover: false,
  selectedValue: null,
  showSelection: false,
  searchText: '',
};

/**
 * 公式类型
 * @description 如果公式中存在指标绑定日期，则为Time否则为Common
 */
export enum ExpressionType {
  /** 正常 */
  Common = 'COMMON',
  /** 带时间 */
  Time = 'TIME',
}

/** 产品域对应的表达式日历形式 */
export const DomainCalendarType: PlainObject = {
  /** 绩效 */
  KPI: 'year',
  /** 激励赛 */
  ENCOURAGE: 'year',
  /** 荣誉 */
  HONOUR: 'month',
};

/** 选项类型 */
export enum OptionItemTypeEnum {
  /** 指标 */
  Indicator = 'Indicator',
  /** 考核项 */
  CheckItem = 'CheckItem',
  /** 考核模块 */
  CheckModule = 'CheckModule',
  /** 公式结果 */
  CheckResult = 'CheckResult',
}

/** 可选项标识 */
export const OptionItemTypePrefix = {
  [OptionItemTypeEnum.Indicator]: '@',
  [OptionItemTypeEnum.CheckItem]: '$',
  [OptionItemTypeEnum.CheckModule]: '#',
  [OptionItemTypeEnum.CheckResult]: '&',
};

export const OptionItemTypeText = {
  [OptionItemTypeEnum.Indicator]: '指标',
  [OptionItemTypeEnum.CheckItem]: '项',
  [OptionItemTypeEnum.CheckModule]: '模块',
  [OptionItemTypeEnum.CheckResult]: '公式结果',
};

export const OptionItemTypeColor = {
  [OptionItemTypeEnum.Indicator]: 'gold',
  [OptionItemTypeEnum.CheckItem]: 'green',
  [OptionItemTypeEnum.CheckModule]: 'blue',
  [OptionItemTypeEnum.CheckResult]: 'red',
};

/** 正则表达式 */
export const REG_EXPRESSION = {
  /** 从表达式中匹配指标ID */
  IdFromExp: /((@|\d|-|,)+|(@|\$|#)(\d|\w)+)/g,
  /** 匹配出带Prefix的ID */
  IdWithPrefix: /(@|#|\$|&)(\d+|\w+)/g,
  /** 从字符串中匹配出日期 */
  DateFromExp: /(_\d{6}_\d{6})|(_CURMONTH(?:-\d+)?_CURMONTH(?:-\d+)?)|(_\d{6})|(_CURMONTH(?:-\d+)?)/g,
  /** 从表达式中匹配出游离的@ */
  KeyChar: /@(?!(\d|\w)+)/g,
};

/** 公式选择弹窗内容类 */
export const FORMULA_POPOVER_CLASSNAME = 'FORMULA_POPOVER_CLASSNAME';

/** 用于排名的指标-取值时间分区 */
export const GROUP_INDICATOR_TIME_PERIOD = [
  {
    label: '用本政策周期中该指标的值进行排名',
    value: 'GROUP_INDICATOR_NOW',
  },
  {
    label: '用上个政策周期中该指标的值进行排名',
    value: 'GROUP_INDICATOR_LAST',
  },
];

export const RELATIVE_DATE_OPTIONS = [
  { label: 'CURMONTH', value: 'CURMONTH' },
  { label: 'CURMONTH-1', value: 'CURMONTH-1' },
  { label: 'CURMONTH-2', value: 'CURMONTH-2' },
  { label: 'CURMONTH-3', value: 'CURMONTH-3' },
  { label: 'CURMONTH-4', value: 'CURMONTH-4' },
  { label: 'CURMONTH-5', value: 'CURMONTH-5' },
  { label: 'CURMONTH-6', value: 'CURMONTH-6' },
  { label: 'CURMONTH-7', value: 'CURMONTH-7' },
  { label: 'CURMONTH-8', value: 'CURMONTH-8' },
  { label: 'CURMONTH-9', value: 'CURMONTH-9' },
  { label: 'CURMONTH-10', value: 'CURMONTH-10' },
  { label: 'CURMONTH-11', value: 'CURMONTH-11' },
  { label: 'CURMONTH-12', value: 'CURMONTH-12' },
];