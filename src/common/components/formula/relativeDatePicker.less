.relative-date-display {
  min-width: 200px;
  height: 24px;
  line-height: 22px;
  padding: 0 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;

  &:hover {
    border-color: #40a9ff;
  }
}

.relative-date-panel {
  width: 280px;
  padding: 8px;
}

.relative-date-columns {
  display: flex;
  gap: 16px;
}

.relative-date-column {
  flex: 1;
}

.column-header {
  font-size: 12px;
  font-weight: 500;
  color: #666;
  margin-bottom: 8px;
  text-align: center;
  padding-bottom: 4px;
  border-bottom: 1px solid #f0f0f0;
}

.column-options {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.relative-date-option {
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  text-align: center;

  &:hover:not(.disabled) {
    background-color: #f5f5f5;
  }

  &.selected {
    background-color: #1890ff;
    color: #fff;
  }

  &.disabled {
    color: #bfbfbf;
    cursor: not-allowed;
    background-color: #f5f5f5;
  }
}

.column-divider {
  width: 1px;
  background-color: #f0f0f0;
  margin: 20px 0 8px 0;
} 