import React, { useEffect } from 'react';
import { useSetState } from 'ahooks';
import { Col, Form, Row, FormProps } from 'antd';
import { FormulaWithSearch } from '@/common/components/FormulaWithSearch';
import { moduleItem, OptionItem } from './type';
import { variableGroup } from './helper';
import './index.less';
import { tracertInfo } from '../../Types';

interface IProps extends FormProps {
  // 考核模块使用的地方
  usedPosition?: 'FirAppraisal' | 'SecAppraisal';
  options: OptionItem[];
  /** 组件类型 custom: 自定义；auto: 自动生成 */
  resultType?: string;
  moduleData?: moduleItem[];
  formName?: any[];
  /** 是否禁用 */
  disabled?: boolean;
  /** 数据变更操作 */
  onChange?: (v: any) => void;
  /** 按钮点击回调 */
  propOnChange?: (type: string) => void;
  /** 公式类型切换 */
  formulaTypeOnChange?: (type: string) => void;
  /** 自定义类型文案 */
  customText?: {
    auto?: string;
    custom?: string;
  };
  /** 是否展示按钮icon */
  isShowIcon?: boolean;
  /** 默认回显公式 */
  ruleGroupExpression?: any;
  /** 埋点相关 */
  tracertInfo?: tracertInfo;
  curComeponetName?: string;
  [key: string]: any;
}

const AutoGenerateFormula: React.FC<IProps> = (props) => {
  const {
    options,
    resultType = 'custom',
    moduleData = null,
    curComeponetName = '考核结果',
    formName,
    onChange,
    disabled = false,
    ruleGroupExpression = null,
    tracertInfo = null,
    businessGroup,
  } = props;
  const [state, setState] = useSetState({
    formulaType: 'custom',
    disabled: false,
  });

  useEffect(() => {
    setState({ disabled });
  }, [disabled]);

  useEffect(() => {
    if (resultType) setState({ formulaType: resultType });
  }, [resultType]);

  useEffect(() => {
    if (moduleData && onChange && state.formulaType === 'custom') {
      if (variableGroup(moduleData) === 'DEFAULT') {
        onChange(ruleGroupExpression);
      } else {
        onChange(variableGroup(moduleData));
      }
    } else if (!moduleData && onChange && state.formulaType === 'custom') {
      onChange(null);
    }
  }, [JSON.stringify(moduleData), state.formulaType]);

  return (
    <Form.Item className="auto-generate-warp">
      <Row gutter={24}>
        <Col span={24}>
          <Form.Item
            name={formName}
            rules={[
              {
                required: true,
                message: '请输入考核公式',
              },
            ]}
            noStyle
          >
            <FormulaWithSearch
              tracertInfo={tracertInfo}
              value={props.value}
              onChange={props.onChange}
              options={options}
              placeholder={'输入「@」后选择指标'}
              name={curComeponetName}
              disabled={false}
              periodTypeWatch={props?.periodTypeWatch}
              periodValueWatch={props?.periodValueWatch}
              periodValueMultiWatch={props?.periodValueMultiWatch}
              businessGroup={businessGroup}
              calendarMode={props?.calendarMode}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form.Item>
  );
};

export default AutoGenerateFormula;
