import { moduleItem } from './type';

/** 生成运算过程 */
export const getProcess = (NORMAL_DATA, RATIO_DATA, ADJUST_DATA, SEE_DATA, orgs) => {
  let textExpression = null;

  // 如果有系数项
  if (RATIO_DATA?.length) {
    RATIO_DATA?.map(it => {
      textExpression = textExpression ? `${textExpression}*${it[orgs]}` : it[orgs]
    })
    textExpression = `${textExpression}`
  };

  // 如果有常数项
  if (NORMAL_DATA?.length) {
    let normal_data = null
    NORMAL_DATA?.map(it => {
      normal_data = normal_data ? `${normal_data}+${it[orgs]}` : it[orgs]
    })
    if (NORMAL_DATA.length > 1) {
      normal_data = `(${normal_data})`
    }
    textExpression = textExpression ? `${textExpression}*${normal_data}` : `${normal_data}`;
  };

  // 如果有调整项
  if (ADJUST_DATA?.length) {
    let adjust_data = null
    ADJUST_DATA?.map(it => {
      adjust_data = adjust_data ? `${adjust_data}+${it[orgs]}` : it[orgs]
    })
    if (ADJUST_DATA.length > 1) {
      adjust_data = `(${adjust_data})`
    }
    textExpression = textExpression ? `${textExpression}+${adjust_data}` : `${adjust_data}`;
  };
  return textExpression;
};

export const variableGroup = (value: moduleItem[]) => {
  let isNoType = 0;
  let NORMAL_DATA = []; let RATIO_DATA = []; let ADJUST_DATA = []; let SEE_DATA = [];
  let varMap = null;
  const varIds = [];
  function splitModuleData(data) {
    switch (data.type) {
      case 0:
        NORMAL_DATA.push(data);
        break;
      case 1:
        RATIO_DATA.push(data);
        break;
      case 2:
        ADJUST_DATA.push(data);
        break;
      case 4:
        SEE_DATA.push(data);
        break;
      default:
        break;
    }
  }

  value?.map(it => {
    if(it.type === 4) isNoType += 1
    splitModuleData(it);
    varMap = { ...varMap, [`${it.id}`]: it.name };
    varIds.push(it.id);
  });

  if (isNoType && isNoType === value?.length) {
    return 'DEFAULT'
  }

  const formatValue = {
    varMap,
    textExpression: getProcess(NORMAL_DATA, RATIO_DATA, ADJUST_DATA, SEE_DATA, 'name'),
    expression: getProcess(NORMAL_DATA, RATIO_DATA, ADJUST_DATA, SEE_DATA, 'id'),
    type: 'COMMON',
    funcName: null,
    varIds,
    // expressionWithWeight: '13',
  };
  return formatValue;
};
