/** 选项类型 */
export enum OptionItemTypeEnum {
  /** 指标 */
  Indicator = 'Indicator',
  /** 考核项 */
  CheckItem = 'CheckItem',
  /** 考核模块 */
  CheckModule = 'CheckModule',
  /** 考核模块 */
  CheckResult = 'CheckResult',
}
export interface OptionItem {
  /** 指标类型 */
  configType?: string;
  /** 指标标题 */
  label: string;
  /** 指标ID */
  value: string;
  /** 指标层级 */
  type: OptionItemTypeEnum | string;
  /** 权重 */
  weight?: string;
}

export enum ExpressionType {
  /** 正常 */
  Common = 'COMMON',
  /** 带时间 */
  Time = 'TIME'
}

/** 表单数据结构 */
export interface FormulaValue {
  /**
   * 表达式
   * @example executePeriodVar(@1231231231,2020-12-20)/#123123123123
   */
  expression: string;
  /** 表达式类型，包含日期指标则为TIME，其他为COMMON */
  type?: ExpressionType;
  /** 用文本替换ID后的表达式，通常用作文本表达式展示 */
  textExpression?: string;
  /**
   * 表达式中涉及的ID与文本映射关系
   * @notice key需要根据不同维度指标添加Prefix，指标为@，考核项为$，考核模块为#
   */
  varMap?: Record<string, string>;
  [key: string]: any;
}

export enum changeBtnTypeText {
  custom = '自定义',
  auto = '自动生成'
}

/** 考核模块类型 */
export enum moduleDataType {
  NORMAL = '常数项',
  RATIO = '系数项',
  ADJUST = '调整项',
  SEE = '查看项',
}

/** 公式结构 */
export interface Expression {
  varMap?: { [index: string]: string };
  textExpression?: string;
  expression?: string;
  type?: string;
  funcName?: string;
  upperLimit?: Expression;
  lowLimit?: Expression;
  ql?: string;
  varIds?: string[];
  expressionWithWeight?: string;
}

export interface moduleItem {
  /** 模块｜组件id */
  id: string;
  /** 模块名称 */
  name: string;
  /** 模块类型 */
  type: number;
  /** 权重 */
  weight?: Expression;
  /** 模块数据类型 */
  configType: string;
}
