import { useDebounceFn, useSetState } from 'ahooks';
import { message, Select, Spin, Tag } from 'antd';
import React, { useEffect } from 'react';

import './index.less';

function setRealTimeTag(timeType: string, label: string) {
  return (
    <>
      {timeType === '2' ? <Tag color="processing">准实时</Tag> : null}
      {label}
    </>
  );
}

export const SelectWithSearch: React.FC<any & {
  onSearchRemote: (value: string) => Promise<any[]>;
}> = (props) => {
  const [state, setState] = useSetState({
    searchValue: '',
    newIndictorList: [],
    searchResult: [],
    searchLoading: false,
  });

  const {
    run: onSearchRemote,
  } = useDebounceFn(() => {
    if (!state.searchValue) {
      setState({ searchLoading: false });
      return;
    }
    setState({ searchLoading: true });
    props.onSearchRemote(state.searchValue)
      .then(searchResult => {
        message.success({ key: 'loading', content: '查询完成' });
        const data = searchResult?.map(it => ({
          ...it,
          options: it?.options.map(opIt => ({ ...opIt, label: setRealTimeTag(opIt.timeType, opIt.label) })),
        }));
        setState({ searchResult: data });
      })
      .catch((err) => {
        message.error({ key: 'loading', content: `查询失败，${err.errorMessage}` });
      })
      .finally(() => {
        setState({ searchLoading: false });
      });
  });

  useEffect(() => {
    onSearchRemote();
  }, [state.searchValue]);

  useEffect(() => {
    if (props.options?.length) {
      const data = props.options.map(it => ({ ...it, label: setRealTimeTag(it.timeType, it.label) }));
      setState({ newIndictorList: data });
    } else {
      setState({ newIndictorList: [] });
    }
  }, [props.options]);

  return (
    <Select
      {...props}
      loading={state.searchLoading}
      filterOption={false}
      options={state.searchValue ? state.searchResult : state.newIndictorList}
      onChange={(value, option) => {
        setState({ searchValue: '' });
        if (props.onChange) props.onChange(value, option);
      }}
      onSearch={(value) => {
        setState({
          searchValue: value,
          searchLoading: true,
        });
      }}
      dropdownRender={(menu) => (
        <Spin spinning={state.searchLoading}>
          { menu }
        </Spin>
      )}
    />
  );
};
