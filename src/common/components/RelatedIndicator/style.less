.related-indicators {
  flex-wrap: wrap;

  &__item-helper {
    z-index: 9999;
  }

  &__item,
  &__item--add {
    width: 368px;
    flex-shrink: 0;
    box-sizing: border-box;
  }

  &__item--dataset {
    margin-left: 16px;
  }

  &__item {
    position: relative;
    border: 1px solid rgba(0, 0, 0, 10%);

    .ant-select-selector {
      height: auto !important;
    }

    .ant-select-selection-item {
      padding-top: 4px !important;
      padding-bottom: 4px !important;
      font-style: 14px;
      line-height: 22px !important;
      white-space: pre-line;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .anticon {
      color: #666;
    }

    .handler {
      position: absolute;
      left: -26px;
      top: 0;
      display: none;
      padding: 18px 10px;
    }

    &:hover .handler {
      display: inline-flex;
    }
  }
}
