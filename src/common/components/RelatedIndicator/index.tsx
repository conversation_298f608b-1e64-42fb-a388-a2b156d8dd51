/**
 * 关联指标组件
 */

import React, { forwardRef, useImperativeHandle } from 'react';
import { DeleteOutlined, EditOutlined, HolderOutlined, PlusOutlined } from '@ant-design/icons';
import { useSetState } from 'ahooks';
import { Button, Checkbox, Form, Popconfirm, Row } from 'antd';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';
import { OptionItem } from '@/common/components/formula';
import PlanManageFacade from '@/_docplus/target/service/gateway/KpiPolicyQueryGatewayService';
import { SelectWithSearch } from '@/common/components/SelectWithSearch';
import { groupBy } from 'lodash';
import { IndicatorFormModal } from '@/common/components/IndicatorFormModal';
import { NUM_FORMAT_DEFAULT_VALUE } from '@/common/components/NumFormatForm';
import { DEFAULT_INDICATOR_LIST_PAGE_SIZE } from '@/common/constants';
import './style.less';

export interface IndicatorItemType {
  expression: string;
  [key: string]: any;
}

export interface RelatedIndicatorProps {
  // eslint-disable-next-line react/no-unused-prop-types, @typescript-eslint/no-unused-vars
  onChange?: (data: IndicatorItemType[]) => void;
  showDatasetButton?: boolean;
  /** 当前产品域 */
  productDomain?: string;
  /** 业务线 */
  business: string[];
  indicatorList: Array<
    OptionItem & {
      formatType: string;
      precision: string;
      thousandthShow: string;
      unitName: string;
      companyInput: string;
    }
  >;
  /** 删除状态 */
  isRemoveState?: boolean;
  [x: string]: any;
}

const IndicatorList = SortableContainer(({ children }) => (
  <div className="u-d-flex related-indicators">{children}</div>
));

const IndicatorListItemHandler = SortableHandle(() => (
  <HolderOutlined className="u-cursor-grab handler" />
));

const IndicatorListItem = SortableElement((props: any) => (
  <div className="u-bgc-fafafa u-br-4 u-py-9 u-px-16 u-mb-16 u-mr-16 u-flex-ai-center related-indicators__item">
    <IndicatorListItemHandler />

    {props.isRemoveState ? (
      <Checkbox
        checked={props.isChecked}
        className="u-mr-10"
        onClick={() => props?.onSeleted?.(!props.isChecked)}
      />
    ) : null}

    <SelectWithSearch
      value={props.expression}
      showSearch
      className="u-flex-1 u-mr-16"
      popupClassName="adjust-select"
      style={{ width: 30 }}
      placeholder="请选择指标"
      options={props.options}
      filterOption={(input, option) =>
        `${option?.label ?? ''}`.toLowerCase().includes(input.toLowerCase())
      }
      onChange={props.onIndicatorChange}
      onSearchRemote={props.onRemoteSearch}
    />

    <EditOutlined className="u-cursor-pointer u-ml-auto" onClick={props.onEdit} />

    <Popconfirm title="确认删除当前指标吗？" onConfirm={props.onDelete}>
      <DeleteOutlined className="u-cursor-pointer u-ml-16" />
    </Popconfirm>
  </div>
));

export interface RelatedIndicatorRef {
  /** 选择全部 */
  toggleSelectAll: () => void;
  /** 确定删除 */
  deleteMultiConfirm: () => number;
}

export const RelatedIndicator = forwardRef<RelatedIndicatorRef, RelatedIndicatorProps>(
  (props: RelatedIndicatorProps, ref) => {
    const [state, setState] = useSetState({
      dataImportList: [],
      showDatasetImportSelector: false,
      editFormIndex: null,
      editFromData: null as IndicatorItemType,
      selectedIndexList: [],
    });

    const form = Form.useFormInstance<{
      extraIndicators: any[];
    }>();
    const indicators = Form.useWatch('extraIndicators', form) || [];

    const onChange = (indicators: IndicatorItemType[]) => {
      form.setFieldsValue({
        extraIndicators: (indicators || []).map((o, i) => Object.assign(o, { sortNum: i })),
      });
    };

    const onRemoteSearch = async (value: string) => {
      if (value) {
        return PlanManageFacade.queryIndicatorList({
          businessGroup: props.businessGroup || 'C33_DIRECT',
          keyword: value,
          pageSize: DEFAULT_INDICATOR_LIST_PAGE_SIZE,
          pageStart: 0,
        }).then((res) => {
          const newGroup = groupBy(res.data, 'dataSetName');
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          return Object.keys(newGroup).map((groupKey) => ({
            label: '指标',
            options: (res?.data || []).map((item) => ({
              label: item.indicatorName,
              value: item.indicatorId,
              type: 'Indicator',
            })),
          }));
        });
      }
      return [];
    };

    useImperativeHandle(ref, () => ({
      toggleSelectAll: () => {
        let isSelectAll = false;
        if (state.selectedIndexList.length < indicators.length) {
          isSelectAll = true;
        }
        setState({
          selectedIndexList: isSelectAll ? indicators.map((_, index) => index) : [],
        });
      },
      deleteMultiConfirm: () => {
        if (state.selectedIndexList.length) {
          onChange(
            (indicators || []).filter((_, index) => !state.selectedIndexList.includes(index)),
          );
          setState({ selectedIndexList: [] });
        }
        return state.selectedIndexList.length;
      },
    }));

    return (
      <div className="related-indicators--wrapper">
        <IndicatorList
          useDragHandle
          useWindowAsScrollContainer
          axis="xy"
          helperClass="related-indicators__item-helper"
          onSortEnd={({ oldIndex, newIndex }) => {
            onChange(
              arrayMoveImmutable([...indicators], oldIndex, newIndex).map((o, i) =>
                Object.assign(o, { sortNum: i }),
              ),
            );
          }}
        >
          {indicators.map((indicator, index) => (
            <IndicatorListItem
              key={`item-${index}`}
              index={index}
              expression={
                indicator?.indicatorId ? indicator.indicatorId.replace('@', '') : undefined
              }
              options={props.indicatorList}
              isRemoveState={props.isRemoveState}
              onRemoteSearch={onRemoteSearch}
              isChecked={state.selectedIndexList.includes(index)}
              onSeleted={(checked: boolean) => {
                setState({
                  selectedIndexList: checked
                    ? [...state.selectedIndexList, index]
                    : state.selectedIndexList.filter((o) => o !== index),
                });
              }}
              onIndicatorChange={(value: string) => {
                const newIndicators = [...indicators];
                const indicator = props.indicatorList.find((o) => o.value === value);
                newIndicators[index] = {
                  ...newIndicators[index],
                  indicatorId: `${value}`,
                  name: indicator?.label,
                  resultFormat: NUM_FORMAT_DEFAULT_VALUE,
                  expression: {
                    expression: `@${value}`,
                    textExpression: `${indicator?.label}`,
                    type: 'COMMON',
                    varMap: {
                      [`@${value}`]: indicator?.label,
                    },
                  },
                  // formatType: indicator?.formatType,
                  // precision: indicator?.precision,
                };
                onChange(newIndicators);
              }}
              onEdit={() => {
                if (!indicators[index]) return;
                const editFromData = indicators[index];
                // if (!('formatType' in editFromData) && indicators[index]?.expression?.expression) {
                //   const indicator = props.indicatorList
                //     .find(o => `@${o.value}` === indicators[index]?.expression?.expression);
                //   editFromData.formatType = indicator?.formatType;
                //   editFromData.precision = indicator?.precision;
                // }
                setState({
                  editFromData,
                  editFormIndex: index,
                });
              }}
              onDelete={() => {
                const newIndicators = [...indicators];
                newIndicators.splice(index, 1);
                onChange(newIndicators);
              }}
            />
          ))}

          {!props.isRemoveState ? (
            <Row style={{ width: '100%', flexShrink: 0 }}>
              <Button
                type="dashed"
                className="related-indicators__item--add"
                icon={<PlusOutlined />}
                onClick={() => {
                  onChange(
                    [].concat(indicators, {
                      graphType: null,
                      sortNum: indicators.length,
                    }),
                  );
                }}
              >
                添加指标
              </Button>
            </Row>
          ) : null}
        </IndicatorList>

        <IndicatorFormModal
          open={state.editFormIndex !== null}
          productDomain={props.productDomain}
          formData={state.editFormIndex !== null ? state.editFromData : null}
          indicatorList={props.indicatorList}
          onConfirm={(newFormData) => {
            console.log('newFormData', newFormData);

            const newIndicators = [...indicators];
            const indicator = props.indicatorList.find((o) => o.value === newFormData.expression);
            newIndicators[state.editFormIndex] = {
              ...newFormData,
              indicatorId: `${newFormData.expression}`,
              name: indicator.label,
              expression: {
                expression: `@${newFormData.expression}`,
                textExpression: `${indicator.label}`,
                type: 'COMMON',
                varMap: {
                  [`@${newFormData.expression}`]: indicator.label,
                },
              },
            };
            onChange(newIndicators);
            setState({
              editFormIndex: null,
              editFromData: null,
            });
          }}
          onCancel={() => setState({ editFormIndex: null })}
        />
      </div>
    );
  },
);
