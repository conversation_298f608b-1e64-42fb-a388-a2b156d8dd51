import { Tree, Spin, Input, Button, message, Space } from 'antd';
import React, { useEffect, useState, useMemo } from 'react';
import moment, { Moment } from 'moment';
import { useDebounceFn } from 'ahooks';
import { monthListToStr, findLeafNodesByKey } from '@/common/utils';
import { queryObjectCircleTree } from '../service';
import styles from './index.module.less';

interface IProps {
  periodType?: string; // 周期类型
  periodValue?: Moment; // 单月政策周期
  periodValueMulti?: Moment[]; // 多月政策周期
  disabled?: boolean; // 是否禁用
}

interface CircleDataNode {
  name: string;
  path: string;
  children?: CircleDataNode[];
}

interface DataNode {
  title: string;
  key: string;
  children?: DataNode[];
}

const transformData = (treeNodes: CircleDataNode[] = []): DataNode[] => {
  return treeNodes.map(({ name, path, children }) => ({
    title: name,
    key: path,
    value: path,
    children: transformData(children),
  }));
};

const RuleSelector = (props: IProps) => {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);

  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  useEffect(() => {
    getTreeData();
  }, [props.periodType, props.periodValue, props.periodValueMulti]);

  const { run: getTreeData } = useDebounceFn(() => {
    try {
      const { periodType, periodValue, periodValueMulti } = props;
      let val = '';
      if (periodType === 'MONTH') {
        val = moment(periodValue).format('YYYYMM');
      }

      // 多月
      if (periodType === 'MULTI_MONTH') {
        val = monthListToStr([
          moment(periodValueMulti?.[0]).format('YYYYMM'),
          moment(periodValueMulti?.[1]).format('YYYYMM'),
        ])?.join(',');
      }

      if (val) {
        setLoading(true);
        queryObjectCircleTree({
          periodType: props.periodType,
          periodValue: val,
        }).then((res) => {
          const list = res?.data?.root ? [res?.data?.root] : [];
          setDataSource(transformData(list));
          setLoading(false);
        });
      }
    } catch (error) {
      message.error(error.errorMessage);
      setLoading(false);
    }
  });

  const getParentKey = (key, tree) => {
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    return parentKey;
  };

  const dataList = useMemo(() => {
    const list = [];
    const generateList = (data) => {
      for (let i = 0; i < data.length; i++) {
        const node = data[i];
        const { key, title } = node;
        list.push({
          key,
          title,
        });
        if (node.children) {
          generateList(node.children);
        }
      }
    };
    generateList(dataSource);
    return list;
  }, [dataSource]);

  const { run: handleSearchChange } = useDebounceFn((value) => {
    const newExpandedKeys = dataList
      .map((item) => {
        if (item.title === value) {
          return getParentKey(item.key, dataSource);
        }
        return null;
      })
      .filter((item, i, self): item is React.Key => !!(item && self.indexOf(item) === i));
    setExpandedKeys(newExpandedKeys);
    setSearchValue(value);
    setAutoExpandParent(true);
  });

  const handleSelectChange = (checkedKeys) => {
    const res = checkedKeys.filter((key) => key.endsWith('正式') || key.endsWith('外包'));
    props?.onChange(res);
  };

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  // const treeData = useMemo(() => {
  //   const loop = (data) => {
  //     return data.reduce((accumulator, node) => {
  //       // 检查当前节点是否包含关键词
  //       const matches = node.title === searchValue;

  //       // 递归筛选子节点
  //       const filteredChildren = loop(node.children);

  //       // 如果当前节点匹配，或者子节点中有匹配项，就将当前节点加入结果
  //       if (matches || filteredChildren.length > 0) {
  //         accumulator.push({
  //           ...node,
  //           children: matches ? node.children : filteredChildren, // 如果当前匹配，保留所有子节点
  //         });
  //       }

  //       return accumulator;
  //     }, []);
  //   };

  //   return !searchValue ? dataSource : loop(dataSource);
  // }, [searchValue, dataSource]);

  const treeData = useMemo(() => {
    const loop = (data) =>
      data.map((item) => {
        const title =
          item.title === searchValue ? (
            <span className={styles['matched-search-title']}>{searchValue}</span>
          ) : (
            <span>{item.title}</span>
          );
        if (item.children) {
          return { title, key: item.key, children: loop(item.children) };
        }

        return {
          title,
          key: item.key,
        };
      });

    return loop(dataSource);
  }, [searchValue, dataSource]);

  /**
   * 选中所有搜索结果的叶子节点
   */
  const checkAllSearchResult = () => {
    const searchResult = []; // 存储所有搜索结果的叶子节点
    const queue = [...dataSource];

    // 使用广度优先来遍历树结构
    while (queue.length > 0) {
      const currentNode = queue.shift(); // 取出队列的第一个节点

      if (currentNode.title === searchValue) {
        // 找到匹配的节点，获取所有叶子节点
        const leafs = findLeafNodesByKey(dataSource, currentNode.key);
        searchResult.push(...leafs.map((leaf) => leaf.key));
      } else {
        // 否则，将当前节点的子节点加入队列，继续遍历
        queue.push(...currentNode.children);
      }
    }

    // 与之前选中的选项合并
    let res = [...(props.value || []), ...searchResult];
    // 然后去重
    res = [...new Set(res)];
    // 触发更新
    handleSelectChange(res);
  };

  /**
   * 取消所有搜索结果的叶子节点
   */
  const cancelAllSearchResult = () => {
    const searchResult = []; // 存储所有搜索结果的叶子节点
    const queue = [...dataSource];

    // 使用广度优先来遍历树结构
    while (queue.length > 0) {
      const currentNode = queue.shift(); // 取出队列的第一个节点

      if (currentNode.title === searchValue) {
        // 找到匹配的节点，获取所有叶子节点
        const leafs = findLeafNodesByKey(dataSource, currentNode.key);
        searchResult.push(...leafs.map((leaf) => leaf.key));
      } else {
        // 否则，将当前节点的子节点加入队列，继续遍历
        queue.push(...currentNode.children);
      }
    }

    // 将这些节点从之前选中的选项中移除
    let res = props?.value?.filter((item) => !new Set(searchResult).has(item));
    // 然后去重
    res = [...new Set(res)];
    // 触发更新
    handleSelectChange(res);
  };

  if (loading) {
    return <Spin spinning={loading} />;
  }

  return (
    <div>
      <div className={styles['search-wrapper']}>
        <Space style={{ marginBottom: 10 }}>
          <Input.Search
            style={{ width: 400 }}
            placeholder="Search"
            onChange={(e) => handleSearchChange(e.target.value)}
          />
          {searchValue && (
            <Button type="primary" onClick={checkAllSearchResult}>
              选中所有{searchValue}
            </Button>
          )}
          {searchValue && (
            <Button type="primary" onClick={cancelAllSearchResult}>
              取消所有{searchValue}
            </Button>
          )}

          <Button onClick={() => props?.onChange([])}>全部取消</Button>
        </Space>
      </div>

      <Tree
        checkable
        onCheck={handleSelectChange}
        checkedKeys={props.value}
        disabled={props.disabled}
        onExpand={onExpand}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        treeData={treeData}
      />
    </div>
  );
};

export default RuleSelector;
