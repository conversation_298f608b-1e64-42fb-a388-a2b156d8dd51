import { Form, Button, Radio, Transfer, Tree } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import React, { FC } from 'react';
import { CommonUpload } from '@alife/mp-oss-upload';
import BDSelect from '@alife/mo-bd-select';
import { Moment } from 'moment';
import { BUCKET_PID, BUCKET_NAME } from '@/common/constants';
import RuleSelector from './ruleSelector';
import './index.less';

const UPLOAD_LABEL = '上传考核对象';

interface OptionType {
  label: string;
  value: string;
}

interface IProps {
  btnLabel?: string; // 上传按钮文案
  tempUrl?: string | Function; // 模板地址
  tempRemark?: string; // 模板说明
  extraOptions?: Array<OptionType>; // 除了自定义选项外的其他选项
  periodType?: string; // 周期类型
  periodValue?: Moment; // 单月政策周期
  periodValueMulti?: Moment[]; // 多月政策周期
  kpiPolicy: any;
  disabled: boolean;
}

const TabChild = (props: IProps) => {
  const {
    btnLabel = '上传考核对象',
    tempUrl = '',
    tempRemark = '',
    extraOptions = [],
    kpiPolicy,
  } = props;

  const defaultBlackOptions = kpiPolicy?.objectBlackList?.map((item) => ({
    label: item.name,
    value: item.id,
  }));
  const defaultWhiteOptions = kpiPolicy?.objectWhiteList?.map((item) => ({
    label: item.name,
    value: item.id,
  }));
  const handleDownload = (e) => {
    e.stopPropagation();
    if (typeof tempUrl === 'function') {
      tempUrl();
    } else {
      window.open(tempUrl);
    }
  };
  return (
    <div key="add-details">
      <Form.Item
        name="objectType"
        initialValue="CUSTOMIZE"
        label="圈选途径"
        rules={[{ required: true, message: '请选择圈选途径' }]}
      >
        <Radio.Group>
          <Radio value="CUSTOMIZE">自定义</Radio>

          {extraOptions.map((item) => (
            <Radio value={item.value}>{item.label}</Radio>
          ))}
        </Radio.Group>
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) => prevValues.objectType !== curValues.objectType}
      >
        {(form) => {
          const objectType = form.getFieldValue('objectType');
          if (objectType === 'CUSTOMIZE') {
            return (
              <Form.Item
                name="objectOssKey"
                label=" "
                colon={false}
                extra="支持扩展名：.xls .xlsx"
                rules={[{ required: true, message: `请${UPLOAD_LABEL}` }]}
              >
                <CommonUpload
                  className="assess_object"
                  bucketName={BUCKET_NAME}
                  ossConfigId={BUCKET_PID}
                  buttonText={btnLabel}
                  accept=".xls,.xlsx"
                  maxCount={1}
                  filePath="permanent/2147483647/kpi"
                  disabled={props.disabled}
                >
                  <div>
                    <Button icon={<UploadOutlined />}>{UPLOAD_LABEL}</Button>
                    <a className="assess_object_template" onClick={handleDownload}>
                      下载模版
                    </a>
                    {tempRemark && <span className="assess_object_template_remark"></span>}
                  </div>
                </CommonUpload>
              </Form.Item>
            );
          }
          if (objectType === 'RULE') {
            return (
              <>
                <Form.Item
                  name="objectRuleList"
                  label="圈选人员"
                  rules={[{ required: true, message: '请选择圈选人员' }]}
                >
                  <RuleSelector
                    periodType={props.periodType}
                    periodValue={props.periodValue}
                    periodValueMulti={props.periodValueMulti}
                    disabled={props.disabled}
                  />
                </Form.Item>
                <Form.Item name="objectBlackList" label="人员过滤">
                  <BDSelect
                    type="SALES"
                    style={{ width: '100%' }}
                    mode="multiple"
                    labelInValue
                    defaultOptions={defaultBlackOptions}
                    disabled={props.disabled}
                  />
                </Form.Item>
                <Form.Item name="objectWhiteList" label="人员追加">
                  <BDSelect
                    type="SALES"
                    style={{ width: '100%' }}
                    mode="multiple"
                    labelInValue
                    defaultOptions={defaultWhiteOptions}
                    disabled={props.disabled}
                  />
                </Form.Item>
              </>
            );
          }
          return null;
        }}
      </Form.Item>
    </div>
  );
};

export default TabChild;
