export const BUCKET_PID = {
  DAILY: 'd151c6356e433aad8',
  PRE: '9acce3598d31d5c2a',
  PROD: '9acce3598d31d5c2a',
};

export const BUCKET_NAME = {
  DAILY: 'kbcommprod-aldaily-wlcb1-oss-1',
  PRE: 'kbcommprod-zbprod-zb1-oss-1',
  PROD: 'kbcommprod-zbprod-zb1-oss-1',
};

export const PERIOD_FORMAT = 'YYYYMM';
export const PERIOD_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

export const DEFAULT_INDICATOR_LIST_PAGE_SIZE = 500;

// 明细指标类型（直营）
export const DETAIL_INDICATOR_TYPE_LIST_OPTIONS = [
  {
    label: '业绩总收入（新签团队口径-3充）',
    value: 'TOTAL_AMOUNT_SIGN',
  },
  {
    label: '业绩总收入（新签团队口径-1充）',
    value: 'TOTAL_AMOUNT_SIGN_ONE_CHARGE',
  },
  {
    label: '业绩总收入（运维团队口径-3充）',
    value: 'TOTAL_AMOUNT_MAINTAIN',
  },
  {
    label: '业绩总收入（运维团队口径-1充）',
    value: 'TOTAL_AMOUNT_MAINTAIN_ONE_CHARGE',
  },
  {
    label: '流水考核收入',
    value: 'PROCESS_ASSESS_AMOUNT',
  },
  {
    label: '团队违规系数',
    value: 'TEAM_VIOLATION',
  },
  {
    label: '团队商户投诉个数',
    value: 'TEAM_COMPLAINT_NUMBER',
  },
  {
    label: '上门拜访（新签团队）',
    value: 'DOOR_2_DOOR_VISIT',
  },
  {
    label: '电话邀约（新签团队）',
    value: 'TELEPHONE_VISIT_SIGN',
  },
  {
    label: '电话拜访（电销团队）',
    value: 'TELEPHONE_VISIT_MOBILE',
  },
  {
    label: '团队质检抽查得分',
    value: 'TEAM_QUALITY_INSPECTION',
  },
  {
    label: '团队KP建联且高德点亮数',
    value: 'TEAM_KP_AND_LIGHT_NUMBER',
  },
  {
    label: '团队新签旺铺收入',
    value: 'TEAM_NEW_SIGN_AMOUNT',
  },
  {
    label: '团队新签广告主首次充值金额',
    value: 'TEAM_NEW_ADS_CHARGE_AMOUNT',
  },
  {
    label: '年费新签门店数',
    value: 'NEW_SIGN_STORE_NUM',
  },
  {
    label: '新签广告主数',
    value: 'NEW_SIGN_ADVERTISER_NUM',
  },
  {
    label: '高价值leads新签广告主数',
    value: 'HIGH_VALUE_LEADS_NEW_SIGN_ADVERTISER_NUM',
  },
  {
    label: '转介绍KP线索提供人分润',
    value: 'CUSTOMER_REFERRAL_PROFIT_SHARING',
  },
  {
    label: '小二退款明细',
    value: 'STAFF_REFUND_DETAIL',
  },
  {
    label: '主管退款明细',
    value: 'LEADER_REFUND_DETAIL',
  },
  {
    label: '年费签约',
    value: 'SALES_DETAIL',
  },
  {
    label: '（单户/店）广告收入',
    value: 'C33_DIRECT_ADVERTISER_CHARGE',
  },
  {
    label: 'CPS佣金',
    value: 'C33_DIRECT_CPS_COMMISSION',
  },
  {
    label: '督导特殊调账',
    value: 'C33_DIRECT_SUPERVISE_SPECIAL_ADJUSTMENTS',
  },
  {
    label: '年费应续实续',
    value: 'C33_DIRECT_PROCESS_RENEW_RATE',
  },
  {
    label: '广告应充实充',
    value: 'C33_DIRECT_PROCESS_AD_RECHARGE_RATE',
  },
  {
    label: '广告消耗',
    value: 'C33_DIRECT_AD_BALANCE_COST',
  },
  {
    label: 'UE成本',
    value: 'C33_DIRECT_UE_COST',
  },
  {
    label: '非标收入',
    value: 'C33_DIRECT_FEIBIAO',
  },
];

// 明细指标类型（c33服务商）
export const AGENT_INDICATOR_TYPE_LIST_OPTIONS = [
  {
    label: '年费新签',
    value: 'C33_AGENT_NEW_SIGN',
  },
  {
    label: '年费续签',
    value: 'C33_AGENT_RENEW',
  },
  {
    label: '广告充值',
    value: 'C33_AGENT_AD_RECHARGE',
  },
  {
    label: '广告消耗',
    value: 'C33_AGENT_AD_BALANCE_COST',
  },
  {
    label: 'CPS',
    value: 'C33_AGENT_CPS',
  },
  {
    label: '零售水牌',
    value: 'C33_AGENT_AMAP_MALL_AD_AMT',
  },
  {
    label: '年费续签率明细',
    value: 'C33_AGENT_PROCESS_RENEW_RATE',
  },
  {
    label: '质量分明细',
    value: 'C33_AGENT_PROCESS_QUALITY_RATE',
  },
  {
    label: '广告应充实充明细',
    value: 'C33_AGENT_PROCESS_AD_RECHARGE_RATE',
  },
];

export const POLICY_TYPE_PARAMS = {
  C31: 'C31_AGENT',
  C33: 'C33_AGENT',
  C33_DIRECT: 'C33_DIRECT',
};

export const BUSINESS_DOMAIN = [
  {
    value: 'C33_AGENT',
    label: '高德商户通-服务商',
  },
  {
    value: 'C31_AGENT',
    label: '高德旺铺-服务商',
  },
];

export const POLICY_TYPE_ENUM = {
  C31: '旺铺',
  C33: '商户通',
  C33_DIRECT: '直营',
};