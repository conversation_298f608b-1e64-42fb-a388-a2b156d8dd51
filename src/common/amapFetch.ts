import afetch from '@alife/amap-fetch';
import { message } from 'antd';

export default (api: string, param: any = {}, options: any = {}) => {
  const params = {
    api,
    param,
    ...options,
  };

  return afetch(params)
    .then((res: any) => {
      if (res && (res.success || res?.data?.success || res?.data?.status === 'succeed')) {
        if (res.data && res.data.success === false) {
          return Promise.reject(res);
        }
        return Promise.resolve(res);
      }
      return Promise.reject(res);
    })
    .catch((err) => {
      if (err?.data?.resultMessage) {
        message.error(err?.data?.resultMessage);
      } else if (err?.result?.errorMsg) {
        message.error(err?.result?.errorMsg);
      } else if (err?.errorMessage) {
        message.info(err?.errorMessage);
      } else if (err.resultMessage) {
        message.error(err?.resultMessage);
      }
      return Promise.reject(err);
    });
}; 