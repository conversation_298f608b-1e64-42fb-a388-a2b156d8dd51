/* eslint-disable dot-notation */
import React from 'react';
import { setStoreContainerDefaultProps } from '@alife/kobe-store-next';
import { Result, Button } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { parse } from 'query-string';

export const getInitParam = () => parse(window.location.search);

setStoreContainerDefaultProps({
  initParam: getInitParam,
  async beforeSetup(globalStore) {
    // eslint-disable-next-line
    globalStore['_beforeSetupTime'] = Date.now();
  },

  async afterSetup(globalStore) {
    // eslint-disable-next-line
    console.log(
      'global store 初始化耗时:',
      Date.now() - globalStore._beforeSetupTime,
    );
  },

  onSetupError(error, globalStore) {
    // 忽略上报接口错误（已经在 接口盘中监控）
    if (error.name === 'FetchError') return;
    console.log('other error', error, globalStore);
  },
  renderLoading: () => (
    <div
      style={{
        minHeight: '500px',
        height: '100%',
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <LoadingOutlined
        style={{
          fontSize: 30,
        }}
      />
    </div>
  ),
  renderError: (error, reload) => (
    <Result
      title="出现错误"
      subTitle={
        (typeof error === 'string' ? error : error?.message) || '请稍候重试'
      }
      extra={
        <Button type="primary" key="console" onClick={reload}>
          点击重试
        </Button>
      }
    />
  ),
});
