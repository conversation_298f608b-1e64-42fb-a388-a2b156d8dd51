import fetch from '@ali/kb-fetch';
import { message } from 'antd';

export type { GwRequestOption } from '@ali/kb-fetch';

export interface GwResult<T = any> {
  success?: boolean;
  resultMessage?: string;
  retry?: boolean;
  errorCode?: string;
  errorMsg?: string;
  model?: T;
  data: T;
  extInfo?: { [index: string]: any };
}

export class GwError extends Error {
  name = 'BusinessError';
  result: GwResult;
  constructor(result: GwResult) {
    super(result.errorMsg);
    this.result = result;
  }
}

export const mockResponseHandler = (res: GwError) => res;

// eslint-disable-next-line no-restricted-syntax
export default (
  options: Parameters<typeof fetch>[0],

  // docplus 生成的请求函数都会传第二个参数
  // 但是其实不需要，这里随便给个形参
  // 防止 TS 报错
  _?: any,
) => fetch(options)
  .then((res: any) => {
    if (!res.success && !res.succ) {
      throw new GwError(res);
    }

    return res;
  })
  .catch((e) => {
    console.log('request error', JSON.stringify(e));
    message.error(e?.response?.result?.errorMsg ||
          e?.response?.result?.resultMessage ||
          e?.result?.resultMessage ||
          e?.message ||
          e?.errorMessage ||
          '系统错误，请稍候再试');
    throw e;
  });
