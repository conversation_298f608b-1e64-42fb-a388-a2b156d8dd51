/** 主色 */
@c-main: #1a66ff;

/** 主色禁用色 */
// @c-main-disable: #FDB191;

// /** 主色-亮色 */
// @c-main-lighter: #FF803D;

/** 副色 */
@c-sub: #3986FE;

/** 副色-亮色 */
@c-sub-lighter: #4D97FF;

/** 边框色 */
@c-border: #E4E7ED;

/** 背景色 */
@c-bg: #F2F4F5;

/** 页面背景色 */
@c-bg-page: #F5F5F5;

/** 高亮背景色 */
@c-bg-main: #FFF7F0;

/** 灰色背景 */
@c-bg-gray: #FAFAFA;

/** 字体主色 */
@c-text-main: #292D33;

/** 字体副色 */
@c-text-sub: #515966;

/** 字体灰色 */
@c-text-gray: #9097A3;

@c-text-silver: #C7CCD4;

/** 红色 */
@c-red: #EB1D00;

/** 绿色 */
@c-green: #05AE7E;

/** 默认字体 */
@ff-normal: PingFangSC-Regular, -apple-system, 'Open Sans', 'Helvetica Neue', sans-serif;

/** 圆角大小 */
@sz-radius: 8px;

/** 页面边框距离 */
@sz-side: 24px;

/** 模块间距 */
@sz-block-gap: 20px;
