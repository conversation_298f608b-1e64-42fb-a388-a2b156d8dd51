@import url("./variables.less");

/* stylelint-disable property-no-unknown */

/*
 * u-fs 字体大小
 * u-fs-icon 为匹配字体大小的iconfont字体大小
 * u-c 字体颜色
 * u-ta 字体对齐
 * u-mt 上边距
 * u-mr 右边距
 * u-mb 下边距
 * u-ml 左边距
 * u-number 数字使用阿里巴巴Sans
 */

.fs-map() {
  fs: 12;
  fs: 14;
  fs: 18;
  fs: 20;
  fs: 22;
  fs: 24;
  fs: 26;
  fs: 28;
  fs: 30;
  fs: 32;
  fs: 36;
}

each(.fs-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      font-size: @v * 1px !important;
    }
  }

);

.ml-map() {
  ml: -24;
  ml: 5;
  ml: 8;
  ml: 10;
  ml: 12;
  ml: 15;
  ml: 16;
  ml: 20;
  ml: 24;
  ml: 40;
  // ml: auto;
}

each(.ml-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      margin-left: @v * 1px !important;
    }
  }

);

.mb-map() {
  mb: -24;
  mb: -16;
  mb: -2;
  mb: -1;
  mb: 0;
  mb: 12;
  mb: 16;
  mb: 20;
  mb: 24;
  mb: 64;
  // mb: auto;
}

each(.mb-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      margin-bottom: @v * 1px !important;
    }
  }

);

.mr-map() {
  mr: 0;
  mr: 5;
  mr: 10;
  mr: 12;
  mr: 15;
  mr: 16;
  mr: 20;
  mr: 24;
  mr: -24;
  // mr: auto;
}

each(.mr-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      margin-right: @v * 1px !important;
    }
  }

);

.mt-map() {
  mt: -20;
  mt: -2;
  mt: -1;
  mt: 0;
  mt: 6;
  mt: 8;
  mt: 12;
  mt: 16;
  mt: 20;
  mt: 24;
  mt: 26;
}

each(.mt-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      margin-top: @v * 1px !important;
    }
  }

);

.mx-map() {
  mx: 5;
  mx: 20;
  mx: 24;
}

each(.mx-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      margin-left: @v * 1px !important;
      margin-right: @v * 1px !important;
    }
  }

);

.my-map() {
  my: 0;
  my: 12;
  my: 20;
  my: 24;
}

each(.my-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      margin-top: @v * 1px !important;
      margin-bottom: @v * 1px !important;
    }
  }

);

.m-map() {
  m: 15;
  m: 16;
  m: 20;
  m: 24;
  m: -24;
}

each(.m-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      margin: @v * 1px !important;
    }
  }

);

.pl-map() {
  pl: 0;
  pl: 4;
  pl: 10;
  pl: 15;
  pl: 16;
  pl: 20;
  pl: 24;
  pl: -24;
  // pl: auto;
}

each(.pl-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      padding-left: @v * 1px !important;
    }
  }

);

.pr-map() {
  pr: 0;
  pr: 15;
  pr: 16;
  pr: 20;
  pr: 24;
  pr: -24;
  // pr: auto;
}

each(.pr-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      padding-right: @v * 1px !important;
    }
  }

);

.pt-map() {
  pt: 15;
  pt: 16;
  pt: 20;
  pt: 24;
  pt: -24;
}

each(.pt-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      padding-top: @v * 1px !important;
    }
  }

);

.pb-map() {
  pb: 0;
  pb: 16;
  pb: 20;
  pb: 24;
}

each(.pb-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      padding-bottom: @v * 1px !important;
    }
  }

);

.p-map() {
  p: 0;
  p: 5;
  p: 15;
  p: 16;
  p: 20;
  p: 24;
  p: -24;
}

each(.p-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      padding: @v * 1px !important;
    }
  }

);

.px-map() {
  px: 0;
  px: 15;
  px: 16;
  px: 20;
  px: 24;
  px: -24;
}

each(.px-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      padding-left: @v * 1px !important;
      padding-right: @v * 1px !important;
    }
  }

);

.py-map() {
  py: 0;
  py: 9;
  py: 15;
  py: 16;
  py: 20;
  py: 24;
  py: -24;
}

each(.py-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      padding-top: @v * 1px !important;
      padding-bottom: @v * 1px !important;
    }
  }

);

.lh-map() {
  lh: 28;
  lh: 30;
  lh: 32;
  lh: 36;
}

each(.lh-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      line-height: @v * 1px !important;
    }
  }

);

.u-ml--10 {
  margin-left: -10px !important;
}

.u-ml-auto {
  margin-left: auto !important;
}

.u-mr-auto {
  margin-right: auto !important;
}

.u-fs-icon-24 {
  font-size: 20px !important;
}

.u-fw-bold {
  font-weight: bold !important;
}

.u-fw-normal {
  font-weight: normal !important;
}

/** tc: TextColor 文字颜色 */
.u-c-main {
  color: @c-text-main  !important;
}

.u-c-sub {
  color: @c-text-sub  !important;
}

.u-c-gray {
  color: @c-text-gray  !important;
}

.u-c-silver {
  color: @c-text-silver  !important;
}

.u-c-white {
  color: white !important;
}

.u-c-black {
  color: black !important;
}

/** 文字主色高亮 */
.u-c-hl-main {
  color: @c-main  !important;
}

/** ta TextAlign 文字对齐 */
.u-ta-right {
  text-align: right !important;
}

.u-ta-center {
  text-align: center !important;
}

.u-lh-1 {
  line-height: 1 !important;
}

.u-number {
  /* stylelint-disable-next-line max-line-length */
  font-family: "Alibaba Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif !important;
}

.u-ellipsis {
  flex: 1;
  width: 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/** 0.5像素的边框 */
.u-b-top,
.u-b-bottom {
  position: relative;
}

.u-b-top::before,
.u-b-bottom::after {
  position: absolute;
  width: 100%;
  content: " ";
  left: 0;
  border-bottom: 1px solid #E4E7ED;
  transform: scaleY(0.5);
}

.u-b-top::before {
  top: 0;
}

.u-b-bottom::after {
  bottom: 0;
}

.u-b-left,
.u-b-right {
  position: relative;
}

.u-b-left::before,
.u-b-right::after {
  position: absolute;
  height: 100%;
  content: " ";
  border-left: 1px solid #E4E7ED;
  transform: scaleX(0.5);
}

.u-b-left::before {
  left: 0;
}

.u-b-right::after {
  right: 0;
}

.u-button-border {
  position: relative;

  &-after,
  &::after {
    position: absolute;
    content: " ";
    left: -50%;
    top: -50%;
    margin-top: -1px;
    margin-left: -1px;
    width: 200%;
    height: 200%;
    border-width: 1px;
    border-style: solid;
    border-color: #8D93A0;
    transform: scale(0.5);
    border-radius: 10px;
  }
}

/** 通用函数 */
#common() {

  /** 生成箭头 */
  .arrow {
    position: relative;

    &::before {
      position: absolute;
      content: " ";
      top: -10px;
      left: 105px;
      height: 15px;
      width: 15px;
      background: white;
      transform: rotate(45deg);
    }
  }
}

.u-d-flex {
  display: flex;
}

.u-flex-1 {
  flex: 1;
}

.u-jc-center {
  justify-content: center;
}

.u-flex-dir-col {
  display: flex;
  flex-direction: column;
}

.u-flex-ai-center {
  display: flex;
  align-items: center;
}

.u-flex-jc-between {
  display: flex;
  justify-content: space-between;
}

.u-d-inline {
  display: inline;
}

.u-d-none {
  display: none;
}

.u-trend-gt:extend(.u-number) {
  color: @c-red;
}

.u-trend-lt:extend(.u-number) {
  color: @c-green;
}

.u-trend-eq:extend(.u-number) {
  color: @c-text-main;
}

.u-w-100_ {
  width: 100% !important;
}

.u-safe-bottom {
  /* stylelint-disable-next-line function-no-unknown */
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
}

.u-of-hidden {
  overflow: hidden !important;
}

.brd-map() {
  brd: 4;
  brd: 12;
  brd: 16;
}

each(.brd-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      border-radius: @v * 1px !important;
    }
  }

);

.u-bgc-f7f9fc {
  background-color: #F7F9FC;
}

.u-bgc-fafafa {
  background-color: #FAFAFA;
}

.u-f-24 {
  font-size: 24px;
  line-height: 36px;
}

.u-bs-border {
  box-sizing: border-box !important;
}

.u-compare--gt {
  color: #EB1D00;
}

.u-compare--lt {
  color: #05AE7E;
}

.u-cursor-pointer {
  cursor: pointer;
}

.u-cursor-grab {
  cursor: grab;

  &:target {
    cursor: grabbing;
  }
}

.u-cursor-grab,
.u-cursor-pointer {
  &:hover {
    color: @c-main;
  }
}

.br-map() {
  br: 2;
  br: 4;
}

each(.br-map(), .(@v, @k, @i) {
    .u-@{k}-@{v} {
      border-radius: @v * 1px !important;
    }
  }

);
