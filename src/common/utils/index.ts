/* eslint-disable max-len */
import * as moment from 'moment';
import { EnumObject } from 'ts-enum-object';
import { CalRuleGroupInfoDTO } from '@/_docplus/target/types/gateway';
import type { DeepPartial } from '@/_docplus/target/request/client';
import { IENV, ENV_ENUM } from '@/common/Types/env';
import { parseMatrixDataToForm, parseMatrixFormToData } from '../components/matrix/helper';
import {
  parseRuleGroupDataToForm,
  parseRuleGroupFormToData,
} from '../components/rule-group/helper';
import { OptionsEnmuras, CurValues } from '../Types';
import type { ValidatorRule } from 'rc-field-form/es/interface';

export * from './form';

export const getPerid = function (s: string) {
  let result: string;
  switch (s) {
    case 'pre':
      result = moment().subtract(1, 'month').format('YYYY-MM');
      break;
    case 'next':
      result = moment().add(1, 'month').format('YYYY-MM');
      break;
    default:
      result = moment().format('YYYY-MM');
  }
  return result;
};

/**
 * @param name 令箭名称
 * @param data 提交数据
 * @param type 令箭类型
 * @param method HTTP请求方式
 */
export const sendGoldKey = (
  name,
  data: { [k: string]: any } = {},
  type: 'CLK' | 'SLD' | 'EXP' | 'OTHER' = 'CLK',
  method: 'GET' | 'POST' = 'GET',
) => {
  const w: any = window;
  if (!w.goldlog) {
    throw new Error('aplus-sdk未加载');
  }
  if (!w.goldlog.record) {
    throw new Error('埋点错误');
  }
  let urlQuery = '';
  try {
    urlQuery = Object.keys(data).reduce((t, v, k) => {
      if (k === 1) {
        // eslint-disable-next-line no-param-reassign
        t += `=${data[t]}`;
      }
      return `${t}&${v}=${data[v]}`;
    });
  } catch (error) {
    urlQuery = '';
    throw new Error('埋点数据异常');
  }
  w.goldlog.record(name, type, urlQuery, method);
};

/**
 * 判断环境  默认判断生产
 *
 * @export
 * @param {IENV} [env]
 * @returns {boolean}
 */
export function checkENV(env?: IENV): boolean {
  const ENV: IENV = env || ENV_ENUM.PRODUCTION;
  const w: any = window;
  return w.$ENV === ENV;
}

/**
 *
 *
 * @param {File} file
 */
export const readerFile = async (file: File) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function (e) {
      const base64 = e.target?.result;
      resolve(base64);
    };
    reader.onprogress = () => {
      // console.log('onprogress', e)
    };
    reader.onabort = (e) => {
      reject(e);
    };
    reader.onerror = (e) => {
      reject(e);
    };
  });

/**
 *
 *
 * @param {URL} params
 */
export const getUrlToken = (name, str) => {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
  const r = str.substr(1).match(reg);
  if (r != null) return decodeURIComponent(r[2]);
  return null;
};

/**
 *
 *
 * @param {IFRAME URL} params
 */
export const getIframeURL = (name) => {
  const array = [];
  const str = document?.referrer;
  const parastr = str?.split('?')?.[1];
  const arr = parastr?.split('&');
  for (let i = 0; i < arr?.length; i++) {
    array[arr?.[i]?.split('=')?.[0]] = arr?.[i]?.split('=')?.[1];
  }
  return array[name] || '';
};

// FORM转数据 上传
export const handleRuleFormToData = (
  data,
  allIndicList?: any[],
): DeepPartial<CalRuleGroupInfoDTO> => {
  switch (data.showType) {
    // 矩阵
    case 'ARRAY':
      const result = parseMatrixDataToForm(
        data.arrayExpressionObj.arrayExpressionObj,
        allIndicList,
      );
      return {
        ruleGroupType: 'ARRAY',
        // 公式
        expressionRuleGroup: null,
        // 矩阵
        arrayRuleGroup: {
          indicatorX: result.indicatorX,
          indicatorXName: result.indicatorXName,
          arrayExpressions: result.arrayExpressions,
          upperLimit: data?.arrayExpressionObj?.upperLimit,
          lowLimit: data?.arrayExpressionObj?.lowLimit,
        },
      };
    case 'RULES': // 规则组
      return {
        ruleGroupType: 'RULES',
        expressionRuleGroup: null,
        arrayRuleGroup: null,
        // 规则组
        multiCriteriaRuleGroup: {
          rules: parseRuleGroupDataToForm(data.indicatorRules, allIndicList),
        },
      };
    default: // 公式
      return {
        ruleGroupType: 'EXPRESSION',
        expressionRuleGroup: data.expression,
        arrayRuleGroup: null,
      };
  }
};

// 数据转FORM 回显
export const handleRuleDataToForm = (data: DeepPartial<CalRuleGroupInfoDTO>) => {
  switch (data?.ruleGroupType) {
    case 'ARRAY':
      return {
        ...data,
        showType: 'ARRAY',
        arrayExpressionObj: {
          upperLimit: data?.arrayRuleGroup?.upperLimit,
          lowLimit: data?.arrayRuleGroup?.lowLimit,
          arrayExpressionObj: parseMatrixFormToData(data.arrayRuleGroup),
        },
      };
    case 'RULES':
      return {
        ...data,
        showType: 'RULES',
        indicatorRules: parseRuleGroupFormToData(data.multiCriteriaRuleGroup?.rules),
      };
    default:
      return {
        expression: data.expressionRuleGroup,
        showType: 'EXPRESSION',
      };
  }
};

// 正则校验【公式】
/**
 *@params
 *optionsEnmura // 当前公式组件的枚举值
 *curValue // 当前公式组件的value值
}
 */
export const regularCheck = (optionsEnmura: OptionsEnmuras[], curValue: CurValues) => {
  // console.log('%c 🍈 optionsEnmura: ', 'font-size:20px;background-color: #33A5FF;color:#fff;', optionsEnmura, curValue)
  // 如果指标前后面跟了数字或者.直接报错
  // if (Object.keys(curValue.varMap)?.length > 0) {
  //   let indc = Promise.resolve()
  //   Object.keys(curValue.varMap).map((curItem) => {
  //     const regObj = new RegExp(`${curItem}[0-9.@$#]|[0-9.]${curItem}`)
  //     if (regObj.test(curValue.expression)) {
  //       indc = Promise.reject(new Error(`请检查'${curValue.varMap[curItem]}'前后的符号`))
  //     }
  //     return null
  //   })
  //   return indc
  // }
  // 运算符连续的情况
  if (/[+\-*/.]{2,}/.test(curValue?.expression)) {
    return Promise.reject(new Error('输入的符号不能连续'));
  }
  // 出现空括号
  if (/\(\)/.test(curValue?.expression)) {
    return Promise.reject(new Error('括号内不能为空'));
  }
  // (括号右边不能出现运算符
  if (/\([+\-*/.]/.test(curValue?.expression)) {
    return Promise.reject(new Error("'('后面不能跟运算符或."));
  }
  // 除号后面不能是0
  if (/\/[0]/.test(curValue?.expression) && !/\/[0]\.(?!0+$)\d+/.test(curValue?.expression)) {
    return Promise.reject(new Error("'/'后面不能是0"));
  }
  //  (括号左边不能直接跟数字
  if (/[\d.]\(/.test(curValue?.expression)) {
    return Promise.reject(new Error("'('前面字符错误"));
  }
  // )左边不能直接跟操作符
  if (/[+\-*/.]\)/.test(curValue?.expression)) {
    return Promise.reject(new Error("')'后面不能跟运算符或."));
  }
  // )右边不能直接跟数字
  if (/\)[\d.@$#]/.test(curValue?.expression)) {
    return Promise.reject(new Error('请检查)左右的运算符号'));
  }
  // 出现括号不配对的情况
  const stack = [];
  for (let i = 0; i < curValue?.expression?.length; i++) {
    if (curValue?.expression.charAt(i) === '(') {
      stack.push('(');
    } else if (curValue?.expression.charAt(i) === ')') {
      if (stack.length > 0) {
        stack.pop();
      } else {
        return Promise.reject(new Error('左右括号不匹配'));
      }
    }
  }
  if (stack.length !== 0) {
    return Promise.reject(new Error('左右括号不匹配'));
  }
  // 运算符不能出现在首末位置
  const { expression } = curValue || {};
  // eslint-disable-next-line no-useless-escape
  const startOrEndWithOperators = /^[+\*/.]|[+\-\*/.]$/.test(expression);
  const startWithInvalidMinus =
    /^-/.test(expression) /* 以负号开头 */ &&
    !/^-\d/.test(expression); /* 且负号后面跟着的不是数字 */
  if (startOrEndWithOperators || startWithInvalidMinus) {
    return Promise.reject(new Error('符号不能出现在首末位置'));
  }
  // 如果当前指标不是数值类型则返回错误
  if (curValue?.varMap && Object.keys(curValue?.varMap)?.length > 0) {
    let flag = Promise.resolve();
    Object.keys(curValue?.varMap).map((curItem) => {
      const curIndicatorInfo = optionsEnmura.find((opItem) => opItem.value === curItem.slice(1));
      // 生成正则规则  @#$或者e开头选了日期 前面和后面紧跟有数字或者.【实际上这样是检查错误的表达式】
      const regStr = curItem?.startsWith('$')
        ? new RegExp(`\\${curItem}[0-9.@$#&e(]|[0-9.)]\\${curItem}`)
        : new RegExp(`${curItem}[0-9.@$#&e(]|[0-9.)]${curItem}`);
      const regObj = new RegExp(regStr);
      // 生成正则规则  @#$或者e开头选了日期 前面和后面紧跟有数字或者.【实际上这样是检查错误的表达式】 executePeriodVar(
      const regWrapDateInc = new RegExp(`[0-9.)]executePeriodVar\\(${curItem}`);
      if (curIndicatorInfo?.configType && curIndicatorInfo?.configType !== 'NUMBERIC') {
        flag = Promise.reject(new Error(`'${curIndicatorInfo?.label}'不是数值类型`));
        return null;
      }
      if (regObj.test(curValue?.expression)) {
        flag = Promise.reject(new Error(`请检查'${curValue?.varMap[curItem]}'前后的符号`));
      }
      if (regWrapDateInc.test(curValue?.expression)) {
        flag = Promise.reject(new Error(`请检查'${curValue?.varMap[curItem]}'前后的符号`));
      }
      return null;
    });
    return flag;
  }
  return Promise.resolve();
};

/** 防抖函数 */
export const debounce = (fn, delay = 500) => {
  let timer = null;
  return (...param) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn(...param);
    }, delay);
  };
};

/** 构建的Enum对象转表单选项 */
export function formatEnumToOptions(enumOptions: EnumObject<any>) {
  return enumOptions.items().map((o) => ({ label: o.label, value: o.value }));
}

export function serilizeParam(str) {
  const query = {};
  str.split('&').forEach((equation) => {
    const [key, value] = equation.split('=');
    if (value !== undefined) {
      query[decodeURIComponent(key)] = decodeURIComponent(value);
    }
  });
  return query;
}
// 获取页面url数据
export function getUrlParam() {
  let query = {};
  query = serilizeParam(window.location.search.slice(1));
  if (window.location.hash) {
    const index = window.location.hash.indexOf('?');
    if (index > -1) {
      query = {
        ...query,
        ...serilizeParam(window.location.hash.slice(index + 1)),
      };
    }
  }
  return query;
}

export const listNotEmptyValidator: ValidatorRule['validator'] = (_, value) => {
  if (value?.length) {
    return Promise.resolve();
  }
  return Promise.reject('请配置至少一条');
};

// startViewTransition 是 Chrome v111 引入的新特性，可以创建无比丝滑的转场动画
// https://developer.chrome.com/docs/web-platform/view-transitions/?hl=zh-cn#standardization_status
export const startViewTransitionHelper = (cb: () => any) => {
  if (typeof document.startViewTransition === 'function') {
    // 有 startViewTransition 的浏览器就用上
    document.startViewTransition(() => cb());
  } else {
    // 没有 startViewTransition 的浏览器：直接调用，跳过
    cb();
  }
};

/**
 * 月份数组转换成字符串
 * [x, y] => '199303,199304,199305,...,202401,202402,202403'
 */
export const monthListToStr = (monthList = []) => {
  const arr = [];
  const endDate = moment(monthList[monthList.length - 1], 'YYYYMM');
  let startDate = moment(monthList[0], 'YYYYMM');

  while (startDate.isSameOrBefore(endDate)) {
    arr.push(startDate.format('YYYYMM'));
    startDate = startDate.add(1, 'month');
  }

  return arr;
};

/**
 * 字符串转换成月份数组
 * '199303,199304,199305,...,202401,202402,202403' => [x, y]
 */
export const strToMonthList = (str = '') => {
  const arr = str.split(',');
  const endDate = moment(arr[arr.length - 1]);
  const startDate = moment(arr[0]);

  return [startDate, endDate];
};

export const ensureIsStringWithContnets = (x: any): string | null => {
  if (typeof x !== 'string' || !x) {
    return null;
  }
  return x;
};

export const extractServerErrorMessage = (
  res: any,
  fallback = '系统开小差了，请稍后再试',
): string => {
  const internalExtract = (res: any): string | null => {
    if (!res) {
      return null;
    }

    return (
      internalExtract(res?.result) ||
      internalExtract(res?.resp) ||
      internalExtract(res?.response) ||
      ensureIsStringWithContnets(res?.message) ||
      ensureIsStringWithContnets(res?.resultMessage) ||
      ensureIsStringWithContnets(res?.errorMessage) ||
      ensureIsStringWithContnets(res?.msgInfo) ||
      ensureIsStringWithContnets((res as any)?.msg)
    );
  };

  return internalExtract(res) || fallback;
};

export const sleep = (time = 100) =>
  new Promise<void>((resolve) => {
    setTimeout(resolve, time);
  });

/**
 * 一直循环校验，直到校验通过
 */
export const lazyValidate = (check: () => boolean) =>
  new Promise<void>(async (resolve) => {
    while (!check()) {
      await sleep();
    }
    resolve();
  });

export const generateRandomString = (length = 19) => {
  let result = '';

  // 首位随机生成 1-9
  result += Math.floor(Math.random() * 9) + 1;

  // 后面的 18 位随机生成 0-9
  for (let i = 1; i < length; i++) {
    result += Math.floor(Math.random() * 10);
  }

  return result;
};

/**
 * 通过某个节点的 key 找到这个节点下的所有叶子节点
 * @param tree
 * @param targetKey
 * @returns
 */
export function findLeafNodesByKey(tree, targetKey) {
  const leafNodes = [];

  // 递归查找目标节点
  function helper(nodes) {
    for (let node of nodes) {
      // 检查当前节点的 key 是否匹配
      if (node.key === targetKey) {
        // 当前节点匹配，查找叶子节点
        findLeafs(node); // 收集所有叶子节点
      } else if (node.children) {
        // 继续递归子节点
        helper(node.children);
      }
    }
  }

  // 收集叶子节点的函数
  function findLeafs(node) {
    // 检查当前节点是否有子节点
    if (!node.children || node.children.length === 0) {
      // 没有子节点，它是叶子节点
      leafNodes.push(node);
    } else {
      // 递归查找子节点
      node.children.forEach((child) => findLeafs(child));
    }
  }

  // 从顶层节点开始查找
  helper(tree);
  return leafNodes;
}
