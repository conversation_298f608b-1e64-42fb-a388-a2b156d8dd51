import { FormProps, notification } from 'antd';

export const handleFinishFailed: FormProps['onFinishFailed'] = (errorInfo) => {
  const resultMessageList = errorInfo?.errorFields?.flatMap(item => item.errors);
  const commonMessageSuffix = '，请稍后重试，如果此问题一直出现，请联系前端技术排查';
  if (errorInfo?.outOfDate) {
    resultMessageList.unshift(
      `表单状态同步失败${commonMessageSuffix}`,
    );
  }
  if (!resultMessageList.length) {
    resultMessageList.push(
      `未知错误${commonMessageSuffix}`,
    );
    console.error(errorInfo);
  }

  notification.error({
    message: '请检查以下表单错误',
    description: (
      <ul style={{ paddingLeft: '1em' }}>
        {Array.from(new Set(resultMessageList)).map(item => <li>{item}</li>)}
      </ul>
    ),
  });
};
