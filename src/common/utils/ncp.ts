import { env, Ncp } from '@ali/prometheus-fetch';
import { apiUrl } from '../Types/env';

const openNcp = Ncp.create({
  baseConfig: {
    appName: 'alsc-kpi-reach',
    host: apiUrl[env],
  },
  modules: ['axe'],
});
const getConfig = () =>
// ...
  ({
  // ...
  });

openNcp.setDefaultConfig(getConfig);

export const upLoadFilePrivate = async (base64Data, extension = 'pdf') => {
  const data = base64Data.replace(/^data:.+base64,/, '');
  // eslint-disable-next-line no-return-await
  return await openNcp.module.axe.fetch({
    service: 'SmallFileService', // 对应接口所属 service
    method: 'upLoadFilePrivate', // 对应接口对应方法名
    params: { fileBase64: data, extension }, // 接口参数
  });
};
