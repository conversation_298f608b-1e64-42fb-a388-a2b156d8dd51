/* tslint:disable */
/* eslint-disable */
// Generated using typescript-generator version 2.32.889 on 2024-06-21 11:51:44.

export interface CreateIndicatorRequest extends BaseRequest {
  indicatorDTO: IndicatorDTO;
  operatorInfo: OperatorInfo;
}

export interface GatewayResult<T> extends Serializable {
  success: boolean;
  resultMessage: string;
  resultCode: string;
  data: T;
  extInfo: { [index: string]: any };
}

export interface ModifyIndicatorRequest extends BaseRequest {
  indicatorDTO: IndicatorDTO;
  operatorInfo: OperatorInfo;
}

export interface QueryIndicatorRequest extends BaseRequest {
  indicatorIds: string[];
  keyWord: string;
  indicatorTypes: string[];
  indicatorSourceFrom: string;
  includeIndicatorRule: boolean;
  includeIndicatorDatasource: boolean;
  businessGroup: string;
  productDomain: string;
  businessLine: string;
  pageNum: number;
  pageSize: number;
  operatorInfo: OperatorInfo;
  timeType: string;
}

export interface QueryIndicatorResponse extends ToString {
  indicatorDTOList: IndicatorDTO[];
  pageInfoDTO: PageInfoDTO;
}

export interface QueryIndicatorDatasourceRequest extends BaseRequest {
  datasourceType: string;
  sourceFrom: string;
  pageNum: number;
  pageSize: number;
  operatorInfo: OperatorInfo;
}

export interface QueryIndicatorDatasourceResponse extends ToString {
  datasourceDTOList: IndicatorDatasourceDTO[];
  pageInfoDTO: PageInfoDTO;
}

export interface FetchDataRequest extends BaseRequest {
  scene: string;
  indicatorId: string;
  targetIds: string[];
  periodType: string;
  /**
   * @deprecated
   */
  periodValue: string;
  periodValues: string[];
  ds: string;
  onlySelf: boolean;
  analyzeGroup: boolean;
  groupValueType: string;
  groupAnalyzeValues: GroupAnalyzeValueDTO[];
  pageNum: number;
  pageSize: number;
  operatorInfo: OperatorInfo;
}

export interface FetchDataResponse extends ToString {
  indicatorId: string;
  data: IndicatorTargetDataDTO[];
  pageNoInfoDTO: PageNoInfoDTO;
}

export interface PolicyConfigQueryGatewayRequest extends BaseRequest {
  periodType: string;
  periodValue: string;
}

export interface KpiPolicyConfigDTO extends ToString {
  appeal: boolean;
  appealStartTime: string;
  appealEndTime: string;
  showCommission: boolean;
  flexibleBill: boolean;
  remark: string;
  periodType: string;
  periodValue: string;
}

export interface IndicatorListQueryGatewayRequest extends BaseRequest {
  keyword: string;
  businessGroup: string;
  pageSize: number;
  pageStart: number;
}

export interface IndicatorQueryResponse extends ToString {
  indicatorId: string;
  indicatorName: string;
  label: string;
}

export interface KpiPolicyQueryGatewayRequest extends BaseRequest {
  kpiPolicyId: number;
  kpiPolicyName: string;
  kpiPolicyStatus: string;
  periodType: string;
  periodValue: string;
  pageNo: number;
  pageSize: number;
  businessGroup: string;
}

export interface KpiPolicyDetailResponse extends ToString {
  kpiPolicy: KpiPolicyDTO;
  kpiItem: CalItemInfoDTO;
  commItem: CalItemInfoDTO;
  indicatorRenameConfigDTOList: IndicatorRenameConfigDTO[];
}

export interface KpiPolicyListResponse extends ToString {
  kpiPolicyDTOList: KpiPolicyDTO[];
  pageInfoDTO: PageInfoDTO;
}

export interface KpiPolicyEditGatewayRequest extends BaseRequest {
  kpiPolicyId: string;
  kpiPolicyName: string;
  kpiPolicyDesc: string;
  policyOssKey: string;
  detailIndicatorType: string;
  detailIndicatorTypeList: string[];
  periodType: string;
  periodValue: string;
  objectOssKey: string;
  businessGroup: string;
  objectType: string[];
  indicatorRenameConfig: IndicatorRenameConfigDTO[];
  kpiItem: CalItemInfoDTO;
  commItem: CalItemInfoDTO;
  preDependencyPolicyList: string[];
  objectRuleList: string[];
  objectWhiteList: string[];
  objectBlackList: string[];
}

export interface KpiPolicyManageGatewayRequest extends BaseRequest {
  bizAction: string;
  kpiPolicyId: string;
  businessGroup: string;
}

export interface PolicyConfigEditGatewayRequest extends BaseRequest {
  periodType: string;
  periodValue: string;
  appeal: boolean;
  showCommission: boolean;
  flexibleBill: boolean;
  appealStartTime: string;
  appealEndTime: string;
  remark: string;
  businessGroup: string;
}

/**
 * 创建工单请求
 * <AUTHOR>
 * @version : CreateWorkOrderRequest.java, v 0.1 2023/10/8 10:32 下午 yuhan Exp $
 */
export interface CreateWorkOrderGatewayRequest extends BaseRequest {
  /**
   * 工单名称
   */
  name: string;
  /**
   * 工单类型
   */
  bizType: string;
  /**
   * 业务域
   */
  businessGroup: string;
  /**
   * 工单描述
   */
  description: string;
  /**
   * 申诉内容
   */
  content: { [index: string]: string };
}

/**
 * <AUTHOR>
 * @version : ModifyWorkOrderGateWayRequest, v 0.1 2024-01-17 7:57 PM chengyu.yxm Exp $
 */
export interface ModifyWorkOrderGateWayRequest extends BaseRequest {
  /**
   * 工单id
   */
  workOrderId: string;
  /**
   * 申诉内容
   */
  content: { [index: string]: string };
}

/**
 * 工单详情查询请求
 * <AUTHOR>
 * @version : WorkOrderDetailQueryRequest.java, v 0.1 2023/10/8 10:32 下午 yuhan Exp $
 */
export interface WorkOrderDetailQueryGatewayRequest extends BaseRequest {
  /**
   * 工单id
   */
  workOrderId: string;
}

export interface WorkOrderVO {
  workOrderId: number;
  type: string;
  name: string;
  description: string;
  businessGroup: string;
  bizType: string;
  createUserId: string;
  createUserName: string;
  status: string;
  statusName: string;
  content: { [index: string]: any };
  gmtCreated: Date;
  gmtModified: Date;
  gmtFinish: Date;
  extInfo: { [index: string]: string };
}

/**
 * 工单楼下查询请求
 * <AUTHOR>
 * @version : WorkOrderBatchQueryRequest.java, v 0.1 2023/10/8 10:32 下午 yuhan Exp $
 */
export interface WorkOrderQueryGatewayRequest extends BaseRequest {
  /**
   * @see WorkOrderBizTypeEnum
   */
  bizType: string;
  /**
   * 工单id
   */
  workOrderId: string;
  /**
   * 创建人
   */
  createUserId: string;
  /**
   * 状态
   */
  status: string[];
  /**
   * 开始日期 格式：yyyyy-MM-dd
   */
  startDate: string;
  /**
   * 结束日期 格式：yyyyy-MM-dd
   */
  endDate: string;
  /**
   * start
   */
  pageNum: number;
  /**
   * size
   */
  pageSize: number;
}

export interface PageResult<T> extends Result<T> {
  pageNoInfoDTO: PageNoInfoDTO;
}

/**
 * <AUTHOR>
 * @version : ModifyConfigResponse, v 0.1 2024-01-17 5:16 PM chengyu.yxm Exp $
 */
export interface ModifyConfigResponse extends ToString {
  /**
   * 修改配置
   */
  modifyColumnConfig: ModifyColumnConfigVO[];
}

export interface DownloadPredictResultRequest extends BaseRequest {
  targetPredictPlanId: string;
}

export interface StartTargetPredictPlanRequest extends BaseRequest {
  targetPredictPlanId: string;
}

export interface TargetPredictPlanObjectRequest extends BaseRequest {
  targetPredictPlanId: string;
  ossKey: string;
  financialTargetValue: string;
  aliCodes: string[];
  extInfo: { [index: string]: string };
}

export interface QuantileValueSaveRequest extends BaseRequest {
  periodType: string;
  periodValue: string;
  businessLine: string;
  predictPlanId: string;
  quantileValues: QuantileValueDTO[];
}

export interface TargetPredictPlanQueryRequest extends BaseRequest {
  predictPlanId: string;
}

export interface TargetPredictPlanDTO extends ToString {
  predictPlanId: string;
  predictPlanName: string;
  periodType: string;
  periodValue: string;
  businessGroup: string;
  businessLine: string;
  businessLineDesc: string;
  financialTargetValue: string;
  predictFinish: boolean;
  ossKey: string;
}

export interface TargetPredictQueryRequest extends BaseRequest {
  businessLine: string;
  periodType: string;
  periodValue: string;
  predictPlanId: string;
}

export interface QuantileValueDTO extends ToString {
  indicatorId: string;
  groupIndicatorId: string;
  indicatorName: string;
  dims: QuantileDim[];
  value: number;
  count: number;
}

export interface TargetDataInfoQueryRequest extends BaseRequest {
  planId: string;
  indicatorId: string;
  pageNo: number;
  pageSize: number;
}

export interface TargetPlanDataInfoQueryResponse extends ToString {
  pageInfoDTO: PageInfoDTO;
  targetDataDetailModelInfoList: TargetDataDetailModelInfo[];
}

export interface TargetPlanModelBizRequest extends BaseRequest {
  planId: string;
  businessGroup: string;
  businessLine: string;
  periodType: string;
  periodValue: string;
  conditions: string[];
  objectId: string;
  ds: string;
}

export interface TargetPlanModelDTO extends ToString {
  targetPlanInfo: TargetPlanInfoDTO;
  indicatorInfos: TargetIndicatorDTO[];
  targetPlanDataModels: TargetDataModelDTO[];
}

export interface TargetPlanInfoQueryRequest extends BaseRequest {
  planId: string;
  planName: string;
  businessGroup: string;
  businessLine: string;
  periodType: string;
  periodValue: string;
  pageNo: number;
  pageSize: number;
}

export interface TargetPlanInfoQueryResponse extends ToString {
  pageInfoDTO: PageInfoDTO;
  targetPlanInfoDTOList: TargetPlanInfoDTO[];
}

export interface AddTargetPlanRequest extends BaseRequest {
  planName: string;
  businessGroup: string;
  businessLine: string;
  periodType: string;
  periodValue: string;
  creatorId: string;
  ossKey: string;
  targetIndicatorConfigRequestList: TargetIndicatorConfigRequest[];
}

export interface AddTargetPlanDTO extends ToString {
  planId: string;
}

export interface DownloadTargetPlanDataRequest extends BaseRequest {
  planId: string;
  userId: string;
  planName: string;
  indicatorIds: string[];
  businessGroup: BusinessGroupEnum;
  businessLine: BusinessLineEnum;
}

export interface DownloadTargetPlanResponse extends BaseModel {
  planId: string;
  downloadUrl: string;
  fileName: string;
  ossKey: string;
  message: string;
}

export interface EditTargetPlanRequest extends BaseRequest {
  planId: string;
  planName: string;
  creatorId: string;
  ossKey: string;
  targetIndicatorConfigRequestList: TargetIndicatorConfigRequest[];
}

export interface EditTargetPlanDTO extends ToString {
  planId: string;
}

export interface ManageTargetPlanRequest extends BaseRequest {
  planId: string;
  bizAction: string;
}

export interface TargetPlanInfoDTO extends ToString {
  planId: string;
  planName: string;
  businessGroup: string;
  businessLine: string;
  periodType: string;
  periodValue: string;
  startTime: Date;
  endTime: Date;
  planStatus: string;
  creatorId: string;
  creatorName: string;
  updateId: string;
  gmtCreate: Date;
  ossKey: string;
  ossUrl: string;
}

export interface DownloadKpiReportRequest extends BaseRequest {
  planIds: string[];
  periodValue: string;
  userInfo: KpiReportUserInfo;
}

export interface KpiReportInfoQueryRequest extends BaseRequest {
  productDomain: ProductDomainEnum;
  businessGroup: BusinessGroupEnum;
  businessLine: BusinessLineEnum;
  userId: string;
  periodValue: string;
  userInfo: KpiReportUserInfo;
}

export interface KpiReportInfoVO {
  showBill: boolean;
  canComplain: boolean;
  policyFiles: string;
  policyDesc: string;
  policyName: string;
  policyId: string;
  relateAchievements: KpiReportAchievementVO[];
  reports: KpiReportPlanInfoVO[];
  policyType: string;
  policyRelatedMonths: string[];
  notification: KpiReportNotificationVO;
}

export interface KpiReportSimplePlanInfoVO {
  planId: string;
  planName: string;
}

export interface PlanIndicatorQueryRequest extends BaseRequest {
  planInstanceIds: string[];
  productDomain: string;
  businessGroup: string;
  businessLine: string;
  userId: string[];
  periodType: string;
  periodValue: string;
}

export interface KpiReportIndicatorVO {
  indicatorId: string;
  value: string;
  name: string;
}

export interface IndicatorDTO extends ToString {
  indicatorId: string;
  name: string;
  description: string;
  projectName: string;
  tableName: string;
  tableColumn: string;
  tableExtraColumn: ColumnInfoDTO[];
  tableDimColumn: ColumnInfoDTO[];
  businessGroup: string;
  productDomain: string;
  businessLine: string;
  indicatorType: string;
  indicatorRules: IndicatorRuleDTO[];
  domainType: string;
  configType: string;
  indicatorValueType: string;
  measureUnit: string;
  sourceType: string;
  status: string;
  formatType: string;
  classificationLabel: string;
  indicatorCode: string;
  dataScale: string;
  thousandthsShow: string;
  timeType: string;
  datasourceId: string;
  datasource: IndicatorDatasourceDTO;
  createId: string;
  modifiedId: string;
  gmtCreate: Date;
  gmtModified: Date;
  createName: string;
  modifiedName: string;
  extInfo: { [index: string]: string };
}

export interface OperatorInfo extends ToString {
  operatorId: string;
  universalId: string;
  coffeeId: string;
  operatorName: string;
  workNo: string;
  operatorType: string;
}

export interface BaseRequest extends ToString {
  requestId: string;
  extInfos: { [index: string]: string };
}

export interface Serializable {}

export interface PageInfoDTO extends ToString {
  hasMore: boolean;
  nextPageNo: number;
  pageSize: number;
  totalCount: number;
  totalPage: number;
  nextStart: number;
  currentPageNo: number;
}

export interface ToString extends Serializable {}

export interface IndicatorDatasourceDTO extends ToString {
  datasourceId: string;
  name: string;
  description: string;
  type: string;
  dataType: string;
  sourceFrom: string;
  schemeName: string;
  tableName: string;
  columnConfig: IndicatorDatasourceColumnConfigDTO;
  status: string;
  createId: string;
  modifiedId: string;
  gmtCreate: Date;
  gmtModified: Date;
  createName: string;
  modifiedName: string;
}

export interface GroupAnalyzeValueDTO extends ToString {
  targetId: string;
  groupValue: string;
  groupDimValues: GroupDimDTO[];
}

export interface IndicatorTargetDataDTO extends ToString {
  targetId: string;
  targetName: string;
  indicatorId: string;
  periodType: string;
  periodValue: string;
  ds: string;
  value: string;
  extraValues: { [index: string]: string };
  dimValues: { [index: string]: string };
  productDomain: string;
  businessLine: string;
}

export interface PageNoInfoDTO extends ToString {
  hasMore: boolean;
  nextPageNo: number;
  pageSize: number;
  totalCount: number;
  totalPage: number;
  nextStart: number;
  currentPageNo: number;
}

export interface KpiPolicyDTO extends ToString {
  policyId: number;
  policyName: string;
  policyType: string;
  policyDesc: string;
  detailIndicatorType: string;
  detailIndicatorTypeList: string[];
  policyStatus: CommPolicyStatusEnum;
  gmtEffect: Date;
  gmtExpire: Date;
  gmtCreate: Date;
  gmtModified: Date;
  commDurationUnit: CommDurationUnitEnum;
  creator: string;
  modifier: string;
  policyOssKey: string;
  policyOssUrl: string;
  objectOssKey: string;
  objectOssUrl: string;
  objectType: string[];
  canEdit: boolean;
  periodType: string;
  periodValue: string;
}

export interface CalItemInfoDTO extends ToString {
  itemId: string;
  sortNum: number;
  itemCode: string;
  itemName: string;
  itemType: string;
  level: number;
  description: string;
  ruleGroupInfo: CalRuleGroupInfoDTO;
  resultFormat: ExpressionResultFormatDTO;
  calItems: CalItemInfoDTO[];
  extraIndicators: ExtraIndicatorInfoDTO[];
}

export interface IndicatorRenameConfigDTO extends ToString {
  indicatorId: string;
  name: string;
  rename: string;
  sortNum: number;
}

export interface ModifyColumnConfigVO extends ToString {
  key: string;
  label: string;
  type: string;
  modifyType: string;
  defaultValue: any;
  isRequired: boolean;
  selectionList: any[];
}

export interface QuantileDim {
  indicatorId: string;
  alias: string;
  sortNum: number;
  dimValue: string;
  displayValue: string;
}

export interface TargetDataDetailModelInfo extends ToString {
  targetDataInfo: TargetDataInfoDTO;
  targetDataDetailInfo: TargetDataDetailInfoDTO;
}

export interface TargetIndicatorDTO extends ToString {
  planId: string;
  indicatorId: string;
  indicatorName: string;
}

export interface TargetDataModelDTO extends ToString {
  targetDataInfo: TargetDataInfoDTO;
  targetDataDetailInfos: TargetDataDetailInfoDTO[];
}

export interface TargetIndicatorConfigRequest extends BaseRequest {
  granularity: string;
  targetIndicators: TargetIndicatorRequest[];
}

export interface BaseModel extends ToString {
  deleteTag: DeleteTagEnum;
  env: string;
  createName: string;
  modifiedName: string;
  gmtCreate: Date;
  gmtModified: Date;
  extInfo: { [index: string]: string };
}

export interface KpiReportUserInfo extends ToString {
  identity: string;
  userType: string;
  jobId: string;
  leaf: boolean;
}

export interface KpiReportAchievementVO {
  code: string;
  name: string;
  bizScene: string;
  downloadBizScene: string;
}

export interface KpiReportPlanInfoVO {
  planInstanceId: string;
  orgId: string;
  orgName: string;
  productDomain: string;
  businessDomain: string;
  businessDomainDesc: string;
  businessLine: string;
  businessLineDesc: string;
  businessLevel: string;
  managerId: string;
  managerName: string;
  managerNickName: string;
  planId: string;
  planName: string;
  planStartTime: string;
  planEndTime: string;
  dataDt: string;
  showUpdateTimeFlag: string;
  isHistory: string;
  updateTime: string;
  totalValueType: string;
  totalValue: string;
  totalValueName: string;
  totalValueShow: string;
  planDesc: string;
  momT: string;
  momH: string;
  dodH: string;
  policyFile: string;
  targetStatus: string;
  resultStatus: string;
  processRate: string;
  upperLimit: string;
  lowerLimit: string;
  notice: string;
  routeNewPlat: string;
  reportFormula: KpiReportFormulaDTO;
  type: string;
  merged: number;
  configId: string;
  personScoreMergeDescVO: PersonScoreMergeDescVO;
  updateLogs: InstanceUpdateLogVO[];
  tags: PlanObjectTagVO[];
  moduleList: KpiReportInfoModuleVO[];
  keyIndicators: ReportIndicatorGroupVO[];
  normalIndicatorGroups: ReportIndicatorGroupVO[];
  itemVariables: KpiReportFormulaVO[];
  tableName: string;
}

export interface KpiReportNotificationVO extends ToString {
  complainNote: string;
}

export interface ColumnInfoDTO extends ToString {
  name: string;
  field: string;
}

export interface IndicatorRuleDTO extends ToString {
  ruleCondition: IndicatorRuleConditionDTO;
  gmtCreate: Date;
  gmtModified: Date;
  ruleVersion?: string;
}

export interface IndicatorDatasourceColumnConfigDTO extends ToString {
  dataColumns: ColumnInfoDTO[];
  targetIdFieldName: string;
  targetTypeFieldName: string;
  targetNameFieldName: string;
  periodTypeFieldName: string;
  periodValueFieldName: string;
  dsFieldName: string;
}

export interface GroupDimDTO extends ToString {
  groupDimIndicatorId: string;
  dimValue: string;
}

export interface CalRuleGroupInfoDTO extends ToString {
  ruleGroupType: string;
  expressionRuleGroup: StandardExpressionRuleGroupDTO;
  multiCriteriaRuleGroup: MultiCriteriaRuleGroupDTO;
  arrayRuleGroup: ArrayRuleGroupDTO;
}

export interface ExpressionResultFormatDTO extends ToString {
  formatType: string;
  precision: number;
}

export interface ExtraIndicatorInfoDTO extends ToString {
  indicatorId: string;
  name: string;
  sortNum: number;
  resultFormat: ExpressionResultFormatDTO;
  periodType: string;
  periodValue: string;
}

export interface Result<T> extends ApiResult {
  value: T;
  retry: boolean;
}

export interface TargetDataInfoDTO extends ToString {
  planInstanceId: string;
  planId: string;
  targetDataStatus: string;
  granularity: string;
  objectId: string;
  objectName: string;
  managerId: string;
  managerName: string;
  parentObjectId: string;
  extInfo: { [index: string]: string };
}

export interface TargetDataDetailInfoDTO extends ToString {
  id: number;
  planId: string;
  planInstanceId: string;
  indicatorId: string;
  processIndicatorId: string;
  sourceValue: string;
  currentValue: string;
}

export interface TargetIndicatorRequest extends BaseRequest {
  indicatorId: string;
  indicatorValueType: string;
}

export interface KpiReportFormulaDTO {
  name: string;
  formulaType: string;
  commonFormula: KpiReportCommonFormulaDTO;
  matrix: KpiReportMatrixDTO;
}

export interface PersonScoreMergeDescVO {
  scoreDescription: string;
  configId: string;
  ruleList: PersonScoreRuleVO[];
}

export interface InstanceUpdateLogVO {
  ds: string;
  description: string;
  scoreChange: string;
}

export interface PlanObjectTagVO {
  bizType: string;
  tagCode: string;
  tagName: string;
  tagDesc: string;
}

export interface KpiReportInfoModuleVO {
  moduleId: string;
  moduleName: string;
  moduleDesc: string;
  moduleInstanceId: string;
  moduleValueType: string;
  weight: string;
  value: string;
  valueShow: string;
  canGetNotFitReason: string;
  diagnosisId: string;
  diagnosisName: string;
  upperLimit: string;
  lowerLimit: string;
  momT: string;
  momH: string;
  dodH: string;
  moduleFormula: KpiReportFormulaVO;
  itemList: KpiReportInfoItemVO[];
  keyIndicators: ReportIndicatorVO[];
  normalIndicatorGroups: ReportIndicatorGroupVO[];
  itemVariables: KpiReportFormulaVO[];
  updateScore: string;
  timeEffInfo: TimeEffectivenessVO;
}

export interface ReportIndicatorGroupVO {
  groupId: string;
  groupName: string;
  indicatorList: ReportIndicatorVO[];
}

export interface KpiReportFormulaVO {
  name: string;
  formulaType: string;
  guaranteeDesc: string;
  commonFormula: KpiReportCommonFormulaVO;
  matrix: KpiReportMatrixVO;
  multiRuleGroup: KpiReportMultiRuleGroupVO;
  valueGapMsg: string;
  forecastValueMsg: string;
  warningInfo: KpiIndicatorWarningInfoVO;
}

export interface IndicatorRuleConditionDTO extends ToString {
  ruleType: string;
  qlRuleCondition: QLRuleConditionDTO;
  sqlRuleCondition: SQLRuleConditionDTO;
  detailSQLRuleCondition: DetailSQLRuleConditionDTO;
  groupIndicatorRuleCondition: GroupIndicatorRuleConditionDTO;
  groupDataRuleCondition: GroupDataRuleConditionDTO;
}

export interface StandardExpressionRuleGroupDTO extends ToString {
  expression: ExpressionDTO;
  upperLimit: ExpressionDTO;
  lowLimit: ExpressionDTO;
}

export interface MultiCriteriaRuleGroupDTO extends ToString {
  rules: MultiCriteriaRuleDTO[];
}

export interface ArrayRuleGroupDTO extends ToString {
  indicatorX: ExpressionDTO;
  indicatorXName: string;
  arrayExpressions: ArrayExpressionDTO[];
  upperLimit: ExpressionDTO;
  lowLimit: ExpressionDTO;
}

export interface ApiResult extends ToString {
  success: boolean;
  errorCode: string;
  errorMsg: string;
  extInfos: { [index: string]: string };
  fail: boolean;
}

export interface KpiReportCommonFormulaDTO {
  express: string;
  items: string[];
  itemInfo: { [index: string]: FormulaItemInfo };
}

export interface KpiReportMatrixDTO {
  matrixType: string;
  variableInfo: CommonItem[];
  head: CommonItem[];
  data: { [index: string]: TableDataItem }[];
}

export interface PersonScoreRuleVO {
  target: string;
  type: string;
  planName: string;
  calculateType: string;
  description: string;
  job: string;
}

export interface KpiReportInfoItemVO {
  itemId: string;
  itemInstanceId: string;
  itemName: string;
  itemValue: string;
  itemValueShow: string;
  itemDesc: string;
  upperLimit: string;
  lowerLimit: string;
  canDrill: string;
  diagnosisId: string;
  diagnosisName: string;
  itemVariables: KpiReportFormulaVO[];
  keyIndicators: KpiReportModuleKeyIndicatorVO[];
  indicatorList: KpiReportInfoIndicatorVO[];
  updateScore: string;
  timeEffInfo: TimeEffectivenessVO;
}

export interface ReportIndicatorVO {
  indicatorId: string;
  indicatorName: string;
  indicatorValue: string;
  indicatorSourceValue: string;
  indicatorDesc: string;
  momT: string;
  dodH: string;
  graphType: string;
  trendValues: string[];
  timeEffInfo: TimeEffectivenessVO;
}

export interface TimeEffectivenessVO {
  isRealTime: string;
  isDataUpdate: string;
  dataUpdateTime: string;
}

export interface KpiReportCommonFormulaVO {
  express: string;
  items: string[];
  itemInfo: { [index: string]: KpiReportCommonFormulaVO$FormulaItemInfo };
}

export interface KpiReportMatrixVO {
  matrixType: string;
  variableInfo: KpiReportMatrixVO$CommonItem[];
  head: KpiReportMatrixVO$CommonItem[];
  data: { [index: string]: KpiReportMatrixVO$TableDataItem }[];
}

export interface KpiReportMultiRuleGroupVO {
  rules: MultiCriteriaRuleVO[];
}

export interface KpiIndicatorWarningInfoVO {
  currentValue: string;
  forecastValue: string;
  inDanger: string;
  warningMsg: string;
}

export interface QLRuleConditionDTO extends ToString {
  ruleGroupType: string;
  expressionRuleGroup: StandardExpressionRuleGroupDTO;
}

export interface SQLRuleConditionDTO extends ToString {
  expression: string;
}

export interface DetailSQLRuleConditionDTO extends ToString {
  expression: string;
}

export interface GroupIndicatorRuleConditionDTO extends ToString {
  groupIndicatorAggType: string;
  periodType: string;
  groupTargetType: string;
  groupTarget: string[];
  groupRuleDim: GroupRuleDimDTO[];
  groupValueType: string;
  sortType: string;
}

export interface GroupDataRuleConditionDTO extends ToString {
  groupIndicatorAggType: string;
  periodType: string;
  groupRuleDim: GroupRuleDimDTO[];
  groupValueType: string;
}

export interface ExpressionDTO extends ToString {
  varMap: { [index: string]: string };
  expression: string;
  type: string;
}

export interface MultiCriteriaRuleDTO extends ToString {
  name: string;
  priority: number;
  ruleConditions: MultiCriteriaRuleConditionDTO[];
  ruleConditionRelationType: string;
  resultExpressionType: string;
  expressionRuleGroup: StandardExpressionRuleGroupDTO;
  arrayRuleGroup: ArrayRuleGroupDTO;
}

export interface ArrayExpressionDTO extends ToString {
  condition: ArrayExpressionConditionDTO;
  expression: ExpressionDTO;
  position: number;
}

export interface FormulaItemInfo {
  code: string;
  name: string;
  value: string;
  formula: string;
  canDrill: string;
  child: FormulaItemInfo[];
}

export interface CommonItem {
  code: string;
  name: string;
}

export interface TableDataItem {
  value: string;
  hitTarget: string;
}

export interface KpiReportModuleKeyIndicatorVO {
  indicatorInstanceId: string;
  indicatorId: string;
  indicatorName: string;
  indicatorValue: string;
  indicatorSourceValue: string;
  graphType: string;
  indicatorDesc: string;
  configType: string;
}

export interface KpiReportInfoIndicatorVO {
  indicatorId: string;
  indicatorInstanceId: string;
  indicatorName: string;
  indicatorDesc: string;
  indicatorValue: string;
  indicatorSourceValue: string;
  canGetDetail: string;
  configType: string;
  diagnosisId: string;
  diagnosisName: string;
}

export interface KpiReportCommonFormulaVO$FormulaItemInfo {
  code: string;
  name: string;
  value: string;
  formula: string;
  canDrill: string;
  child: KpiReportCommonFormulaVO$FormulaItemInfo[];
  valueCalculateType: string;
  valueComponents: KpiIndicatorComponentVO[];
}

export interface KpiReportMatrixVO$CommonItem {
  code: string;
  name: string;
}

export interface KpiReportMatrixVO$TableDataItem {
  value: string;
  hitTarget: string;
}

export interface MultiCriteriaRuleVO {
  name: string;
  ruleConditionRelationType: string;
  ruleConditions: MultiCriteriaRuleConditionVO[];
  resultType: string;
  commonFormula: KpiReportCommonFormulaVO;
  matrix: KpiReportMatrixVO;
  upperLimit: string;
  lowerLimit: string;
}

export interface GroupRuleDimDTO extends ToString {
  groupDimIndicatorId: string;
  isAssistant: boolean;
  dimAliasCode: string;
}

export interface MultiCriteriaRuleConditionDTO extends ToString {
  leftLabel: ValueExpressionDTO;
  rightLabel: ValueExpressionDTO;
  ruleOperator: string;
}

export interface ArrayExpressionConditionDTO extends ToString {
  leftValue: ValueExpressionDTO;
  rightValue: ValueExpressionDTO;
  leftOperator: string;
  rightOperator: string;
}

export interface KpiIndicatorComponentVO {
  value: string;
  period: string;
}

export interface MultiCriteriaRuleConditionVO {
  formulaText: string;
  variables: Item[];
}

export interface ValueExpressionDTO extends ToString {
  type: string;
  expression: ExpressionDTO;
  values: string[];
}

export interface Item {
  id: string;
  name: string;
  value: string;
  valueCalculateType: string;
  valueComponents: KpiIndicatorComponentVO[];
}

/**
 * <AUTHOR>
 * @version $Id: IndicatorGatewayService.java, v 0.1 2023年10月18日 17:46 朴勤 Exp $
 */
export interface IndicatorGatewayService {
  createIndicator(arg0: CreateIndicatorRequest): Promise<GatewayResult<boolean>>;

  modifyIndicator(arg0: ModifyIndicatorRequest): Promise<GatewayResult<boolean>>;

  queryIndicatorsByCondition(
    arg0: QueryIndicatorRequest,
  ): Promise<GatewayResult<QueryIndicatorResponse>>;
}

/**
 * <AUTHOR>
 * @version $Id: IndicatorDatasourceGatewayService.java, v 0.1 2023年10月18日 17:31 朴勤 Exp $
 */
export interface IndicatorDatasourceGatewayService {
  searchIndicatorDatasourceByCondition(
    arg0: QueryIndicatorDatasourceRequest,
  ): Promise<GatewayResult<QueryIndicatorDatasourceResponse>>;
}

/**
 * 指标数据查询服务
 * <AUTHOR>
 * @version : IndicatorDataGatewayService, v 0.1 2024-01-17 2:32 PM chengyu.yxm Exp $
 */
export interface IndicatorDataGatewayService {
  fetchData(arg0: FetchDataRequest): Promise<GatewayResult<FetchDataResponse>>;
}

/**
 * <AUTHOR>
 * @version KpiPolicyQueryGatewayService:1.0
 * @date 2023/9/25 20:24
 */
export interface KpiPolicyQueryGatewayService {
  queryConfig(arg0: PolicyConfigQueryGatewayRequest): Promise<GatewayResult<KpiPolicyConfigDTO>>;

  queryIndicatorList(
    arg0: IndicatorListQueryGatewayRequest,
  ): Promise<GatewayResult<IndicatorQueryResponse[]>>;

  queryPolicyDetail(
    arg0: KpiPolicyQueryGatewayRequest,
  ): Promise<GatewayResult<KpiPolicyDetailResponse>>;

  queryPolicyList(
    arg0: KpiPolicyQueryGatewayRequest,
  ): Promise<GatewayResult<KpiPolicyListResponse>>;
}

/**
 * 绩效政策管理网关类
 * <AUTHOR>
 * @version KpiPolicyManageGatewayService:1.0
 * @date 2023/8/29 15:20
 */
export interface KpiPolicyManageGatewayService {
  copy(arg0: KpiPolicyEditGatewayRequest): Promise<GatewayResult<KpiPolicyDetailResponse>>;

  /**
   * 生成计算项ID
   * @return
   */
  generateItemId(): Promise<GatewayResult<string>>;

  manage(arg0: KpiPolicyManageGatewayRequest): Promise<GatewayResult<string>>;

  save(arg0: KpiPolicyEditGatewayRequest): Promise<GatewayResult<number>>;

  saveConfig(arg0: PolicyConfigEditGatewayRequest): Promise<GatewayResult<boolean>>;
}

/**
 * 绩效战报网关类
 * <AUTHOR>
 * @version WorkOrderGatewayService:1.0
 * @date 2023/10/08 15:20
 */
export interface WorkOrderGatewayService {
  /**
   * 创建工单
   * @param request
   * @return
   */
  createWorkOrder(request: CreateWorkOrderGatewayRequest): Promise<GatewayResult<string>>;

  /**
   * 修改工单
   * @param request
   * @return
   */
  modifyWorkOrder(request: ModifyWorkOrderGateWayRequest): Promise<GatewayResult<string>>;

  /**
   * 查询工单配置
   * @return
   */
  queryConfig(): Promise<GatewayResult<{ [index: string]: any }>>;

  /**
   * 查询工单详情
   * @param request
   * @return
   */
  queryWorkOrderDetail(
    request: WorkOrderDetailQueryGatewayRequest,
  ): Promise<GatewayResult<WorkOrderVO>>;

  /**
   * 查询工单列表
   * @param request
   * @return
   */
  queryWorkOrderList(
    request: WorkOrderQueryGatewayRequest,
  ): Promise<GatewayResult<PageResult<WorkOrderVO[]>>>;

  /**
   * 查询工单 修改配置
   * @param request
   * @return
   */
  queryWorkOrderModifyConfig(
    request: WorkOrderDetailQueryGatewayRequest,
  ): Promise<GatewayResult<ModifyConfigResponse>>;
}

/**
 * <AUTHOR>
 * @version TargetPredictManageGatewayService:1.0
 * @date 2024/3/12 20:53
 */
export interface TargetPredictManageGatewayService {
  downloadPredictResult(arg0: DownloadPredictResultRequest): Promise<GatewayResult<boolean>>;

  runTargetPredict(arg0: StartTargetPredictPlanRequest): Promise<GatewayResult<boolean>>;

  savePredictPlanInfo(arg0: TargetPredictPlanObjectRequest): Promise<GatewayResult<boolean>>;

  saveQuantileValue(arg0: QuantileValueSaveRequest): Promise<GatewayResult<boolean>>;
}

/**
 * <AUTHOR>
 * @version TargetQueryGatewayService:1.0
 * @date 2024/3/12 15:18
 */
export interface TargetPredictQueryGatewayService {
  queryPredictPlan(
    arg0: TargetPredictPlanQueryRequest,
  ): Promise<GatewayResult<TargetPredictPlanDTO>>;

  queryQuantileValue(arg0: TargetPredictQueryRequest): Promise<GatewayResult<QuantileValueDTO[]>>;
}

/**
 * @author: <a href="<EMAIL>">早梅</a>
 * @date: 2024/6/17
 * @time: 2:49 PM
 * @description:
 */
export interface TargetPlanQueryGatewayService {
  queryTargetPlanDataInfos(
    arg0: TargetDataInfoQueryRequest,
  ): Promise<GatewayResult<TargetPlanDataInfoQueryResponse>>;

  queryTargetPlanModel(arg0: TargetPlanModelBizRequest): Promise<GatewayResult<TargetPlanModelDTO>>;

  queryTargetPlans(
    arg0: TargetPlanInfoQueryRequest,
  ): Promise<GatewayResult<TargetPlanInfoQueryResponse>>;
}

/**
 * @author: <a href="<EMAIL>">早梅</a>
 * @date: 2024/6/17
 * @time: 2:51 PM
 * @description:
 */
export interface TargetPlanOperateGatewayService {
  addTargetPlan(arg0: AddTargetPlanRequest): Promise<GatewayResult<AddTargetPlanDTO>>;

  downloadTargetPlanTemplate(
    arg0: DownloadTargetPlanDataRequest,
  ): Promise<GatewayResult<DownloadTargetPlanResponse>>;

  editTargetPlan(arg0: EditTargetPlanRequest): Promise<GatewayResult<EditTargetPlanDTO>>;

  manageTargetPlan(arg0: ManageTargetPlanRequest): Promise<GatewayResult<TargetPlanInfoDTO>>;
}

/**
 * 绩效战报网关类
 * <AUTHOR>
 * @version KpiReportGatewayService:1.0
 * @date 2023/9/25 15:20
 */
export interface KpiReportGatewayService {
  downloadKpiReportData(arg0: DownloadKpiReportRequest): Promise<GatewayResult<string>>;

  downloadKpiReportDataForMng(arg0: DownloadKpiReportRequest): Promise<GatewayResult<string>>;

  queryKpiReportInfo(arg0: KpiReportInfoQueryRequest): Promise<GatewayResult<KpiReportInfoVO>>;

  queryKpiReportPlans(
    arg0: KpiReportInfoQueryRequest,
  ): Promise<GatewayResult<KpiReportSimplePlanInfoVO[]>>;

  queryPlanIndicates(
    arg0: PlanIndicatorQueryRequest,
  ): Promise<GatewayResult<KpiReportIndicatorVO[]>>;
}

/**
 * getMeta: {"artifact":"com.alibaba.commcenter:gateway:1.0.0-SNAPSHOT","canonicalName":"com.alibaba.commcenter.client.model.bizcommon.enums.BusinessGroupEnum"}
 *
 * Values:
 * - `C33_AGENT`
 * - `C31_AGENT`
 * - `C33_DIRECT`
 */
export type BusinessGroupEnum = 'C33_AGENT' | 'C31_AGENT' | 'C33_DIRECT';

/**
 * getMeta: {"artifact":"com.alibaba.commcenter:gateway:1.0.0-SNAPSHOT","canonicalName":"com.alibaba.commcenter.client.model.bizcommon.enums.BusinessLineEnum"}
 *
 * Values:
 * - `DEFAULT`
 * - `C31_DEFAULT`
 * - `C33_DEFAULT`
 * - `RECREATION`
 * - `CATERING`
 * - `LOW_FREQUENCY`
 * - `BEAUTY`
 * - `MEDICAL_TREATMENT`
 * - `CLOUD_REGION`
 * - `CHANNEL`
 * - `BUSINESS_DISTRICT`
 * - `TELEPOHNE_SALE`
 */
export type BusinessLineEnum =
  | 'DEFAULT'
  | 'C31_DEFAULT'
  | 'C33_DEFAULT'
  | 'RECREATION'
  | 'CATERING'
  | 'LOW_FREQUENCY'
  | 'BEAUTY'
  | 'MEDICAL_TREATMENT'
  | 'CLOUD_REGION'
  | 'CHANNEL'
  | 'BUSINESS_DISTRICT'
  | 'TELEPOHNE_SALE';

/**
 * getMeta: {"artifact":"com.alibaba.commcenter:gateway:1.0.0-SNAPSHOT","canonicalName":"com.alibaba.commcenter.common.newcore.model.enums.DeleteTagEnum"}
 *
 * Values:
 * - `NOT_DELETED`
 * - `DELETED`
 */
export type DeleteTagEnum = 'NOT_DELETED' | 'DELETED';

/**
 * getMeta: {"artifact":"com.alibaba.commcenter:gateway:1.0.0-SNAPSHOT","canonicalName":"com.alibaba.commcenter.client.model.bizcommon.enums.ProductDomainEnum"}
 *
 * Values:
 * - `KPI`
 * - `COMMISSION`
 * - `TARGET_PREDICT`
 * - `FINE_TUNE`
 */
export type ProductDomainEnum = 'KPI' | 'COMMISSION' | 'TARGET_PREDICT' | 'FINE_TUNE';

/**
 * getMeta: {"artifact":"com.alibaba.commcenter:gateway:1.0.0-SNAPSHOT","canonicalName":"com.alibaba.commcenter.client.model.commpolicy.enums.CommPolicyStatusEnum"}
 *
 * Values:
 * - `INIT`
 * - `AUDITING`
 * - `PASS`
 * - `SUCCESS`
 * - `REJECT`
 * - `INVALID`
 */
export type CommPolicyStatusEnum = 'INIT' | 'AUDITING' | 'PASS' | 'SUCCESS' | 'REJECT' | 'INVALID';

/**
 * getMeta: {"artifact":"com.alibaba.commcenter:gateway:1.0.0-SNAPSHOT","canonicalName":"com.alibaba.commcenter.client.model.commpolicy.enums.CommDurationUnitEnum"}
 *
 * Values:
 * - `DAY`
 * - `MONTH`
 * - `MULTI_MONTH`
 * - `QUARTER`
 */
export type CommDurationUnitEnum = 'DAY' | 'MONTH' | 'MULTI_MONTH' | 'QUARTER';
