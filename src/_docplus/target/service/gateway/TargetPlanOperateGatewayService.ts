/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alibaba.commcenter
 * ArtifactId: gateway
 * Version: -
 */
import client, {
  GwRequestOption,
  GatewayResult,
} from '@/_docplus/target/request/client';

import {
  AddTargetPlanRequest, 
  AddTargetPlanDTO, 
  DownloadTargetPlanDataRequest, 
  DownloadTargetPlanResponse, 
  EditTargetPlanRequest, 
  EditTargetPlanDTO, 
  ManageTargetPlanRequest, 
  TargetPlanInfoDTO
} from '@/_docplus/target/types/gateway';

import { DOC_MODE_gateway } from '@/_docplus/target/request/const';

class TargetPlanOperateGatewayServiceClient {
  public async addTargetPlan(arg0: Partial<AddTargetPlanRequest>, option?: GwRequestOption): Promise<GatewayResult<AddTargetPlanDTO>> {
    const innerKey = `kbcommprod.TargetPlanOperateGatewayService.addTargetPlan:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async downloadTargetPlanTemplate(arg0: Partial<DownloadTargetPlanDataRequest>, option?: GwRequestOption): Promise<GatewayResult<DownloadTargetPlanResponse>> {
    const innerKey = `kbcommprod.TargetPlanOperateGatewayService.downloadTargetPlanTemplate:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async editTargetPlan(arg0: Partial<EditTargetPlanRequest>, option?: GwRequestOption): Promise<GatewayResult<EditTargetPlanDTO>> {
    const innerKey = `kbcommprod.TargetPlanOperateGatewayService.editTargetPlan:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async manageTargetPlan(arg0: Partial<ManageTargetPlanRequest>, option?: GwRequestOption): Promise<GatewayResult<TargetPlanInfoDTO>> {
    const innerKey = `kbcommprod.TargetPlanOperateGatewayService.manageTargetPlan:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new TargetPlanOperateGatewayServiceClient();
