/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alibaba.commcenter
 * ArtifactId: gateway
 * Version: -
 */
import client, {
  GwRequestOption,
  GatewayResult,
} from '@/_docplus/target/request/client';

import {
  DownloadPredictResultRequest, 
  StartTargetPredictPlanRequest, 
  TargetPredictPlanObjectRequest, 
  QuantileValueSaveRequest
} from '@/_docplus/target/types/gateway';

import { DOC_MODE_gateway } from '@/_docplus/target/request/const';

class TargetPredictManageGatewayServiceClient {
  public async downloadPredictResult(arg0: Partial<DownloadPredictResultRequest>, option?: GwRequestOption): Promise<GatewayResult<boolean>> {
    const innerKey = `kbcommprod.TargetPredictManageGatewayService.downloadPredictResult:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async runTargetPredict(arg0: Partial<StartTargetPredictPlanRequest>, option?: GwRequestOption): Promise<GatewayResult<boolean>> {
    const innerKey = `kbcommprod.TargetPredictManageGatewayService.runTargetPredict:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async savePredictPlanInfo(arg0: Partial<TargetPredictPlanObjectRequest>, option?: GwRequestOption): Promise<GatewayResult<boolean>> {
    const innerKey = `kbcommprod.TargetPredictManageGatewayService.savePredictPlanInfo:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async saveQuantileValue(arg0: Partial<QuantileValueSaveRequest>, option?: GwRequestOption): Promise<GatewayResult<boolean>> {
    const innerKey = `kbcommprod.TargetPredictManageGatewayService.saveQuantileValue:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new TargetPredictManageGatewayServiceClient();
