/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alibaba.commcenter
 * ArtifactId: gateway
 * Version: -
 */
import client, {
  GwRequestOption,
  GatewayResult,
} from '@/_docplus/target/request/client';

import {
  TargetPredictPlanQueryRequest, 
  TargetPredictPlanDTO, 
  TargetPredictQueryRequest, 
  QuantileValueDTO
} from '@/_docplus/target/types/gateway';

import { DOC_MODE_gateway } from '@/_docplus/target/request/const';

class TargetPredictQueryGatewayServiceClient {
  public async queryPredictPlan(arg0: Partial<TargetPredictPlanQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<TargetPredictPlanDTO>> {
    const innerKey = `kbcommprod.TargetPredictQueryGatewayService.queryPredictPlan:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryQuantileValue(arg0: Partial<TargetPredictQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<QuantileValueDTO[]>> {
    const innerKey = `kbcommprod.TargetPredictQueryGatewayService.queryQuantileValue:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new TargetPredictQueryGatewayServiceClient();
