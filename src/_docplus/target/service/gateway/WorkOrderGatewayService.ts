/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alibaba.commcenter
 * ArtifactId: gateway
 * Version: -
 */
import client, {
  GwRequestOption,
  GatewayResult,
} from '@/_docplus/target/request/client';

import {
  CreateWorkOrderGatewayRequest, 
  ModifyWorkOrderGateWayRequest, 
  WorkOrderDetailQueryGatewayRequest, 
  WorkOrderVO, 
  WorkOrderQueryGatewayRequest, 
  PageResult, 
  ModifyConfigResponse
} from '@/_docplus/target/types/gateway';

import { DOC_MODE_gateway } from '@/_docplus/target/request/const';

class WorkOrderGatewayServiceClient {
  public async createWorkOrder(request: Partial<CreateWorkOrderGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `kbcommprod.WorkOrderGatewayService.createWorkOrder:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async modifyWorkOrder(request: Partial<ModifyWorkOrderGateWayRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `kbcommprod.WorkOrderGatewayService.modifyWorkOrder:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryConfig(option?: GwRequestOption): Promise<GatewayResult<{[index: string]: any}>> {
    const innerKey = `kbcommprod.WorkOrderGatewayService.queryConfig:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [],
    }, option);
    return res;
  }

  public async queryWorkOrderDetail(request: Partial<WorkOrderDetailQueryGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<WorkOrderVO>> {
    const innerKey = `kbcommprod.WorkOrderGatewayService.queryWorkOrderDetail:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryWorkOrderList(request: Partial<WorkOrderQueryGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<PageResult<WorkOrderVO[]>>> {
    const innerKey = `kbcommprod.WorkOrderGatewayService.queryWorkOrderList:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryWorkOrderModifyConfig(request: Partial<WorkOrderDetailQueryGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<ModifyConfigResponse>> {
    const innerKey = `kbcommprod.WorkOrderGatewayService.queryWorkOrderModifyConfig:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new WorkOrderGatewayServiceClient();
