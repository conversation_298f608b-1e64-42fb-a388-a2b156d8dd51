/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alibaba.commcenter
 * ArtifactId: gateway
 * Version: -
 */
import client, {
  GwRequestOption,
  GatewayResult,
} from '@/_docplus/target/request/client';

import {
  CreateIndicatorRequest, 
  ModifyIndicatorRequest, 
  QueryIndicatorRequest, 
  QueryIndicatorResponse
} from '@/_docplus/target/types/gateway';

import { DOC_MODE_gateway } from '@/_docplus/target/request/const';

class IndicatorGatewayServiceClient {
  public async createIndicator(arg0: Partial<CreateIndicatorRequest>, option?: GwRequestOption): Promise<GatewayResult<boolean>> {
    const innerKey = `kbcommprod.IndicatorGatewayService.createIndicator:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async modifyIndicator(arg0: Partial<ModifyIndicatorRequest>, option?: GwRequestOption): Promise<GatewayResult<boolean>> {
    const innerKey = `kbcommprod.IndicatorGatewayService.modifyIndicator:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryIndicatorsByCondition(arg0: Partial<QueryIndicatorRequest>, option?: GwRequestOption): Promise<GatewayResult<QueryIndicatorResponse>> {
    const innerKey = `kbcommprod.IndicatorGatewayService.queryIndicatorsByCondition:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new IndicatorGatewayServiceClient();
