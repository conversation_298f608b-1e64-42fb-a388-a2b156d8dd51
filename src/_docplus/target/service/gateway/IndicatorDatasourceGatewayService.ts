/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alibaba.commcenter
 * ArtifactId: gateway
 * Version: -
 */
import client, {
  GwRequestOption,
  GatewayResult,
} from '@/_docplus/target/request/client';

import {
  QueryIndicatorDatasourceRequest, 
  QueryIndicatorDatasourceResponse
} from '@/_docplus/target/types/gateway';

import { DOC_MODE_gateway } from '@/_docplus/target/request/const';

class IndicatorDatasourceGatewayServiceClient {
  public async searchIndicatorDatasourceByCondition(arg0: Partial<QueryIndicatorDatasourceRequest>, option?: GwRequestOption): Promise<GatewayResult<QueryIndicatorDatasourceResponse>> {
    const innerKey = `kbcommprod.IndicatorDatasourceGatewayService.searchIndicatorDatasourceByCondition:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new IndicatorDatasourceGatewayServiceClient();
