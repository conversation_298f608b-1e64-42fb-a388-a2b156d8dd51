/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alibaba.commcenter
 * ArtifactId: gateway
 * Version: -
 */
import client, {
  GwRequestOption,
  GatewayResult,
} from '@/_docplus/target/request/client';

import {
  KpiPolicyEditGatewayRequest, 
  KpiPolicyDetailResponse, 
  KpiPolicyManageGatewayRequest, 
  PolicyConfigEditGatewayRequest
} from '@/_docplus/target/types/gateway';

import { DOC_MODE_gateway } from '@/_docplus/target/request/const';

class KpiPolicyManageGatewayServiceClient {
  public async copy(arg0: Partial<KpiPolicyEditGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<KpiPolicyDetailResponse>> {
    const innerKey = `kbcommprod.KpiPolicyManageGatewayService.copy:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async generateItemId(option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `kbcommprod.KpiPolicyManageGatewayService.generateItemId:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [],
    }, option);
    return res;
  }

  public async manage(arg0: Partial<KpiPolicyManageGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `kbcommprod.KpiPolicyManageGatewayService.manage:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async save(arg0: Partial<KpiPolicyEditGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<number>> {
    const innerKey = `kbcommprod.KpiPolicyManageGatewayService.save:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async saveConfig(arg0: Partial<PolicyConfigEditGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<boolean>> {
    const innerKey = `kbcommprod.KpiPolicyManageGatewayService.saveConfig:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new KpiPolicyManageGatewayServiceClient();
