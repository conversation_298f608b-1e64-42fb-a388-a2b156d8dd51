/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alibaba.commcenter
 * ArtifactId: gateway
 * Version: -
 */
import client, {
  GwRequestOption,
  GatewayResult,
} from '@/_docplus/target/request/client';

import {
  DownloadKpiReportRequest, 
  KpiReportInfoQueryRequest, 
  KpiReportInfoVO, 
  KpiReportSimplePlanInfoVO, 
  PlanIndicatorQueryRequest, 
  KpiReportIndicatorVO
} from '@/_docplus/target/types/gateway';

import { DOC_MODE_gateway } from '@/_docplus/target/request/const';

class KpiReportGatewayServiceClient {
  public async downloadKpiReportData(arg0: Partial<DownloadKpiReportRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `kbcommprod.KpiReportGatewayService.downloadKpiReportData:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async downloadKpiReportDataForMng(arg0: Partial<DownloadKpiReportRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `kbcommprod.KpiReportGatewayService.downloadKpiReportDataForMng:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryKpiReportInfo(arg0: Partial<KpiReportInfoQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<KpiReportInfoVO>> {
    const innerKey = `kbcommprod.KpiReportGatewayService.queryKpiReportInfo:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryKpiReportPlans(arg0: Partial<KpiReportInfoQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<KpiReportSimplePlanInfoVO[]>> {
    const innerKey = `kbcommprod.KpiReportGatewayService.queryKpiReportPlans:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryPlanIndicates(arg0: Partial<PlanIndicatorQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<KpiReportIndicatorVO[]>> {
    const innerKey = `kbcommprod.KpiReportGatewayService.queryPlanIndicates:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new KpiReportGatewayServiceClient();
