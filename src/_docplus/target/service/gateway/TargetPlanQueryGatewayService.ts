/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alibaba.commcenter
 * ArtifactId: gateway
 * Version: -
 */
import client, {
  GwRequestOption,
  GatewayResult,
} from '@/_docplus/target/request/client';

import {
  TargetDataInfoQueryRequest, 
  TargetPlanDataInfoQueryResponse, 
  TargetPlanModelBizRequest, 
  TargetPlanModelDTO, 
  TargetPlanInfoQueryRequest, 
  TargetPlanInfoQueryResponse
} from '@/_docplus/target/types/gateway';

import { DOC_MODE_gateway } from '@/_docplus/target/request/const';

class TargetPlanQueryGatewayServiceClient {
  public async queryTargetPlanDataInfos(arg0: Partial<TargetDataInfoQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<TargetPlanDataInfoQueryResponse>> {
    const innerKey = `kbcommprod.TargetPlanQueryGatewayService.queryTargetPlanDataInfos:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryTargetPlanModel(arg0: Partial<TargetPlanModelBizRequest>, option?: GwRequestOption): Promise<GatewayResult<TargetPlanModelDTO>> {
    const innerKey = `kbcommprod.TargetPlanQueryGatewayService.queryTargetPlanModel:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryTargetPlans(arg0: Partial<TargetPlanInfoQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<TargetPlanInfoQueryResponse>> {
    const innerKey = `kbcommprod.TargetPlanQueryGatewayService.queryTargetPlans:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new TargetPlanQueryGatewayServiceClient();
