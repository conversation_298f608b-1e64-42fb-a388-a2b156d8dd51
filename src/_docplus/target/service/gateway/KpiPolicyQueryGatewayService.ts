/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alibaba.commcenter
 * ArtifactId: gateway
 * Version: -
 */
import client, {
  GwRequestOption,
  GatewayResult,
} from '@/_docplus/target/request/client';

import {
  PolicyConfigQueryGatewayRequest, 
  KpiPolicyConfigDTO, 
  IndicatorListQueryGatewayRequest, 
  IndicatorQueryResponse, 
  KpiPolicyQueryGatewayRequest, 
  KpiPolicyDetailResponse, 
  KpiPolicyListResponse
} from '@/_docplus/target/types/gateway';

import { DOC_MODE_gateway } from '@/_docplus/target/request/const';

class KpiPolicyQueryGatewayServiceClient {
  public async queryConfig(arg0: Partial<PolicyConfigQueryGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<KpiPolicyConfigDTO>> {
    const innerKey = `kbcommprod.KpiPolicyQueryGatewayService.queryConfig:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryIndicatorList(arg0: Partial<IndicatorListQueryGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<IndicatorQueryResponse[]>> {
    const innerKey = `kbcommprod.KpiPolicyQueryGatewayService.queryIndicatorList:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryPolicyDetail(arg0: Partial<KpiPolicyQueryGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<KpiPolicyDetailResponse>> {
    const innerKey = `kbcommprod.KpiPolicyQueryGatewayService.queryPolicyDetail:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryPolicyList(arg0: Partial<KpiPolicyQueryGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<KpiPolicyListResponse>> {
    const innerKey = `kbcommprod.KpiPolicyQueryGatewayService.queryPolicyList:${DOC_MODE_gateway}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new KpiPolicyQueryGatewayServiceClient();
