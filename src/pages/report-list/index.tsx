import React from 'react';
import { getInitParam } from '@/common/configStore';
import store from './store';
import AssessPlan from './components';
import './index.less';

const Index: React.FC = () => (
  <div className="plan-list-warp">
    <div className="plan-list-title">
      <div style={{ display: 'flex' }}>
        <h2>看板管理</h2>
      </div>
    </div>
    <AssessPlan />
  </div>
);

export default () => (
  <store.Container
    component={Index}
    initParam={() => ({
      ...getInitParam(),
    })}
  />
);
