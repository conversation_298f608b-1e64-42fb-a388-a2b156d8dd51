/* eslint-disable no-use-before-define */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button, Table, Space, Popover, Modal, Row, message, Tooltip, Form } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import moment from 'moment';
import PlanManageFacade from '@/_docplus/target/service/gateway/KpiPolicyQueryGatewayService';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import {
  planStateEnum,
  planStateListEnum,
  containButtonStateMap,
  planStateActionEnum,
  periodTypeListEnum,
  ActionTypeEnum,
} from '@/common/Types';
import CommonStructure from '@/common/components/CommonStructure';
import { PERIOD_TIME_FORMAT } from '@/common/constants';
import { KpiPolicyQueryGatewayRequest, KpiPolicyDTO } from '@/_docplus/target/types/gateway';
import store from '../store';
import SeachList from './seach-list';
import { POLICY_TYPE_ENUM, POLICY_TYPE_PARAMS } from './seach-list/constant';

import './index.less';

const { confirm } = Modal;

interface IPageData {
  pageSize: number;
  current: number;
  total: number;
}

const Index: React.FC = (props) => {
  console.log(props);

  const storeIns = store.useStore();
  const businessGroup = 'C33_DIRECT';

  const pageInfo = useRef<IPageData>({
    pageSize: 10,
    current: 1,
    total: 0,
  });

  const [planValueList, setPlanValueList] = useState<any>([]);

  const [form] = Form.useForm();
  const planSource = Form.useWatch(['planSource'], form);
  const formInfos = useRef({});

  const content = useCallback(
    (record: KpiPolicyDTO) => (
      <Space>
        <Button
          size="small"
          type="link"
          onClick={() => changeStatus(record?.policyId, planStateEnum.PASS, record?.policyType)}
          disabled={
            !record.canEdit || !containButtonStateMap.RELEASE.includes(record?.policyStatus)
          }
        >
          发布
        </Button>
        <Button
          size="small"
          type="link"
          onClick={() => changeStatus(record?.policyId, planStateEnum.INVALID, record?.policyType)}
          disabled={!record.canEdit || !containButtonStateMap.STOP.includes(record?.policyStatus)}
        >
          停用
        </Button>
        <Button
          size="small"
          danger
          type="text"
          onClick={() => changeStatus(record?.policyId, planStateEnum.DELETED, record?.policyType)}
          disabled={!containButtonStateMap.DELETE.includes(record?.policyStatus)}
        >
          删除
        </Button>
      </Space>
    ),
    [],
  );

  const columns: ColumnsType<KpiPolicyDTO> = [
    {
      title: '看板名称',
      dataIndex: 'policyName',
      key: 'policyName',
      width: 300,
      fixed: 'left',
    },
    {
      title: '看板ID',
      dataIndex: 'policyId',
      key: 'policyId',
      width: 200,
    },
    {
      title: '周期类型',
      dataIndex: 'commDurationUnit',
      key: 'commDurationUnit',
      render: (_, record) => periodTypeListEnum[record?.periodType],
    },
    {
      title: '生效时间段',
      dataIndex: 'gmtEffect',
      key: 'gmtEffect',
      render: (_, record) =>
        record?.gmtEffect && record?.gmtExpire
          ? `${moment(record.gmtEffect).format('YYYY-MM-DD')}-${moment(record.gmtExpire).format(
              'YYYY-MM-DD',
            )}`
          : '--',
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      render: (_, record) =>
        record?.gmtCreate ? moment(record.gmtCreate).format(PERIOD_TIME_FORMAT) : '--',
    },
    {
      title: '修改时间',
      dataIndex: 'gmtModified',
      key: 'gmtModified',
      render: (_, record) =>
        record?.gmtModified ? moment(record.gmtModified).format(PERIOD_TIME_FORMAT) : '--',
    },
    {
      title: '负责人',
      dataIndex: 'creator',
      key: 'creator',
      render: (_, record) => (
        <Tooltip placement="topLeft" title={record.creator}>
          {record.creator}
        </Tooltip>
      ),
    },
    {
      title: '修改人',
      dataIndex: 'modifier',
      key: 'modifier',
    },
    {
      title: '状态',
      dataIndex: 'policyStatus',
      key: 'policyStatus',
      render: (_, record) => planStateListEnum[record.policyStatus],
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      render: (_, record) => (
        <Space size="middle" className="btn-list">
          <Button
            size="small"
            className="u-px-0"
            type="link"
            onClick={() => openAdd(ActionTypeEnum.EDIT, record?.policyId)}
            disabled={!record.canEdit || !containButtonStateMap.EDIT.includes(record?.policyStatus)}
          >
            编辑
          </Button>
          <Button
            size="small"
            className="u-px-0"
            type="link"
            onClick={() => openAdd(ActionTypeEnum.SEE, record?.policyId)}
          >
            查看
          </Button>
          <Button
            size="small"
            className="u-px-0"
            type="link"
            onClick={() => window.open(`https://pre-mp-next.amap.com/app/amap_web_c3_info/xy-report-builder/app-pc/builder?policyId=${record.policyId}`, '_blank')}
          >
            搭建
          </Button>
          <Popover
            content={() => content(record)}
            overlayStyle={{
              zIndex: 9,
            }}
          >
            <Button size="small" className="u-px-0" type="link">
              更多
            </Button>
          </Popover>
        </Space>
      ),
    },
  ]?.filter((column) => column?.dataIndex);

  /** 查询方案列表 */
  const getPlanList = async (params) => {
    try {
      message.loading({ key: 'loading', content: '正在加载列表数据' });

      const res = await PlanManageFacade.queryPolicyList(
        {
          ...params,
          businessGroup,
          bizType: 'DISPLAY_CONFIG'
        },
        {
          disableLoading: true,
        },
      );
      message.destroy('loading');
      pageInfo.current = {
        current: pageInfo.current.current || 1,
        pageSize: pageInfo.current.pageSize || 10,
        total: res?.data?.pageInfoDTO?.totalCount,
      };
      setPlanValueList(res?.data?.kpiPolicyDTOList);
    } catch (error) {
      pageInfo.current = {
        current: 1,
        pageSize: 10,
        total: 0,
      };
      setPlanValueList([]);
    }
  };

  // 跳转详情页
  const openAdd = (type = ActionTypeEnum.ADD, planId = '') => {
    const prefix =
      window.APP?.isFromKbServ === 'true' ? '/sale-pc/mp-kpi-assess-plan' : '/mp-kpi-assess-plan';
    if (type === ActionTypeEnum.ADD) {
      window.open(`${prefix}/report-detail?type=${type}&role=${storeIns?.store?.role}`);
    } else {
      window.open(`${prefix}/report-detail?type=${type}&planId=${planId}&role=${storeIns?.store?.role}`);
    }
  };

  // 操作考核方案
  const changeStatus = useCallback(
    async (planId: string | number, planStatus: string, policyType?: string) => {
      confirm({
        title: `确认${planStateActionEnum[planStatus]}看板？`,
        icon: <ExclamationCircleOutlined />,
        onOk() {
          handleActionFunc(planId, planStatus, policyType);
        },
      });
    },
    [planSource],
  );

  const handleActionFunc = useCallback(
    async (planId, planStatus, policyType) => {
      try {
        const res = await storeIns.updatePlanStatus({
          kpiPolicyId: planId,
          bizAction: planStatus,
          businessGroup,
        });
        if (!res?.success) {
          return;
        }
        if (planStatus === planStateEnum.PASS) {
          message.success({
            content: '看板发布成功',
            key: 'release',
            duration: 10,
          });
        } else if (planStatus === planStateEnum.INVALID) {
          message.success({ content: '停用成功', key: 'loading' });
        }

        const request = {
          ...formInfos.current,
          pageNo: pageInfo.current.current,
          pageSize: pageInfo.current.pageSize,
        };
        setTimeout(() => {
          getPlanList(request);
        }, 1500);
      } catch (error) {
        message.error({ content: error.errorMessage, key: 'loading' });
      }
    },
    [planSource],
  );

  const pagination = {
    current: pageInfo.current.current || 1,
    pageSize: pageInfo.current.pageSize || 10,
    total: pageInfo.current.total || 0,
    showTotal: () => `共${pageInfo.current.total || 0}条`,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20'],
    showQuickJumper: true,
  };

  const changeTable = (v) => {
    pageInfo.current = {
      pageSize: v?.pageSize,
      current: v?.current,
      total: v?.total,
    };
    const request: Partial<KpiPolicyQueryGatewayRequest> = {
      ...formInfos.current,
      pageNo: v?.current,
      pageSize: v?.pageSize,
    };
    getPlanList(request);
  };

  const handleSearch = (value) => {
    const request: Partial<KpiPolicyQueryGatewayRequest> = {
      pageNo: 1,
      pageSize: pageInfo.current.pageSize || 10,
      ...value,
    };
    pageInfo.current = { ...pageInfo.current, current: 1 };
    formInfos.current = { ...formInfos.current, ...value };
    getPlanList(request);
  };

  useEffect(() => {
    handleSearch({});
  }, []);

  return (
    <div>
      {/* 搜索 */}
      <SeachList form={form} handleSearch={handleSearch} storeIns={storeIns} />

      <CommonStructure.Section
        title={<span className="u-fs-16">看板列表</span>}
        extra={
          <Row justify="end">
            <Space>
              <Button type="primary" onClick={() => openAdd(ActionTypeEnum.ADD)}>
                新增看板
              </Button>
            </Space>
          </Row>
        }
      >
        <Table
          style={{ marginTop: 16 }}
          columns={columns}
          dataSource={planValueList}
          pagination={{ ...pagination }}
          onChange={changeTable}
          rowKey="policyId"
          scroll={{ x: 'max-content' }}
        />
      </CommonStructure.Section>
    </div>
  );
};

export default Index;
