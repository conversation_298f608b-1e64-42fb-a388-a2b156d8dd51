import React, { FC, useRef } from 'react';
import { Affix, Alert, Modal, PageHeader } from 'antd';
import { useRequest } from 'ahooks';
import { LoadingOutlined } from '@ant-design/icons';
import CommonStructure from '@/common/components/CommonStructure';
import { withLoading, WithLoadingProps } from '@/common/components/withLoading';
import targetPredictQueryClient from '@/_docplus/target/service/gateway/TargetPredictQueryGatewayService';
import { TargetConfigContext } from '../context';
import styles from './TargetConfigPage.module.less';
import { TargetBasicConfigs } from './TargetBasicConfigs';
import { TargetQuantileConfigs } from './TargetQuantileConfigs';
import { TargetDownload, tryScrollToDownload } from './TargetDownload';

type TargetConfigPageAsyncProps = WithLoadingProps<Awaited<ReturnType<typeof getTargetConfigPageInitialData>>>;

const RawTargetConfigPage: FC<TargetConfigPageAsyncProps> = (props) => {
  const plan = props.data;

  const isInitialPollRef = useRef(true);

  const {
    data: predictFinished,
    loading: pollingPredictFinish,
    run: startPollPredictFinish,
    cancel: cancelPollPredictFinish,
  } = useRequest(
    async () => {
      let result: boolean;

      if (isInitialPollRef.current) {
        // 初次进入的时候，直接取前面拉过的结果
        result = plan.predictFinish;
      } else {
        const resp = await targetPredictQueryClient.queryPredictPlan({
          predictPlanId: plan.predictPlanId,
        });
        result = resp?.data?.predictFinish ?? true;
      }

      if (result) {
        // 计算完毕，停止轮询
        // 需要异步停止，如果同步停止的话，data 永远是 undefined
        setTimeout(() => {
          cancelPollPredictFinish();
        }, 1000);

        // 计算成功，且不是首次进入的那种默认成功而是轮询出来的成功
        // 给个提示
        if (!isInitialPollRef.current) {
          Modal.success({
            title: '计算完毕',
            content: '目标结果计算完毕，现在可以进行下载',
            onOk: tryScrollToDownload,
          });
        }
      }

      isInitialPollRef.current = false;
      return result;
    },
    {
      pollingInterval: 5e3,
      pollingWhenHidden: false,
    },
  );

  const isCalculatingPredictPlan = pollingPredictFinish || !predictFinished;

  return (
    <TargetConfigContext.Provider
      value={{
        plan,
        isCalculatingPredictPlan,
        startPollPredictFinish,
      }}
    >
      {isCalculatingPredictPlan && (
        <Affix>
          <Alert
            icon={<LoadingOutlined />}
            type="info"
            banner
            message="正在计算目标结果，保存和下载暂不可用…"
          />
        </Affix>
      )}

      <div className={styles.wrapper}>
        <PageHeader
          title="目标计算方案详情"
        />

        <CommonStructure.Section title="基础信息">
          <TargetBasicConfigs />
        </CommonStructure.Section>

        <CommonStructure.Section
          title={[plan.predictPlanName, '分位置配置'].filter(Boolean).join(' ')}
        >
          <TargetQuantileConfigs plan={plan} />
        </CommonStructure.Section>

        <CommonStructure.Section
          title="目标计算结果下载"
        >
          <TargetDownload />
        </CommonStructure.Section>
      </div>
    </TargetConfigContext.Provider>
  );
};

async function getTargetConfigPageInitialData() {
  const usp = new URLSearchParams(window.location.search.slice(1));
  const predictPlanId = usp.get('predictPlanId');
  if (!predictPlanId) {
    const err = new Error('目标测算方案 ID 不能为空，请检查您的链接');
    (err as any).canRetry = false;
    throw err;
  }

  const resp = await targetPredictQueryClient.queryPredictPlan({
    predictPlanId,
  });
  if (!resp?.data?.predictPlanId) {
    throw new Error('目标测算方案查询失败');
  }

  return resp.data;
}

export const TargetConfigPage = withLoading(
  RawTargetConfigPage,
  getTargetConfigPageInitialData,
);
