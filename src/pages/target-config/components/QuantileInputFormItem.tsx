import { FC } from 'react';
import { Button, InputNumber, InputNumberProps, Spin, Tag } from 'antd';
import { useRequest } from 'ahooks';
import indicatorDataClient from '@/_docplus/target/service/gateway/IndicatorDataGatewayService';
import { GroupAnalyzeValueDTO, QuantileValueDTO, TargetPredictPlanDTO } from '@/_docplus/target/types/gateway';
import styles from './QuantileInputFormItem.module.less';

export interface QuantileInputFormItemProps extends InputNumberProps {
  item: QuantileValueDTO;
  plan: TargetPredictPlanDTO;
}

const MAGIC_NULL = '$$null$$';

// 这些维度是不需要带到服务端的
const DIM_BLACK_LIST = {
  人员标签: true,
};

const getNormalizedValue = (v: any) => {
  if (typeof v !== 'string' && typeof v !== 'number') {
    return 0;
  }
  const parsed = parseFloat(v as any);
  if (parsed !== parsed) {
    // NaN !== NaN
    return 0;
  }
  return parsed;
};

export const QuantileInputFormItem: FC<QuantileInputFormItemProps> = (props) => {
  const {
    item,
    plan,
    value,
    ...inputNumberProps
  } = props;

  const {
    data,
    loading,
    run,
  } = useRequest(
    async () => {
      try {
        const groupAnalyzeValues: Partial<GroupAnalyzeValueDTO> = {
          groupDimValues: item.dims.filter(item => !DIM_BLACK_LIST[item.alias]).map(dim => ({
            groupDimIndicatorId: dim.indicatorId,
            dimValue: dim.dimValue,
          })),
          groupValue: String((getNormalizedValue(value) / 100)),
        };
        const resp = await indicatorDataClient.fetchData({
          groupAnalyzeValues: [groupAnalyzeValues as any],
          indicatorId: item.groupIndicatorId,
          periodType: plan.periodType,
          analyzeGroup: true,
          periodValues: [
            plan.periodValue,
          ],
          groupValueType: 'RANK_PERCENTAGE',
        });
        const predictValue = resp.data?.data?.[0]?.value;
        if (typeof predictValue !== 'string') {
          return predictValue || MAGIC_NULL;
        }
        return parseFloat(predictValue).toFixed(2);
      } catch (err) {
        console.error(err);
        return '--';
      }
    },
    {
      manual: true,
      debounceWait: 800,
      debounceLeading: true,
      debounceTrailing: false,
    },
  );

  const renderData = () => {
    if (loading) {
      return (
        <span className={styles.value}>
          <Spin spinning />
        </span>
      );
    }
    if (!data) {
      return null;
    }
    if (data === MAGIC_NULL) {
      return <Tag className={styles.value}>null</Tag>;
    }
    return <span className={styles.value}>{data}</span>;
  };

  return (
    <>
      <InputNumber
        value={value}
        {...inputNumberProps}
        disabled={loading || inputNumberProps.disabled}
      />
      <Button
        type="link"
        disabled={loading}
        onClick={run}
        className={styles.btn}
      >
        查看对应值
      </Button>
      {renderData()}
    </>
  );
};
