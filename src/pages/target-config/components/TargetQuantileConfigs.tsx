import React, { FC } from 'react';
import { Empty, Form, message, Table } from 'antd';
import { useRequest } from 'ahooks';
import { withLoading, WithLoadingProps } from '@/common/components/withLoading';
import {
  QuantileDim,
  QuantileValueDTO,
  TargetPredictPlanDTO,
} from '@/_docplus/target/types/gateway';
import { DeepPartial } from '@/_docplus/target/request/client';
import targetPredictQueryClient from '@/_docplus/target/service/gateway/TargetPredictQueryGatewayService';
import targetPredictManageClient from '@/_docplus/target/service/gateway/TargetPredictManageGatewayService';
import { handleFinishFailed } from '@/common/utils';
import { useTargetConfig } from '../context';
import { ActionsWrapper } from './ActionsWrapper';
import { QuantileInputFormItem } from './QuantileInputFormItem';
import styles from './TargetQuantileConfigs.module.less';
import { AsyncLockButton } from './AsyncLockButton';
import type { ColumnType } from 'antd/es/table/interface';

export interface TargetQuantileConfigsProps {
  plan: TargetPredictPlanDTO;
}

type TargetQuantileConfigsAsyncData = WithLoadingProps<Awaited<ReturnType<typeof getTargetQuantileConfigsInitialData>>>;
type TargetQuantileConfigsFinalProps = TargetQuantileConfigsProps & TargetQuantileConfigsAsyncData;

type FormValues = Record<string, Record<string, number | undefined> | undefined>;

type AsyncData = Awaited<ReturnType<typeof getTargetQuantileConfigsInitialData>>;
type RowItem = AsyncData['dataSource'][0];
// type DimRef = AsyncData['allDimensions'][0]

/**
 * 服务端返回的数据是打平的，所以如果这一行前面的几个单元格和上一行一样，
 * 需要前端计算手动合并下
 */
const getRowSpan = <TItem = any>(
  record: TItem,
  index: number,
  /**
   * 所有数据
   */
  dataSource: TItem[],

  /**
   * 判断两行数据是否一致
   */
  compare: (a: TItem, b: TItem) => boolean,
) => {
  const prevRecord = dataSource[index - 1];

  if (compare(record, prevRecord)) {
    // 如果和前一行相同，说明之前合并过了，也不需要合并
    return 0;
  }

  // 计算需要合并的行数
  let span = 1;
  let i = index + 1;
  while (i < dataSource.length && compare(record, dataSource[i])) {
    i++;
    span++;
  }

  return span;
};

const FORM_KEY_SEPARATOR_KV = '_@@_';
const FORM_KEY_SEPARATOR = '_$$_';
/**
 * 根据所有「维度」信息，生成表单 key
 */
const buildFormKey = (item: QuantileValueDTO) => item.dims
  .map(d => [d.indicatorId, d.dimValue].join(FORM_KEY_SEPARATOR_KV))
  .join(FORM_KEY_SEPARATOR);

const RawTargetQuantileConfigs: FC<TargetQuantileConfigsFinalProps> = (props) => {
  const { data, plan } = props;
  const { dataSource, allDimensions, initialValues } = data || {};
  const { isCalculatingPredictPlan } = useTargetConfig();

  const {
    loading,
    run,
  } = useRequest(
    async (formValues: FormValues) => {
      const data = dataSource.map((item): Partial<QuantileValueDTO> => ({
        indicatorId: item.indicatorId,
        indicatorName: item.indicatorName,
        dims: item.dims,
        value: (formValues[item.indicatorId]?.[buildFormKey(item)] ?? 0) / 100,
        count: item.count,
      }));
      await targetPredictManageClient.saveQuantileValue({
        periodType: plan.periodType,
        periodValue: plan.periodValue,
        businessLine: plan.businessLine,
        predictPlanId: plan.predictPlanId,
        quantileValues: data as any,
      });
      message.success('保存成功');
    },
    {
      manual: true,
    },
  );

  if (!dataSource?.length || !allDimensions?.length) {
    return (
      <Empty description="没有查询到需要配置的方案" />
    );
  }

  const dimCols = allDimensions.map((dimDef): ColumnType<RowItem> => ({
    title: dimDef.alias,
    render: (_: any, record) => {
      const dim = record?.mappedDims[dimDef.indicatorId!];
      return dim?.displayValue || dim?.dimValue || '--';
    },
    onCell: (record: RowItem) => ({
      rowSpan: record.dimRowSpans[dimDef.indicatorId] ?? 1,
    }),
  }));

  const columns: Array<ColumnType<RowItem>> = [
    // 第一列固定是指标
    {
      title: '指标',
      className: styles.indicatorCol,
      dataIndex: 'indicatorName',
      onCell: (record: RowItem) => ({
        rowSpan: record.rowSpan,
      }),
    },
    ...dimCols,
    {
      title: '人数',
      dataIndex: 'count',
    },
    {
      title: '取值对应排名分位值',
      fixed: 'right',
      render: (_, record) => (
        <Form.Item
          name={[record.indicatorId, record.formKey]}
          noStyle
        >
          <QuantileInputFormItem
            min={0}
            max={100}
            step={1}
            addonAfter="%"
            className={styles.numInput}
            item={record}
            plan={plan}
          />
        </Form.Item>
      ),
    },
  ];

  return (
    <Form<FormValues>
      onFinish={run}
      disabled={loading || isCalculatingPredictPlan}
      initialValues={initialValues}
      onFinishFailed={handleFinishFailed}
    >
      <Table
        loading={loading}
        className={styles.table}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        scroll={{ x: true }}
      />

      <ActionsWrapper>
        <AsyncLockButton
          type="primary"
          htmlType="submit"
          disabled={loading}
        >
          保存
        </AsyncLockButton>
      </ActionsWrapper>
    </Form>
  );
};

async function getTargetQuantileConfigsInitialData(props: TargetQuantileConfigsProps) {
  const {
    businessLine,
    predictPlanId,
    periodValue,
    periodType,
  } = props.plan || {};
  const resp = await targetPredictQueryClient.queryQuantileValue({
    businessLine,
    predictPlanId,
    periodValue,
    periodType,
  });
  const data = resp?.data;

  // 先进行排序，确保后面的生成的 form key 是稳定的
  data.forEach(item => {
    item?.dims?.sort((a, b) => (a?.sortNum ?? 0) - (b?.sortNum ?? 0));
  });

  // 合并所有数据的「维度」信息，产出表格的所有列
  const dimensions: Array<DeepPartial<Omit<QuantileDim, 'dimValue'>>> = [];
  const addedDimIds = new Set<string | undefined>();
  data.forEach(item => {
    item.dims.forEach(dim => {
      if (!addedDimIds.has(dim?.indicatorId)) {
        addedDimIds.add(dim?.indicatorId);
        dimensions.push(dim);
      }
    });
  });

  // 预处理下数据
  const mappedData = data.map((indicatorItem, index, arr) => ({
    ...indicatorItem,

    // 把服务端返回的数字转换成 ID - value 的形式，方面取值
    mappedDims: indicatorItem.dims.reduce(
      (prev, curr) => ({
        ...prev,
        [curr.indicatorId]: curr,
      }),
      {} as Record<string, DeepPartial<QuantileDim>>,
    ),

    // 提前把表单 key 拼好，省的每次 render 都重新拼接浪费性能
    formKey: buildFormKey(indicatorItem),

    // 提前把「指标」列的单元格合并计算好，省的后面每次 render 都需要计算
    rowSpan: getRowSpan(
      indicatorItem,
      index,
      arr,
      (a, b) => a?.indicatorId === b?.indicatorId,
    ),
  })).map(((indicatorItem, index, arr) => {
    const dimRowSpans: Record<string, number> = {};

    // 提前把其他列的单元格合并计算好，省的后面每次 render 都需要计算
    indicatorItem.dims.forEach((dimDef) => {
      const getDimValue = (recordItem: typeof indicatorItem) => (
        recordItem?.mappedDims[dimDef.indicatorId].dimValue
      );
      dimRowSpans[dimDef.indicatorId] = getRowSpan(
        indicatorItem,
        index,
        arr,
        (a, b) => getDimValue(a) === getDimValue(b),
      );
    });

    return {
      ...indicatorItem,
      dimRowSpans,
    };
  }));

  // 处理表单回填逻辑
  const initialValues: FormValues = {};
  mappedData.forEach(item => {
    initialValues[item.indicatorId] = initialValues[item.indicatorId] || {};
    initialValues[item.indicatorId][item.formKey] = (() => {
      if (typeof item.value !== 'string' && typeof item.value !== 'number') {
        return undefined;
      }
      return parseFloat(item.value as any) * 100;
    })();
  });

  return {
    dataSource: mappedData || [],
    allDimensions: dimensions,
    initialValues,
  };
}

export const TargetQuantileConfigs = withLoading(
  RawTargetQuantileConfigs,
  getTargetQuantileConfigsInitialData,
);
