import { FC, ReactNode } from 'react';
import { Button, ButtonProps, Tooltip } from 'antd';
import { useTargetConfig } from '../context';

export interface AsyncLockButtonProps extends ButtonProps {
  message?: ReactNode;
}

export const AsyncLockButton: FC<AsyncLockButtonProps> = (props) => {
  const { message, ...buttonProps } = props;
  const { isCalculatingPredictPlan } = useTargetConfig();

  return (
    <Tooltip
      title={isCalculatingPredictPlan ? (
        <>
          正在计算目标结果，无法{message || buttonProps.children}
        </>
      ) : null}
    >
      <Button
        {...buttonProps}
        disabled={buttonProps.disabled || isCalculatingPredictPlan}
      />
    </Tooltip>
  );
};
