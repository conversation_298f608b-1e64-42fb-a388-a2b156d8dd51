import React, { FC } from 'react';
import { Button, Form, InputNumber, message } from 'antd';
import { CommonUpload } from '@alife/mp-oss-upload';
import { useRequest } from 'ahooks';
import { FileExcelOutlined, UploadOutlined } from '@ant-design/icons';
import { BUCKET_NAME, BUCKET_PID } from '@/common/constants';
import { TargetPredictPlanObjectRequest } from '@/_docplus/target/types/gateway';
import targetPredictManageClient from '@/_docplus/target/service/gateway/TargetPredictManageGatewayService';
import { handleFinishFailed } from '@/common/utils';
import { useTargetConfig } from '../context';
import { ActionsWrapper } from './ActionsWrapper';
import styles from './TargetBasicConfigs.module.less';
import { AsyncLockButton } from './AsyncLockButton';

type FormValues = Pick<TargetPredictPlanObjectRequest, 'financialTargetValue'> & {
  file: Array<{ ossFileName: string }>;
};

const renderMonth = (month: string) => {
  if (typeof month !== 'string' || !month || month.length !== 6) {
    return month;
  }
  return [month.slice(0, 4), month.slice(4)].join('-');
};

export const TargetBasicConfigs: FC = () => {
  const [form] = Form.useForm<FormValues>();
  const { plan, isCalculatingPredictPlan } = useTargetConfig();

  const {
    loading,
    run,
  } = useRequest(
    async (formValues: FormValues) => {
      const { file, financialTargetValue } = formValues;
      await targetPredictManageClient.savePredictPlanInfo({
        targetPredictPlanId: plan.predictPlanId,
        financialTargetValue,
        ossKey: file[0].ossFileName,
      });
      message.success('保存成功');
    },
    {
      manual: true,
      debounceWait: 800,
      debounceLeading: true,
      debounceTrailing: false,
    },
  );

  const formDisabled = loading || isCalculatingPredictPlan;

  return (
    <Form<FormValues>
      form={form}
      disabled={formDisabled}
      onFinish={run}
      className={styles.wrapper}
      initialValues={{
        financialTargetValue: plan.financialTargetValue ?? '',
      }}
      onFinishFailed={handleFinishFailed}
    >
      <Form.Item label="行业线">
        {plan.businessLineDesc || '--'}
      </Form.Item>

      <Form.Item label="考核月">
        {renderMonth(plan.periodValue) || '--'}
      </Form.Item>

      <Form.Item
        name="financialTargetValue"
        label="财务目标"
        rules={[
          { required: true, message: '请输入财务目标' },
        ]}
      >
        <InputNumber
          className={styles.targetInput}
          min={0.01}
          step={0.01}
        />
      </Form.Item>

      <Form.Item
        name="file"
        label="下目标人员名单"
        className={styles.uploader}
        extra={(
          <>
            支持扩展名：.xls .xlsx<br />
            <Button
              type="link"
              disabled={formDisabled}
              className={styles.download}
              icon={<FileExcelOutlined />}
              href="https://a.amap.com/smallBiz/static/agent-xy/目标测算对象模版.xlsx"
              target="_blank"
            >
              下载Excel模板
            </Button>
          </>
        )}
        rules={[
          { required: true, message: '请上传人员名单' },
        ]}
      >
        <CommonUpload
          bucketName={BUCKET_NAME}
          ossConfigId={BUCKET_PID}
          accept=".xls,.xlsx"
          maxCount={1}
          filePath="days/1/target/targetpredict"
          disabled={formDisabled}
        >
          <Button
            icon={<UploadOutlined />}
            className={styles.targetInput}
            disabled={formDisabled}
          >
            上传人员名单
          </Button>
        </CommonUpload>
      </Form.Item>

      <ActionsWrapper>
        <AsyncLockButton
          type="primary"
          htmlType="submit"
          loading={loading}
        >
          保存
        </AsyncLockButton>
      </ActionsWrapper>
    </Form>
  );
};
