import { FC } from 'react';
import { message, Modal } from 'antd';
import { useRequest } from 'ahooks';
import targetPredictManageClient from '@/_docplus/target/service/gateway/TargetPredictManageGatewayService';
import { useTargetConfig } from '../context';
import { ActionsWrapper } from './ActionsWrapper';
import { AsyncLockButton } from './AsyncLockButton';
import styles from './TargetDownload.module.less';

const sharedUseRequestOptions = {
  manual: true,
  debounceWait: 800,
  debounceLeading: true,
  debounceTrailing: false,
};

export const TargetDownload: FC = () => {
  const { plan, isCalculatingPredictPlan, startPollPredictFinish } = useTargetConfig();

  const {
    loading: running,
    run: runTargetPredict,
  } = useRequest(
    async () => {
      try {
        await targetPredictManageClient.runTargetPredict({
          targetPredictPlanId: plan.predictPlanId,
        });
        message.success('开始计算成功');
        // 触发调用后开始轮询
        startPollPredictFinish();
      } catch (err) {
        // ...
      }
    },
    sharedUseRequestOptions,
  );

  const {
    loading: downloading,
    run: downloadPredictResult,
  } = useRequest(
    async () => {
      await targetPredictManageClient.downloadPredictResult({
        targetPredictPlanId: plan.predictPlanId,
      });
      Modal.success({
        title: '下载成功',
        content: '下载链接已发送至您的邮箱，链接具有时效性，请尽快前往阿里邮箱查收',
      });
    },
    sharedUseRequestOptions,
  );

  return (
    <ActionsWrapper>
      <AsyncLockButton
        type="primary"
        disabled={running || downloading}
        loading={running || isCalculatingPredictPlan}
        onClick={runTargetPredict}
        message="重复计算"
      >
        {isCalculatingPredictPlan ? '正在计算…' : '开始计算'}
      </AsyncLockButton>
      <AsyncLockButton
        type="primary"
        disabled={running || downloading}
        loading={downloading}
        onClick={downloadPredictResult}
        id={styles.download}
      >
        下载结果
      </AsyncLockButton>
    </ActionsWrapper>
  );
};

export const tryScrollToDownload = () => {
  try {
    document.getElementById(styles.download)?.scrollIntoView?.({
      behavior: 'smooth',
    });
  } catch (err) {
    console.error(err);
  }
};
