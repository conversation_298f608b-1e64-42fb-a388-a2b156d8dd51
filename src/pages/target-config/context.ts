import { createContext, useContext } from 'react';
import { TargetPredictPlanDTO } from '@/_docplus/target/types/gateway';

export interface TargetConfigContextData {
  plan: Omit<TargetPredictPlanDTO, 'predictFinish'>;
  isCalculatingPredictPlan: boolean;
  startPollPredictFinish: () => void;
}

export const TargetConfigContext = createContext<TargetConfigContextData>({} as any);

export const useTargetConfig = () => useContext(TargetConfigContext);
