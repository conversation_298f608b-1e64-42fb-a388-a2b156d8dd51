import { Ncp } from '@ali/prometheus-fetch';
import fetch from '@ali/kb-fetch';

/**
 * 批量复制政策
 * @param param
 * @returns
 */
export function batchCopy(params) {
  return fetch({
    apiKey: 'kbcommprod.KpiPolicyManageGatewayService.batchCopy',
    params,
  });
}

function findEnumMap() {
  return Ncp.fetch({
    module: 'poiWb',
    service: 'XyStrategyService',
    method: 'findEnumMap',
  });
}

export interface QueryInterface {
  query: any;
  page: number;
  pageSize?: number;
}

function getStrategyTable(query: QueryInterface) {
  return Ncp.fetch({
    module: 'poiWb',
    service: 'XyStrategyService',
    method: 'getStrategies',
    params: query,
  });
}

export default {
  findEnumMap,
  getStrategyTable,
};
