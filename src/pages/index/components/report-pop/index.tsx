import React, { useState } from 'react';
import { Modal, DatePicker, message } from 'antd';
import moment from 'moment';
import { downloadFile } from '@alife/kb-biz-util';
import KpiReportGatewayService from '@/_docplus/target/service/gateway/KpiReportGatewayService';
import request from '@/common/request';

import './index.less';

export enum DownloadType {
  BILL = 'BILL',
  PAYMENT = 'PAYMENT',
}

/**
 * 下载弹窗
 * @param props
 * @returns
 */
const Index = (props: {
  visible: boolean;
  type: DownloadType;
  handleHiddenReport: Function;
  role: string;
}) => {
  const [monthValue, setMonthValue] = useState(moment().add(-1, 'month'));
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleOk = async () => {
    if (!monthValue) {
      message.error('请选择考核周期');
      return;
    }
    setConfirmLoading(true);
    try {
      if (props.type === DownloadType.PAYMENT) {
        const res = await KpiReportGatewayService.downloadKpiReportDataForMng({
          periodValue: monthValue?.format('YYYYMM'),
          source: 'MANAGER',
          businessGroups: props.role === 'direct' ? ['C33_DIRECT'] : ['C31_AGENT', 'C33_AGENT'],
        });
        const info = res?.resultMessage;
        message.success({ content: info });
      } else {
        const res = await request({
          apiKey: 'alsc-merchant-bill-center.DownloadService.syncDownload',
          params: [
            {
              beginMonth: monthValue?.format('YYYY-MM'),
              endMonth: monthValue?.format('YYYY-MM'),
              bizScene: 'C33_DIRECT_FUND_BILL_DOWNLOAD',
            },
          ],
        });
        const url = res?.data?.fileUrl;
        if (url) {
          downloadFile('', url);
        }
      }
      setConfirmLoading(false);
    } catch {
      setConfirmLoading(false);
    }
  };

  const handleCancel = () => {
    props.handleHiddenReport();
  };

  const onMonthChange = (v) => {
    setMonthValue(v);
  };

  return (
    <Modal
      title={props.type === DownloadType.BILL ? '月度明细下载' : '绩效基础数据下载'}
      open={props.visible}
      confirmLoading={confirmLoading}
      onOk={handleOk}
      onCancel={handleCancel}
      className="report-down-warp"
      width={600}
    >
      <div className="month-warp">
        <p>
          <span>* </span>
          {props.type === DownloadType.BILL ? '账单周期' : '考核周期'}
        </p>
        <DatePicker
          style={{ width: 450 }}
          picker="month"
          allowClear={false}
          onChange={onMonthChange}
          value={monthValue}
        />
      </div>
    </Modal>
  );
};

export default React.memo(Index);
