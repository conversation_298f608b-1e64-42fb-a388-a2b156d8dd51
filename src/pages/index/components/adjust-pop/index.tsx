import React, { useState, useEffect, useCallback } from 'react';
import {
  Form,
  Modal,
  DatePicker,
  Switch,
  Space,
  Button,
  message,
  Spin,
  Input,
} from 'antd';
import moment from 'moment';
import queryService from '@/_docplus/target/service/gateway/KpiPolicyQueryGatewayService';
import manageService from '@/_docplus/target/service/gateway/KpiPolicyManageGatewayService';
import { periodTypeEnum } from '@/common/Types';
import { PERIOD_FORMAT, PERIOD_TIME_FORMAT } from '@/common/constants';

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
/**
 * 开启核算弹窗
 * @param props
 * @returns
 */
const Index = (props) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const openAppealValue = Form.useWatch('appeal', form);

  const handleCancel = () => {
    props.handleHiddenAdjust();
  };

  const onFinish = (v) => {
    setConfirmLoading(true);
    const { appeal, flexibleBill, showCommission, appealStartTime, appealEndTime, periodValue, remark, reportPublishStage, reportWhiteList } = v || {};
    manageService.saveConfig({
      periodType: periodTypeEnum.MONTH,
      periodValue: moment(periodValue).format(PERIOD_FORMAT),
      appeal,
      flexibleBill,
      showCommission,
      appealStartTime: appealStartTime && moment(appealStartTime).format(PERIOD_TIME_FORMAT),
      appealEndTime: appealEndTime && moment(appealEndTime).format(PERIOD_TIME_FORMAT),
      remark: remark?.trim(),
      reportPublishStage: reportPublishStage ? "GRAY": "ALL",
      reportWhiteList: reportWhiteList?.trim(),
    } as any).then(() => {
      setConfirmLoading(false);
      message.success({ content: '保存成功' });
      props.handleHiddenAdjust();
    }).catch(() => {
      setConfirmLoading(false);
    });
  };

  const queryConfig = (value) => {
    setLoading(true);
    const periodValue = moment(value);
    queryService.queryConfig({
      periodType: periodTypeEnum.MONTH,
      periodValue: periodValue.format(PERIOD_FORMAT),
    }).then(res => {
      const { appeal, flexibleBill, showCommission, appealStartTime, appealEndTime, remark, reportPublishStage, reportWhiteList } = (res?.data || {}) as any;
      form.setFieldsValue({
        periodValue,
        appeal,
        flexibleBill,
        showCommission,
        appealStartTime: appealStartTime && moment(appealStartTime, PERIOD_TIME_FORMAT),
        appealEndTime: appealEndTime && moment(appealEndTime, PERIOD_TIME_FORMAT),
        remark: remark?.trim(),
        reportPublishStage: reportPublishStage === "GRAY" ? true : false,
        reportWhiteList,
      } as any);
      setLoading(false);
    }).catch(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    if (props.visible) {
      const defaultMonth = moment().add(-1, 'month');
      queryConfig(defaultMonth);
    }
  }, [props.visible]);

  const onMonthChange = useCallback((value) => {
    queryConfig(value);
  }, []);

  return (
    <Modal
      title="月度核算"
      open={props.visible}
      confirmLoading={confirmLoading}
      // onOk={handleOk}
      onCancel={handleCancel}
      footer={null}
      width={700}
    >
      <Spin spinning={loading} >
        <Form
          form={form}
          {...layout}
          onFinish={(forms) => onFinish({ ...forms })}
        >
          <Form.Item
            label="考核周期"
            name="periodValue"
            rules={[{ required: true }]}
          >
            <DatePicker
              picker="month"
              allowClear={false}
              onChange={onMonthChange}
            />
          </Form.Item>
          <Form.Item label="账单状态" name="flexibleBill" valuePropName="checked">
            <Switch checkedChildren="未结单" unCheckedChildren="已结单" />
          </Form.Item>
          <Form.Item label="薪酬展示" name="showCommission" valuePropName="checked">
            <Switch checkedChildren="开" unCheckedChildren="关" />
          </Form.Item>
          <Form.Item label="申诉开启" name="appeal" valuePropName="checked">
            <Switch checkedChildren="开" unCheckedChildren="关" />
          </Form.Item>
          {openAppealValue &&
            <>
              <Form.Item
                label="申诉开启时间"
                name="appealStartTime"
              >
                <DatePicker showTime />
              </Form.Item>
              <Form.Item
                label="申诉截止时间"
                name="appealEndTime"
                rules={[{ required: openAppealValue }]}
              >
                <DatePicker showTime />
              </Form.Item>
            </>
            }
          <Form.Item
            label="战报开放阶段"
            name="reportPublishStage"
            valuePropName="checked"
          >
            <Switch checkedChildren="灰度阶段" unCheckedChildren="全量阶段" />
          </Form.Item>
          <Form.Item
            label="战报开放白名单"
            name="reportWhiteList"
            rules={[{
              pattern: /^\d+(,\d+)*$/,
              message: '只允许输入数字，多个用英文逗号分隔',
            }]}
          >
            <Input.TextArea style={{ width: 400 }} rows={2} placeholder="请输入白名单，多个用英文逗号分隔" />
          </Form.Item>
          <Form.Item
            label="备注"
            name="remark"
          >
            <Input.TextArea
              style={{ width: 400 }}
              rows={4}
            />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 9, span: 16 }}>
            <Space>
              <Button htmlType="button" onClick={handleCancel}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={confirmLoading}>
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default React.memo(Index);
