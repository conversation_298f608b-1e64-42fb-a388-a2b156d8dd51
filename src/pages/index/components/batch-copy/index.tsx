import React, { useState } from 'react';
import { Modal, DatePicker, Form, message, Checkbox } from 'antd';
import moment from 'moment';
import { batchCopy } from '../../service';
import './index.less';
import { set } from 'js-cookie';

/**
 * 批量复制弹窗
 * @param props
 * @returns
 */
const Index = (props: { selectedRows: any; handleHidden: Function, handleReresh: Function }) => {
  const [showCopyModal, setShowCopyModal] = useState(true); // 批量复制弹窗
  const [confirmLoading, setConfirmLoading] = useState(false);

  const [form] = Form.useForm();

  /**
   * 处理表单提交
   * @param skip 是否跳过校验，给 handleError 中的二次弹窗用
   */
  const handleOk = async (skip) => {
    const values = await form.validateFields();
    const { periodValue } = values;

    setConfirmLoading(true);
    try {
      const result = await batchCopy({
        periodValue: periodValue.format('YYYYMM'),
        policyIdList: props.selectedRows.map((item) => item.policyId),
        skipVerify: skip,
      });

      setConfirmLoading(false);
      setShowCopyModal(false);

      handleResShow(result);
    } catch (error) {
      setConfirmLoading(false);
      handleError(error);
    }
  };

  /**
   * 处理接口报错
   */
  const handleError = (error) => {
    if (error?.errorCode === 'PRE_DEPENDENCY_POLICY_MISS') {
      Modal.confirm({
        title: '前置依赖缺失',
        content: (
          <div>
            {error.errorMessage}
            <p>是否继续提交？</p>
          </div>
        ),
        okText: '继续提交',
        cancelText: '取消',
        onOk: () => {
          handleOk(true);
        },
      });
    } else {
      message.error(error?.errorMessage || '复制失败');
    }
  };

  /**
   * 展示复制结果
   * @param res
   */
  const handleResShow = (res) => {
    const { successPolicyId = [], failedPolicyId = [], filterPolicyId = [], passFailedPolicyId = [] } = res?.data;

    Modal.info({
      title: '复制结果',
      width: 600,
      content: (
        <div style={{ padding: '40px 0 20px 0' }}>
          <p>复制并发布成功的政策列表：{successPolicyId.join(',')}</p>
          <p>复制失败的政策列表：{failedPolicyId.join(',')}</p>
          <p>过滤未复制的政策列表：{filterPolicyId.join(',')}</p>
          <p>发布失败的政策列表：{passFailedPolicyId.join(',')}</p>
        </div>
      ),
      okText: '确定',
      onOk: () => {
        props.handleHidden();
        props.handleReresh();
      },
    });
  };

  const handleCancel = () => {
    props.handleHidden();
  };

  return (
    <div>
      {/* 批量复制弹窗 */}
      <Modal
        title={`批量复制（${props.selectedRows?.length || 0}个）`}
        open={showCopyModal}
        confirmLoading={confirmLoading}
        onOk={() => handleOk(false)}
        onCancel={handleCancel}
        className="report-down-warp"
        width={600}
      >
        <Form
          form={form}
          initialValues={{ periodValue: moment() }}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 16 }}
        >
          <Form.Item
            label="复制到"
            name="periodValue"
            rules={[{ required: true, message: '请选择目标月份' }]}
          >
            <DatePicker style={{ width: 300 }} picker="month" allowClear={false} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default React.memo(Index);
