/* eslint-disable no-use-before-define */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button, Table, Space, Popover, Modal, Row, message, Tooltip, Form } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import moment from 'moment';
import PlanManageFacade from '@/_docplus/target/service/gateway/KpiPolicyQueryGatewayService';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import {
  planStateEnum,
  planStateListEnum,
  containButtonStateMap,
  planStateActionEnum,
  periodTypeListEnum,
  ActionTypeEnum,
} from '@/common/Types';
import CommonStructure from '@/common/components/CommonStructure';
import { PERIOD_TIME_FORMAT } from '@/common/constants';
import { KpiPolicyQueryGatewayRequest, KpiPolicyDTO } from '@/_docplus/target/types/gateway';
import store from '../store';
import SeachList from './seach-list';
import DownloadPop, { DownloadType } from './report-pop';
import AdjustPop from './adjust-pop';
import BatchCopy from './batch-copy';
import { POLICY_TYPE_ENUM, POLICY_TYPE_PARAMS } from './seach-list/constant';

import './index.less';

const { confirm } = Modal;

interface IPageData {
  pageSize: number;
  current: number;
  total: number;
}

const Index: React.FC = (props) => {
  console.log(props);

  const storeIns = store.useStore();
  const businessGroup = storeIns?.store?.role === 'agent' ? 'C33_AGENT,C31_AGENT' : 'C33_DIRECT';

  const pageInfo = useRef<IPageData>({
    pageSize: 10,
    current: 1,
    total: 0,
  });

  const [planValueList, setPlanValueList] = useState<any>([]);
  const [isShowAdjust, setIsShowAdjust] = useState(false); // 开启核算
  const [isShowReport, setIsShowReport] = useState(false); // 薪酬明细下载
  const [isShowBill, setIsShowBill] = useState(false); // 月度明细下载
  const [isShowBatch, setIsShowBatch] = useState(false); // 批量复制
  const [selectedRows, setSelectedRows] = useState<any>([]); // 批量复制选中项

  const [form] = Form.useForm();
  const planSource = Form.useWatch(['planSource'], form);
  const formInfos = useRef({});

  const content = useCallback(
    (record: KpiPolicyDTO) => (
      <Space>
        <Button
          size="small"
          type="link"
          onClick={() => changeStatus(record?.policyId, planStateEnum.PASS, record?.policyType)}
          disabled={
            !record.canEdit || !containButtonStateMap.RELEASE.includes(record?.policyStatus)
          }
        >
          发布
        </Button>
        <Button
          size="small"
          type="link"
          onClick={() => changeStatus(record?.policyId, planStateEnum.INVALID, record?.policyType)}
          disabled={!record.canEdit || !containButtonStateMap.STOP.includes(record?.policyStatus)}
        >
          停用
        </Button>
        <Button
          size="small"
          type="link"
          onClick={async () => {
            openAdd(ActionTypeEnum.COPY, record?.policyId);
          }}
        >
          复制
        </Button>
        <Button
          size="small"
          danger
          type="text"
          onClick={() => changeStatus(record?.policyId, planStateEnum.DELETED, record?.policyType)}
          disabled={!containButtonStateMap.DELETE.includes(record?.policyStatus)}
        >
          删除
        </Button>
      </Space>
    ),
    [],
  );

  const columns: ColumnsType<KpiPolicyDTO> = [
    {
      title: '方案名称',
      dataIndex: 'policyName',
      key: 'policyName',
      width: 300,
      fixed: 'left',
    },
    storeIns?.store?.role === 'agent'
      ? {
          title: '业务域',
          dataIndex: 'policyType',
          key: 'policyType',
          width: 200,
          render: (_, record) => POLICY_TYPE_ENUM[record?.policyType],
        }
      : {},
    {
      title: '方案ID',
      dataIndex: 'policyId',
      key: 'policyId',
      width: 200,
    },
    {
      title: '周期类型',
      dataIndex: 'commDurationUnit',
      key: 'commDurationUnit',
      render: (_, record) => periodTypeListEnum[record?.periodType],
    },
    {
      title: '生效时间段',
      dataIndex: 'gmtEffect',
      key: 'gmtEffect',
      render: (_, record) =>
        record?.gmtEffect && record?.gmtExpire
          ? `${moment(record.gmtEffect).format('YYYY-MM-DD')}-${moment(record.gmtExpire).format(
              'YYYY-MM-DD',
            )}`
          : '--',
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      render: (_, record) =>
        record?.gmtCreate ? moment(record.gmtCreate).format(PERIOD_TIME_FORMAT) : '--',
    },
    {
      title: '修改时间',
      dataIndex: 'gmtModified',
      key: 'gmtModified',
      render: (_, record) =>
        record?.gmtModified ? moment(record.gmtModified).format(PERIOD_TIME_FORMAT) : '--',
    },
    {
      title: '负责人',
      dataIndex: 'creator',
      key: 'creator',
      render: (_, record) => (
        <Tooltip placement="topLeft" title={record.creator}>
          {record.creator}
        </Tooltip>
      ),
    },
    {
      title: '修改人',
      dataIndex: 'modifier',
      key: 'modifier',
    },
    {
      title: '状态',
      dataIndex: 'policyStatus',
      key: 'policyStatus',
      render: (_, record) => planStateListEnum[record.policyStatus],
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      render: (_, record) => (
        <Space size="middle" className="btn-list">
          <Button
            size="small"
            className="u-px-0"
            type="link"
            onClick={() => openAdd(ActionTypeEnum.EDIT, record?.policyId)}
            disabled={!record.canEdit || !containButtonStateMap.EDIT.includes(record?.policyStatus)}
          >
            编辑
          </Button>
          <Button
            size="small"
            className="u-px-0"
            type="link"
            onClick={() => openAdd(ActionTypeEnum.SEE, record?.policyId)}
          >
            查看
          </Button>
          <Popover
            content={() => content(record)}
            trigger="click"
            placement="left"
            overlayStyle={{
              zIndex: 9,
            }}
          >
            <Button size="small" className="u-px-0" type="link">
              更多
            </Button>
          </Popover>
        </Space>
      ),
    },
  ]?.filter((column) => column?.dataIndex);

  /** 查询方案列表 */
  const listPlan = async (params) => {
    try {
      message.loading({ key: 'loading', content: '正在加载列表数据' });

      const res = await PlanManageFacade.queryPolicyList(
        {
          ...params,
          businessGroup: params?.businessGroup || businessGroup,
        },
        {
          disableLoading: true,
        },
      );
      message.destroy('loading');
      pageInfo.current = {
        current: pageInfo.current.current || 1,
        pageSize: pageInfo.current.pageSize || 10,
        total: res?.data?.pageInfoDTO?.totalCount,
      };
      setPlanValueList(res?.data?.kpiPolicyDTOList);
    } catch (error) {
      pageInfo.current = {
        current: 1,
        pageSize: 10,
        total: 0,
      };
      setPlanValueList([]);
    }
  };

  // 跳转详情页
  const openAdd = (type = ActionTypeEnum.ADD, planId = '') => {
    const prefix =
      window.APP?.isFromKbServ === 'true' ? '/sale-pc/mp-kpi-assess-plan' : '/mp-kpi-assess-plan';
    if (type === ActionTypeEnum.ADD) {
      window.open(`${prefix}/detail?type=${type}&role=${storeIns?.store?.role}`);
    } else {
      window.open(`${prefix}/detail?type=${type}&planId=${planId}&role=${storeIns?.store?.role}`);
    }
  };

  // 操作考核方案
  const changeStatus = useCallback(
    async (planId: string | number, planStatus: string, policyType?: string) => {
      confirm({
        title: `确认${planStateActionEnum[planStatus]}政策？`,
        icon: <ExclamationCircleOutlined />,
        onOk() {
          handleActionFunc(planId, planStatus, policyType);
        },
      });
    },
    [planSource],
  );

  const handleActionFunc = useCallback(
    async (planId, planStatus, policyType) => {
      try {
        const res = await storeIns.updatePlanStatus({
          kpiPolicyId: planId,
          bizAction: planStatus,
          businessGroup:
            storeIns?.store?.role === 'agent' ? POLICY_TYPE_PARAMS[policyType] : 'C33_DIRECT',
        });
        if (!res?.success) {
          return;
        }
        if (planStatus === planStateEnum.PASS) {
          message.success({
            content: '政策发布成功',
            key: 'release',
            duration: 10,
          });
        } else if (planStatus === planStateEnum.INVALID) {
          message.success({ content: '停用成功', key: 'loading' });
        }

        const request = {
          ...formInfos.current,
          pageNo: pageInfo.current.current,
          pageSize: pageInfo.current.pageSize,
        };
        setTimeout(() => {
          listPlan(request);
        }, 1500);
      } catch (error) {
        message.error({ content: error.errorMessage, key: 'loading' });
      }
    },
    [planSource],
  );

  const pagination = {
    current: pageInfo.current.current || 1,
    pageSize: pageInfo.current.pageSize || 10,
    total: pageInfo.current.total || 0,
    showTotal: () => `共${pageInfo.current.total || 0}条`,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20'],
    showQuickJumper: true,
  };

  const changeTable = (v) => {
    pageInfo.current = {
      pageSize: v?.pageSize,
      current: v?.current,
      total: v?.total,
    };
    const request: Partial<KpiPolicyQueryGatewayRequest> = {
      ...formInfos.current,
      pageNo: v?.current,
      pageSize: v?.pageSize,
    };
    listPlan(request);
  };

  const handleSearch = (value) => {
    const request: Partial<KpiPolicyQueryGatewayRequest> = {
      pageNo: 1,
      pageSize: pageInfo.current.pageSize || 10,
      ...value,
    };
    pageInfo.current = { ...pageInfo.current, current: 1 };
    formInfos.current = { ...formInfos.current, ...value };
    listPlan(request);
  };

  useEffect(() => {
    handleSearch({});
  }, []);

  const rowSelection = {
    preserveSelectedRowKeys: true,
    onChange: (selectedRowKeys: React.Key[], selectedRows) => {
      setSelectedRows(selectedRows);
    },
    getCheckboxProps: (record) => ({
      disabled: record.policyStatus !== 'PASS', // 只有生效中的政策才能选择
    }),
  };

  return (
    <div>
      {/* 搜索 */}
      <SeachList form={form} handleSearch={handleSearch} storeIns={storeIns} />

      <CommonStructure.Section
        title={<span className="u-fs-16">项目方案列表</span>}
        extra={
          <Row justify="end">
            <Space>
              {
                // 直营的时候才展示这些按钮
                storeIns?.store?.role === 'direct' && (
                  <>
                    <Button
                      onClick={() => setIsShowBatch(true)}
                      disabled={selectedRows.length === 0}
                    >
                      批量复制
                    </Button>
                    <Button onClick={() => setIsShowAdjust(true)}>月度核算</Button>

                    <Button onClick={() => setIsShowBill(true)}>月度账单下载</Button>
                  </>
                )
              }
              <Button onClick={() => setIsShowReport(true)}>绩效基础数据下载</Button>
              <Button type="primary" onClick={() => openAdd(ActionTypeEnum.ADD)}>
                新增项目
              </Button>
            </Space>
          </Row>
        }
      >
        <Table
          style={{ marginTop: 16 }}
          columns={columns}
          dataSource={planValueList}
          pagination={{ ...pagination }}
          onChange={changeTable}
          rowKey="policyId"
          scroll={{ x: 'max-content' }}
          rowSelection={storeIns?.store?.role === 'direct' ? {
            type: 'checkbox',
            ...rowSelection,
          } : null}
        />
      </CommonStructure.Section>

      {/* 弹窗 */}
      <AdjustPop
        visible={isShowAdjust}
        handleHiddenAdjust={() => {
          setIsShowAdjust(false);
          listPlan({
            ...formInfos.current,
            pageNo: pageInfo.current?.current || 1,
            pageSize: pageInfo.current?.pageSize || 10,
          });
        }}
      />
      <DownloadPop
        type={DownloadType.PAYMENT}
        visible={isShowReport}
        handleHiddenReport={() => setIsShowReport(false)}
        role={storeIns?.store?.role}
      />
      <DownloadPop
        type={DownloadType.BILL}
        visible={isShowBill}
        handleHiddenReport={() => setIsShowBill(false)}
      />
      {isShowBatch && (
        <BatchCopy selectedRows={selectedRows} handleHidden={() => setIsShowBatch(false)} handleReresh={() => form.submit()} />
      )}
    </div>
  );
};

export default Index;
