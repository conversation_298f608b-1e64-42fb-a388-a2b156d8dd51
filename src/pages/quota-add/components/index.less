.tail-style {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 64px;
  left: 0;
  margin-bottom: 0;
  line-height: 64px;
  display: flex;
  // justify-content: flex-end;
  justify-content: center;
  background: #FFF;
  box-shadow: 0 -1px 0 0 #ebeef5;
  z-index: 9;
}

.quota-mask {
  width: 100%;
  height: 100%;
  background-color: #efefef4f;
  position: fixed;
  z-index: 1000;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.detail-bread-container {
  position: relative;
  z-index: 1002;
}

.pre-event {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.8;

  .anticon {
    pointer-events: all !important;
  }
}

.no-pre-event {
  pointer-events: auto !important;
  cursor: pointer;
}
