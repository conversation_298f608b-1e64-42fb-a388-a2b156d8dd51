import React, { useEffect, useState } from 'react';
import {
  Select,
  Divider,
  Space,
  Input,
  Button,
  message,
  SelectProps,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';

interface Props {
  options?: SelectProps['options'];
  value?: string[];
  onChange?: (e: any) => void;
  isSingle?: boolean;
}

/**
 * select tags 单选、多选组件支持新增menu
 * @param param0 
 * @returns 
 */

const SelectMenuAdd: React.FC<Props> = ({
  options: $options,
  value: $value,
  onChange,
  isSingle = false,
}) => {
  const [value, setValue] = useState($value);
  const [options, setOptions] = useState($options);
  const [field, setField] = useState('');
  const [code, setCode] = useState('');

  useEffect(() => {
    setValue($value);
  }, [$value]);

  useEffect(() => {
    setOptions($options);
  }, [$options]);

  const onHandleChange = (v) => {
    const newV = isSingle && v.length > 1 ? v.slice(v.length - 1) : v;
    setValue(newV);
    onChange && onChange(newV);
  };

  const addItem = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    if (!field) {
      message.error('请输入中文名');
      return;
    }
    if (!code) {
      message.error('请输入英文名');
      return;
    }
    setOptions([
      ...options, 
      {
        label: field,
        value: `${field}/${code}`
      }
    ]);
    setField('');
    setCode('');
  };
  return (
    <Select
      mode="tags"
      // style={{ width: '100%' }}
      value={value}
      placeholder="请选择"
      onChange={onHandleChange}
      options={options}
      dropdownRender={menu => (
        <>
          {menu}
          <Divider style={{ margin: '8px 0' }} />
          <Space style={{ padding: '0 8px 4px' }} align="center">
            <Input value={field} onChange={(e) => setField(e.target.value)} placeholder="请输入中文名" />
            <Input value={code} onChange={(e) => setCode(e.target.value)} placeholder="请输入英文名" />
            <Button type="text" icon={<PlusOutlined />} onClick={addItem}>
              新增
            </Button>
          </Space>
        </>
      )}
    />
  );
};

export default SelectMenuAdd;
