import React, { useState, useEffect, useMemo } from 'react';
import {
  Form,
  Select,
  Input,
} from 'antd';
import moment from 'moment';
// import classNames from 'classnames';
import { BusinessGroupEnum, BusinessLineEnum, TimeTypeEnum } from '@/common/Types/index';
import ModuleVersionSelector from '@/common/components/ModuleVersionSelector';
import store from '../../store';

// import './index.less';

const { TextArea } = Input;

interface Props {
  type?: string;
  businessGroupVal?: string;
  form: any;
}

const BaseInfo: React.FC<Props> = ({
  form,
  type,
  businessGroupVal,
}) => {
  const indexStore = store.useStore();
  const [versionList, setVersionList] = useState<string[]>([]);

  const isAddVersionDisabled = useMemo(() => {
    if ((type === 'add' || type === '') && versionList.length > 0) {
      return true;
    }

    if (type === 'edit' && indexStore.store.originalIndicatorData) {
      const firstVersionInfo = indexStore.getFirstVersionInfo();
      console.log('firstVersionInfo', firstVersionInfo);

      if (firstVersionInfo && firstVersionInfo.indicatorType === 'GROUP_INDICATOR') {
        return true;
      }

      // ODPS 指标禁用添加版本功能
      if (firstVersionInfo && firstVersionInfo.indicatorType === 'ODPS') {
        return true;
      }
    }

    return false;
  }, [type, versionList.length, indexStore.store.originalIndicatorData]);
  
  useEffect(() => {
    const newVersionList = indexStore.store.defaultFormData?.versionList || [];
    setVersionList(newVersionList);
  }, [indexStore.store.defaultFormData?.versionList]);

  const handleVersionListChange = (newList: string[]) => {
    if (newList.length > versionList.length) {
      const latestVersion = newList[newList.length - 1];
      handleVersionClick(latestVersion);
    }
    setVersionList(newList);
  };

  // 版本切换处理
  const handleVersionClick = (version: string) => {
    // 判断是否是新加的版本
    const isNewVersion = !indexStore.store.originalIndicatorData?.indicatorRules?.find(
      (rule) => (rule?.ruleCondition as any)?.ruleVersion === version.replace('-', '')
    );

    if (isNewVersion) {
      // 获取第一个版本的信息来设置默认值
      const firstVersionInfo = indexStore.getFirstVersionInfo();
      let defaultIndicatorType = 'COMPLEX_INDICATOR';
      let defaultIndicatorValueType = 'NUMBER';
      let defaultConfigType = undefined;

      if (firstVersionInfo) {
        defaultIndicatorType = firstVersionInfo.indicatorType;
        console.log('firstVersionInfo', firstVersionInfo,111111,indexStore.store.originalIndicatorData);
        
        defaultIndicatorValueType = firstVersionInfo.indicatorValueType;
        defaultConfigType = firstVersionInfo.configType;
      }

      // 重置相关字段
      form.setFieldsValue({
        indicatorType: defaultIndicatorType,
        ruleGroupType: defaultIndicatorType === 'COMPLEX_INDICATOR' ? 'EXPRESSION' : undefined,
        indicatorValueType: defaultIndicatorValueType,
        expressionObj: undefined,
        datasourceId: undefined,
        tableColumn: undefined,
        tableDimColumn: undefined,
        tableExtraColumn: undefined,
        groupIndicatorAggType: undefined,
        sortType: undefined,
        groupRuleDim: undefined,
        groupTargetType: 'ALL_DEPT_STAFF',
        groupValueType: undefined,
        tableColumnGroupIndicator: undefined,
        itemCode: undefined,
        staffRelationType: undefined,
        indicatorAggType: undefined,
        traceCycle: undefined,
        lakeIndicatorCode: undefined,
        sqlTemplateCode: undefined,
        configType: defaultConfigType,
        ruleVersion: moment(version.replace('-', ''), 'YYYYMM'),
      });
    } else {
      // 老版本，走原有逻辑
      indexStore.switchToVersion(version, form);
    }
  };

  return (
    <>
    <Form.Item
      label="业务域"
      name="businessGroup"
    >
      <Select
        disabled={!!type}
        options={BusinessGroupEnum}
      />
    </Form.Item>
    <Form.Item
      label="业务线"
      name="businessLine"
    >
      <Select
        disabled={!!type}
        options={BusinessLineEnum[businessGroupVal]}
      />
    </Form.Item>
    <Form.Item
      label="指标周期"
      name="timeType"
    >
      <Select
        disabled
        options={TimeTypeEnum}
      />
    </Form.Item>
    <Form.Item
      extra="推荐名称:时间+简述。例:月底门店行业、当月门店累计笔数"
      label="指标名称"
      name="name"
      rules={[
        {
          required: true,
          message: '请输入指标名称',
        },
        {
          max: 50,
          message: '指标名称不能超过50字',
        },
      ]}
    >
      <TextArea
        placeholder="请填写，名称在业务模型内不能重复"
        maxLength={50}
        autoSize
      />
    </Form.Item>
    <Form.Item
      label="口径描述"
      name="description"
      rules={[
        {
          max: 1000,
          message: '最多输入一千个字',
        },
      ]}
    >
      <TextArea placeholder="此信息将展示在战报上供用户参考" />
    </Form.Item>
    <Form.Item label="指标版本" name="ruleVersion" rules={[{ required: true, message: '请选择版本' }]}>
      <ModuleVersionSelector
        editType={type || ''}
        versionList={versionList}
        onVersionListChange={handleVersionListChange}
        onVersionClick={handleVersionClick}
        disableAddVersion={isAddVersionDisabled}
      />
    </Form.Item>
    {(type === 'edit' || type === 'detail') &&
    <Form.Item
      label="指标编号"
      name="indicatorId"
    >
      <TextArea
        disabled={type === 'edit'}
        placeholder="当一个指标同时存在于多个层级时，通过配置相同的code以创建关联关系"
        autoSize
      />
    </Form.Item>
      }
  </>
)};

export default BaseInfo;
