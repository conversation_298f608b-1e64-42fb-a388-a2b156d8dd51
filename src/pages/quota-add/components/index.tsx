import React, { useState, useEffect } from 'react';
import CommonStructure from '@/common/components/CommonStructure';
import IndicatorGatewayService from '@/_docplus/target/service/gateway/IndicatorGatewayService';
import { Form, Button, message, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { BusinessLineEnum } from '@/common/Types';
import { useHistory } from 'react-router-dom';
import store from '../store';
import moment from 'moment';

import BaseInfo from './BaseInfo';
import DataProduction from './DataProduction';
import QuarterInfo from './QuarterInfo';

import './index.less';

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 11 },
};

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

function getTitle(type) {
  let title = '新增指标';
  switch (type) {
    case 'copy':
      title = '复制指标';
      break;
    case 'edit':
      title = '编辑指标';
      break;
    case 'detail':
      title = '指标详情';
      break;
    default:
      title = '新增指标';
      break;
  }
  return title;
}

const Index: React.FC<any> = () => {
  // 全局数据
  const indexStore = store.useStore();
  const {
    currentBusinessInfoSimpleData,
    defaultFormData,
    urlParam,
    tableDimColumn,
    tableExtraColumn,
  } = indexStore.store;

  const [form] = Form.useForm();
  const businessGroupVal = Form.useWatch('businessGroup', form);
  const history = useHistory();
  // 初局部数据
  const [loading, setLoading] = useState(false);
  const [type, setType] = useState<string>('');
  const [indicatorId, setIndicatorId] = useState<string>('');

  // 初始化
  async function init() {
    try {
      const { indicatorId: argIndicatorId, type: argType } = urlParam;
      if (argIndicatorId && argType) {
        setLoading(true);
        setType(argType);
        setIndicatorId(argIndicatorId);
        await indexStore.getCurrentIndicatorDetail(argIndicatorId); // 获取详情
      }
    } catch (error) {
      setLoading(false);
    } finally {
      setLoading(false);
    }
  }

  // 新增指标
  const createIndicator = useRequest(IndicatorGatewayService.createIndicator, {
    manual: true,
    onSuccess: ({ data }) => {
      if (data) {
        message.success('创建指标成功');
        history.push('/quota-index');
      } else {
        console.log('创建指标失败--->', data);
      }
    },
  });

  // 更新指标
  const updateIndicator = useRequest(IndicatorGatewayService.modifyIndicator, {
    manual: true,
    onSuccess: ({ data }) => {
      if (data) {
        console.log('更新指标成功--->', data);
        message.success('更新指标成功');
        history.push('/quota-index');
      } else {
        console.log('更新指标失败--->', data);
      }
    },
  });

  const onValuesChange = (values) => {
    console.log('%c 🍿 form变化: ', 'font-size:20px;background-color: #4b4b4b;color:#fff;', values);
  };

  const onFinish = async (o: any) => {
    const tempData = await indexStore.pareForm(Object.assign({}, o));

    const formData = { ...tempData };
    if (type === '' && indicatorId === '') {
      createIndicator.run({ indicatorDTO: formData } as any);
    }
    if (type === 'copy' && indicatorId !== '') {
      createIndicator.run({ indicatorDTO: formData } as any);
    }
    if (type === 'edit' && indicatorId !== '') {
      updateIndicator.run({
        indicatorDTO: {
          indicatorId,
          ...formData,
        },
      } as any);
    }
    return null;
  };

  const initFormData = () => {
    form.setFieldsValue({
      // 基础信息
      businessGroup: defaultFormData.businessGroup,
      businessLine: defaultFormData.businessLine,
      timeType: defaultFormData.timeType,
      name: defaultFormData.name,
      description: defaultFormData.description,
      indicatorId: defaultFormData.indicatorId,
      ruleVersion: defaultFormData.ruleVersion ? moment(defaultFormData.ruleVersion, 'YYYY-MM') : null,
      // 数据生产
      indicatorType: defaultFormData.indicatorType, // 生产方式
      ruleGroupType: defaultFormData.ruleGroupType, // 规则类型
      indicatorValueType: defaultFormData.indicatorValueType, // 字段类型

      expressionObj: defaultFormData.expressionObj, // 公式
      datasourceId: defaultFormData?.datasourceId, // 数据源
      tableColumn: defaultFormData?.tableColumn, // 指标值字段
      expression: defaultFormData?.expression, // sql
      // odps独有字段
      tableDimColumn: defaultFormData?.tableDimColumn,
      tableExtraColumn: defaultFormData?.tableExtraColumn,
      // 数据湖独有字段
      lakeIndicatorCode: defaultFormData?.lakeIndicatorCode,
      sqlTemplateCode: defaultFormData?.sqlTemplateCode,
      // 取值/计算方式
      configType: defaultFormData?.configType,
      groupIndicatorAggType: defaultFormData?.groupIndicatorAggType,
      sortType: defaultFormData?.sortType,
      groupRuleDim: defaultFormData?.groupRuleDim,
      groupTargetType: defaultFormData?.groupTargetType,
      groupValueType: defaultFormData?.groupValueType,
      tableColumnGroupIndicator: defaultFormData?.tableColumnGroupIndicator,
      itemCode: defaultFormData?.itemCode,
      staffRelationType: defaultFormData?.staffRelationType,
      indicatorAggType: defaultFormData?.indicatorAggType,
      traceCycle: defaultFormData?.traceCycle,
    });
  };

  const handleBusinessGroup = async (val) => {
    if (!val) {
      return;
    }

    if (!type) {
      form.setFieldsValue({
        businessLine: BusinessLineEnum[val]?.[0]?.value,
        expressionObj: {
          expression: null,
          upperLimit: null,
          lowLimit: null,
        },
      });
    }

    await indexStore.getCurrentIndicatorByCondition({
      pageNum: 1,
      pageSize: 2001,
      includeIndicatorRule: false,
      includeIndicatorDatasource: false,
      businessGroup: val,
    } as any); // 获取详情
  };

  useEffect(() => {
    init();
  }, []);

  // 初始化表单
  useEffect(() => {
    initFormData();
  }, [defaultFormData]);

  useEffect(() => {
    handleBusinessGroup(businessGroupVal);
  }, [businessGroupVal]);

  return (
    <CommonStructure>
      <CommonStructure.Header title={getTitle(type)} />
      <Spin indicator={antIcon} spinning={loading}>
        <Form
          form={form}
          {...layout}
          colon={false}
          name="quota-add"
          onValuesChange={onValuesChange}
          onFinish={onFinish}
          className={type === 'detail' ? 'pre-event' : ''}
        >
          <CommonStructure.Section title="基础信息">
            <BaseInfo type={type} businessGroupVal={businessGroupVal} form={form} />
          </CommonStructure.Section>

          <CommonStructure.Section title="数据生产">
            <DataProduction
              type={type}
              currentBusinessInfoSimpleData={currentBusinessInfoSimpleData}
              tableDimColumn={tableDimColumn}
              tableExtraColumn={tableExtraColumn}
              form={form}
            />
          </CommonStructure.Section>

          <CommonStructure.Section title="多月/季度政策取值">
            <QuarterInfo type={type} />
          </CommonStructure.Section>

          <Form.Item className="tail-style">
            <Button
              type="primary"
              onClick={() => {
                form.submit();
              }}
              hidden={type === 'detail'}
            >
              {type === 'edit' && indicatorId !== '' ? '更新' : '提交'}
            </Button>
          </Form.Item>
        </Form>
      </Spin>
    </CommonStructure>
  );
};

export default Index;
