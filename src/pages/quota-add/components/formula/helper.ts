import { ExpressionType, OptionItemTypePrefix, REG_EXPRESSION } from '@/common/components/formula/constant';
import { FormulaDataItem, OptionItem, FormulaValue } from '@/common/components/formula/type';


/** 将内部表达式结构转成表单数据 */
export function defaultParseDataToForm(formulaData: FormulaDataItem[], debug?: boolean) {
  let type = ExpressionType.Common;
  let expression = '';
  let textExpression = '';

  if (debug) {
    console.log('Formula defaultBuildDataToForm', formulaData);
  }
  /** 数据对象 */
  const varMap = formulaData.reduce((prev, next) => {
    if (next.value) {
      const prefix = OptionItemTypePrefix[next.type] || '';
      // eslint-disable-next-line no-param-reassign
      prev[`${prefix}${next.value}`] = next.label;
    }
    return prev;
  }, {});

  formulaData.forEach(item => {
    if (item.date) {
      type = ExpressionType.Time;
    }
    if (item.value) {
      const prefix = OptionItemTypePrefix[item.type] || '';
      expression += item.date ? `executePeriodVar(${prefix}${item.value},MONTH,${item.date},content)` : `${prefix}${item.value}`;
      textExpression += item.date ? `executePeriodVar(${prefix}${item.label},MONTH,${item.date},content)` : item.label;
    } else {
      expression += item.label.replace(/\s/g, '');
      textExpression += item.label.replace(/\s/g, '');
    }
  });

  if (debug) {
    console.log('Formula defaultBuildDataToForm', {
      expression,
      type,
      varMap,
      textExpression,
    });
  }

  if (expression) {
    return {
      expression,
      type,
      varMap,
      textExpression,
    };
  } return undefined;
}

/**
 * 将表单数据转成内部表达式结构
 * @example expression: executePeriodVar(@123,2022-05-20)/@123'
 */
export function defaultParseFormToData(
  formData: FormulaValue,
  indecatorData: OptionItem[],
  debug?: boolean,
) {
  /** 匹配ID的正则 */
  const idMatchExp = Object
    .keys(formData.varMap)
    .filter(o => !!o.replace(/\d/g, ''))
    .reduce(
      (prev, next) => {
        const curItem = next.includes('$') ? `\\${next}` : next;
        return prev.concat([curItem, `executePeriodVar\\(${curItem},MONTH,\\d{6},content\\)`]);
      }
      , [],
    )
    .join('|');

  /** 公式中的指标 */
  const indicatorList = formData?.expression?.match(new RegExp(`(${idMatchExp})`, 'g'))
    ?.filter(o => !!o);
  if (!indicatorList?.length) {
    /** 如果没有变量则直接返回文本表达式 */
    if (formData.expression) return [{ label: formData.expression }];
    return [];
  }

  const result: FormulaDataItem[] = [];
  /** 待处理公式 */
  let remainFormula = formData.expression;

  indicatorList.forEach((item, index) => {
    /** 指标元素结果数据 */
    const resultItem: FormulaDataItem = { label: '' };
    /** 当前指标所在开始索引 */
    const itemStartIndex = remainFormula.indexOf(item);
    /** 替换掉当前指标前缀字符串 */
    const prefix = remainFormula.substring(0, itemStartIndex);
    /** 替换掉当前指标后缀字符串 */
    const subfix = remainFormula.substring(itemStartIndex + item.length);

    if (prefix) {
      result.push({ label: prefix });
    }

    /** 通过正常匹配出ID */
    const idSection = item.match(REG_EXPRESSION.IdWithPrefix)?.[0] || '';
    resultItem.value = idSection.slice(1);
    resultItem.type = Object.entries(OptionItemTypePrefix).find(o => o[1] === idSection[0])?.[0] || '';
    resultItem.label = indecatorData.find(o => o.value === resultItem.value)?.label || '';
    resultItem.weight = indecatorData.find(o => o.value === resultItem.value)?.weight || '';

    resultItem.date = (item.match(REG_EXPRESSION.DateFromExp)?.[0] || '').substring(1);
    result.push(resultItem);
    remainFormula = subfix || '';

    if (index === indicatorList.length - 1 && subfix) {
      result.push({ label: subfix });
    }
  });

  if (debug) console.log('Formula defaultParseFormToData', result);

  return result;
}
