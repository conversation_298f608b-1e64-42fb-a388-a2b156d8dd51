import React from 'react';
import { Form, Select } from 'antd';
import { configTypeOptions } from '@/pages/quota-add/const';
import store from '../../store';

interface Props {
  type: string;
}

const QuarterInfo: React.FC<Props> = ({
  type,
}) => {
  const indexStore = store.useStore();

  return (
  <>
    <Form.Item
      name="configType"
      label="取值/计算方式"
      rules={[
        {
          required: true,
          message: '请选择取值/计算方式',
        },
      ]}
    >
      <Select
        disabled={!indexStore.isIndicatorValueTypeEditable(type || '')}
        placeholder="请选择"
        options={configTypeOptions}
      />
    </Form.Item>
  </>
  );
};

export default QuarterInfo;
