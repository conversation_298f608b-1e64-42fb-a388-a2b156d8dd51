import React from 'react';
import { Form, Select } from 'antd';
import { COST_LIST } from '@/common/components/AppraisalFormModal/constant';

interface Props {}

const Calc: React.FC<Props> = () => {
  return (
    <>
      <Form.Item
        label="费用项"
        name="itemCode"
        rules={[{ required: true, message: '请选择费用项' }]}
      >
        <Select
          mode='multiple'
          placeholder="请选择"
          options={COST_LIST}
        />
      </Form.Item>
      <Form.Item
        label="下属关系"
        name="staffRelationType"
        rules={[{ required: true, message: '请选择下属关系' }]}
      >
        <Select
          placeholder="请选择"
          options={[
            { label: '本人', value: 'SELF' },
            { label: 'NC组织框架', value: 'NC' },
            { label: '维表维护框架', value: 'MANUAL_VIRTUAL_RELATION' },
            { label: 'NC组织架构过滤新人', value: 'NC_FILTER_NEWER' },
          ]}
        />
      </Form.Item>
      <Form.Item
        label="计算逻辑"
        name="indicatorAggType"
        rules={[{ required: true, message: '请选择计算逻辑' }]}
      >
        <Select
          placeholder="请选择"
          options={[
            { label: '累计值', value: 'SUM' },
            { label: '平均值', value: 'AVG' },
          ]}
        />
      </Form.Item>
      <Form.Item
        label="追溯周期"
        name="traceCycle"
        rules={[{ required: true, message: '请选择追溯周期' }]}
      >
        <Select
          placeholder="请选择"
          options={[
            { label: '0', value: 0 },
            { label: '1', value: 1 },
            { label: '2', value: 2 },
            { label: '3', value: 3 },
            { label: '4', value: 4 },
            { label: '5', value: 5 },
            { label: '6', value: 6 },
          ]}
        />
      </Form.Item>
    </>
  );
};

export default Calc;
