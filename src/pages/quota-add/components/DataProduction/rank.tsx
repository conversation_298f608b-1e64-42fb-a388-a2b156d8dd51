import React, { useMemo } from 'react';
import { Form, Select } from 'antd';
import {
  groupIndicatorAggTypeOptions,
  groupRuleDimOptions,
  groupValueTypeOptions,
  sortTypeOptions,
  GROUP_TARGET_TYPE_OPTIONS,
} from '@/pages/quota-add/const';

interface Props {
  ruleKind: string;
  currentBusinessInfoSimpleData: any[];
}

const Rank: React.FC<Props> = ({
  ruleKind,
  currentBusinessInfoSimpleData,
}) => {
  const options = useMemo(() => currentBusinessInfoSimpleData?.map(o => ({ label: o.value, value: o.key })), [
    currentBusinessInfoSimpleData.length,
  ]);


  return (
    <>
      <Form.Item
        label="用于排名的指标"
        name="tableColumnGroupIndicator"
        rules={[{ required: true, message: '请选择' }]}
      >
        <Select
          showSearch
          allowClear
          optionFilterProp="label"
          options={options}
          placeholder="请选择"
        />
      </Form.Item>
      <Form.Item
        label="用于排名的指标-计算逻辑"
        name="groupIndicatorAggType"
        rules={[
          {
            required: true, message: '请选择',
          },
        ]}
      >
        <Select
          options={groupIndicatorAggTypeOptions}
          placeholder="请选择"
        />
      </Form.Item>
      <Form.Item
        label="用于排名的指标-排序方式"
        name="sortType"
        rules={[
          {
            required: true, message: '请选择',
          },
        ]}
      >
        <Select
          options={sortTypeOptions}
          placeholder="请选择"
        />
      </Form.Item>
      <Form.Item
        label="排名人员范围"
        name="groupRuleDim"
        rules={[
          {
            required: true,
            message: '请选择',
          },
        ]}
      >
        <Select
          options={groupRuleDimOptions}
          placeholder="请选择"
        />
      </Form.Item>
      <Form.Item
        label="排名人员范围-范围指定人员类型"
        name="groupTargetType"
        rules={[
          {
            required: true,
            message: '请选择',
          },
        ]}
      >
        <Select
          options={GROUP_TARGET_TYPE_OPTIONS}
          placeholder="请选择"
        />
      </Form.Item>
      <Form.Item
        label="排名结果输出"
        name="groupValueType"
        rules={[
          {
            required: true,
            message: '请选择',
          },
        ]}
      >
        <Select
          options={groupValueTypeOptions}
          placeholder="请选择"
        />
      </Form.Item>
    </>
  );
};

// eslint-disable-next-line no-restricted-syntax
export default Rank;
