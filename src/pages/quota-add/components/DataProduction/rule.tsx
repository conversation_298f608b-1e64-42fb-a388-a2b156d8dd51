import React, { useMemo } from 'react';
import { Form, Radio } from 'antd';
import { ruleTypeEnumMap } from '@/common/Types';
import { regularCheck } from '@/common/utils';
import { Formula } from '@/common/components/formula';
import { defaultParseDataToForm, defaultParseFormToData } from '../formula/helper';

interface Props {
  currentBusinessInfoSimpleData?: any[];
  ruleKind?: string;
}

interface IRuleType {
  key: string;
  value: string;
  text: string;
  children?: Array<{
    key?: string;
    value?: string;
    text?: string;
  }>;
}

const Rule: React.FC<Props> = ({ currentBusinessInfoSimpleData, ruleKind }) => {
  const getCurrRuleKind = (v): IRuleType => ruleTypeEnumMap.find((o) => o.key === v);

  /** 公式组件 */
  /**
   * 选项
   */
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const options = useMemo(
    () =>
      currentBusinessInfoSimpleData.map((o) => ({
        label: o.value,
        value: o.key,
        timeType: o.timeType,
        type: 'Indicator',
      })),
    [currentBusinessInfoSimpleData.length],
  );

  return (
    <>
      <Form.Item
        label="规则类型"
        shouldUpdate={(pre, cur) =>
          pre?.indicatorType?.toString() !== cur?.indicatorType?.toString()
        }
        hidden={getCurrRuleKind(ruleKind).children?.length <= 0}
      >
        {() => (
          <Form.Item name="ruleGroupType" className="u-mb-0">
            <Radio.Group>
              {getCurrRuleKind(ruleKind)?.children.map((item) => (
                <Radio value={item.value} key={item.key}>
                  {item.text}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        )}
      </Form.Item>
      <Form.Item
        label="公式"
        name={['expressionObj', 'expression']}
        rules={[
          {
            required: true,
            message: '公式必填',
            validator: (_, value) =>
              value && value.expression !== ''
                ? Promise.resolve()
                : Promise.reject(new Error('公式必填')),
          },
          {
            validator: (_, value) => regularCheck(options, value),
          },
        ]}
      >
        <Formula
          // tracertInfo={tracertInfo}
          buildDataToForm={defaultParseDataToForm}
          parseFormToData={defaultParseFormToData}
          name="公式/公式"
          placeholder="输入「@」后选择指标"
          options={options}
          onChange={(value) => {}}
          calendarMode={null}
        />
      </Form.Item>

      <Form.Item
        label="上限"
        name={['expressionObj', 'upperLimit']}
        rules={[
          {
            validator: (_, value) => regularCheck(options, value),
          },
        ]}
      >
        <Formula
          name="公式/上限"
          buildDataToForm={defaultParseDataToForm}
          parseFormToData={defaultParseFormToData}
          // tracertInfo={tracertInfo}
          placeholder="输入「@」后选择指标"
          options={options}
          calendarMode={null}
        />
      </Form.Item>

      <Form.Item
        label="下限"
        name={['expressionObj', 'lowLimit']}
        rules={[
          {
            validator: (_, value) => regularCheck(options, value),
          },
        ]}
      >
        <Formula
          name="公式/下限"
          buildDataToForm={defaultParseDataToForm}
          parseFormToData={defaultParseFormToData}
          // tracertInfo={tracertInfo}
          placeholder="输入「@」后选择指标"
          options={options}
          calendarMode={null}
        />
      </Form.Item>
    </>
  );
};

export default Rule;
