import React from 'react';
import { Form, Radio, Select } from 'antd';
import { ruleTypeEnumMap } from '@/common/Types';
import store from '../../store';

import Rule from './rule';
import Odps from './odps';
import Rank from './rank';
import Calc from './calc';
import Lake from './lake';

interface Props {
  type?: string;
  form?: any;
  currentBusinessInfoSimpleData?: any[];
  tableDimColumn?: any[];
  tableExtraColumn?: any[];
}

const DataProduction: React.FC<Props> = ({
  type,
  form,
  currentBusinessInfoSimpleData,
  tableDimColumn,
  tableExtraColumn,
}) => {
  const indexStore = store.useStore();

  const [productionDataCache, setProductionDataCache] = React.useState<{[key: string]: any}>({});

  // const indicatorTypeWatch = Form.useWatch('indicatorType', form);
  const getProductionSpecificFields = (indicatorType: string) => {
    const allValues = form.getFieldsValue();

    switch (indicatorType) {
      case 'HOLOGRES':
        return {
          expression: allValues.expression,
        };
      case 'LAKE_INDICATOR_CODE':
        return {
          lakeIndicatorCode: allValues.lakeIndicatorCode,
          sqlTemplateCode: allValues.sqlTemplateCode,
        };
      case 'ODPS':
        return {
          tableExtraColumn: allValues.tableExtraColumn,
        };
      case 'GROUP_INDICATOR':
        return {
          groupIndicatorAggType: allValues.groupIndicatorAggType,
          sortType: allValues.sortType,
          groupRuleDim: allValues.groupRuleDim,
          groupTargetType: allValues.groupTargetType,
          groupValueType: allValues.groupValueType,
          tableColumnGroupIndicator: allValues.tableColumnGroupIndicator,
        };
      case 'CAL_ITEM_INDICATOR':
        return {
          itemCode: allValues.itemCode,
          staffRelationType: allValues.staffRelationType,
          indicatorAggType: allValues.indicatorAggType,
          traceCycle: allValues.traceCycle,
        };
      case 'COMPLEX_INDICATOR':
        return {
          ruleGroupType: allValues.ruleGroupType,
          expressionObj: allValues.expressionObj,
        };
      default:
        return {};
    }
  };

  /** 生产方式选择 */
  const onRuleKindChange = (v: any) => {
    const newIndicatorType = v?.target?.value;
    const currentIndicatorType = form.getFieldValue('indicatorType');

    if (!currentIndicatorType || currentIndicatorType === newIndicatorType) {
      return;
    }

    // 保存当前生产方式的特有数据
    const currentSpecificData = getProductionSpecificFields(currentIndicatorType);
    setProductionDataCache(prev => ({
      ...prev,
      [currentIndicatorType]: currentSpecificData
    }));

    const isHoloLakeSwitch = (
      (currentIndicatorType === 'HOLOGRES' && newIndicatorType === 'LAKE_INDICATOR_CODE') ||
      (currentIndicatorType === 'LAKE_INDICATOR_CODE' && newIndicatorType === 'HOLOGRES')
    );

    if (isHoloLakeSwitch) {
      const targetSpecificData = productionDataCache[newIndicatorType] || {};

      const currentSpecificFields = Object.keys(currentSpecificData);
      const clearCurrentFields = currentSpecificFields.reduce((acc, field) => {
        acc[field] = undefined;
        return acc;
      }, {} as any);

      form.setFieldsValue({
        ...clearCurrentFields,
        ...targetSpecificData,
      });
    } else {
      form.setFieldsValue({
        ruleGroupType: undefined, // 规则类型
        datasourceId: undefined, // 数据源
        tableColumn: undefined, // 指标值字段
        // odps独有字段
        tableDimColumn: undefined,
        tableExtraColumn: undefined,
        groupIndicatorAggType: undefined,
        sortType: undefined,
        groupRuleDim: undefined,
        groupTargetType: 'ALL_DEPT_STAFF',
        groupValueType: undefined,
        tableColumnGroupIndicator: undefined,
        // 清空所有生产方式特有字段
        expression: undefined,
        lakeIndicatorCode: undefined,
        sqlTemplateCode: undefined,
        itemCode: undefined,
        staffRelationType: undefined,
        indicatorAggType: undefined,
        traceCycle: undefined,
        expressionObj: undefined,
      });

      const targetSpecificData = productionDataCache[newIndicatorType] || {};
      form.setFieldsValue(targetSpecificData);
    }

    if (newIndicatorType === 'COMPLEX_INDICATOR') {
      form.setFieldsValue({
        ruleGroupType: 'EXPRESSION', // 规则类型
      });
    }

    if (!isHoloLakeSwitch) {
      form.setFieldsValue({
        indicatorValueType: 'NUMBER',
      });
    }
  };

  return (
    <>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.businessGroup !== currentValues.businessGroup
        }
      >
        {({ getFieldValue }) => {
          const businessGroup = getFieldValue('businessGroup');

          // 获取允许的生产方式
          const allowedTypes = indexStore.getAllowedProductionTypes(type || '');

          let ruleTypes = ruleTypeEnumMap.filter((type) => {
            if (businessGroup === 'C33_DIRECT') {
              // 业务域是C33直营才展示计算项
              return true;
            } else {
              // 其他业务域不展示计算项
              return type.key !== 'CAL_ITEM_INDICATOR';
            }
          });


          return (
            <Form.Item name="indicatorType" label="生产方式">
              <Radio.Group onChange={onRuleKindChange}>
                {ruleTypes.map((item) => {
                  const isDisabled = allowedTypes && !allowedTypes.includes(item.value);
                  return (
                    <Radio value={item.value} key={item.key} disabled={isDisabled}>
                      {item.text}
                    </Radio>
                  );
                })}
              </Radio.Group>
            </Form.Item>
          );
        }}
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.indicatorType !== currentValues.indicatorType
        }
      >
        {({ getFieldValue }) =>
          getFieldValue('indicatorType') === 'COMPLEX_INDICATOR' ? (
            <Rule
              ruleKind={getFieldValue('indicatorType')}
              currentBusinessInfoSimpleData={currentBusinessInfoSimpleData}
            />
          ) : null
        }
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.indicatorType !== currentValues.indicatorType
        }
      >
        {({ getFieldValue }) =>
          getFieldValue('indicatorType') === 'ODPS' ||
          getFieldValue('indicatorType') === 'HOLOGRES' ||
          getFieldValue('indicatorType') === 'LAKE_INDICATOR_CODE' ? (
            <Odps
              ruleKind={getFieldValue('indicatorType')}
              datasourceId={getFieldValue('datasourceId')}
              tableDimColumn={tableDimColumn}
              tableExtraColumn={tableExtraColumn}
            />
          ) : null
        }
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.indicatorType !== currentValues.indicatorType
        }
      >
        {({ getFieldValue }) =>
          getFieldValue('indicatorType') === 'GROUP_INDICATOR' ? (
            <Rank
              ruleKind={getFieldValue('indicatorType')}
              currentBusinessInfoSimpleData={currentBusinessInfoSimpleData || []}
            />
          ) : null
        }
      </Form.Item>
      {/* 业务域是C33直营才展示计算项 */}
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.indicatorType !== currentValues.indicatorType
        }
      >
        {({ getFieldValue }) =>
          getFieldValue('businessGroup') === 'C33_DIRECT' &&
          getFieldValue('indicatorType') === 'CAL_ITEM_INDICATOR' ? (
            <Calc />
          ) : null
        }
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.indicatorType !== currentValues.indicatorType
        }
      >
        {({ getFieldValue }) =>
          getFieldValue('indicatorType') === "LAKE_INDICATOR_CODE" ? (
            <Lake />
          ) : null
        }
      </Form.Item>
      <Form.Item
        className="u-mb-0"
        label="字段类型"
        name="indicatorValueType"
        rules={[{ required: true, message: '请选择配置类型' }]}
      >
        <Select
          placeholder="请选择"
          disabled={!indexStore.isIndicatorValueTypeEditable(type || '')}
        >
          {/* {
            indicatorTypeWatch === 'GROUP_INDICATOR' ?
              <Select.Option value="PERCENTAGE" key="PERCENTAGE">百分比</Select.Option>
              : null
          } */}
          <Select.Option value="NUMBER" key="NUMBER">
            数值
          </Select.Option>
          <Select.Option value="STRING" key="STRING">
            文本
          </Select.Option>
        </Select>
      </Form.Item>
    </>
  );
};

export default DataProduction;
