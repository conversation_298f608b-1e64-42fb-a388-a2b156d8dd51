import React from 'react';
import { Form, Input } from 'antd';

interface Props {}

const Calc: React.FC<Props> = () => {
  return (
    <>
      <Form.Item
        label="关联指标code"
        name="lakeIndicatorCode"
        rules={[{ required: true, message: '请输入关联指标code' }]}
      >
        <Input placeholder="请输入" maxLength={100} />
      </Form.Item>
      <Form.Item
        label="指标模板code"
        name="sqlTemplateCode"
        rules={[{ required: true, message: '请输入指标模板code' }]}
      >
        <Input placeholder="请输入" maxLength={100} />
      </Form.Item>
    </>
  );
};

export default Calc;
