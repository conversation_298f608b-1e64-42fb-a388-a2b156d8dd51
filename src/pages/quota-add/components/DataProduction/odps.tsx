import React, { useMemo, useState } from 'react';
import { uniqBy } from 'lodash';
import {
  Form,
  // Input,
  Select,
} from 'antd';
import { useRequest, useAsyncEffect } from 'ahooks';
import MonacoEditor from 'react-monaco-editor';

import IndicatorDatasourceGatewayService from '@/_docplus/target/service/gateway/IndicatorDatasourceGatewayService';
import SelectTagOrSingle from '../SelectTagOrSingle';
import SelectMenuAdd from '../SelectMenuAdd';

interface Props {
  ruleKind?: string;
  datasourceId?: string;
  tableDimColumn?: any[];
  tableExtraColumn?: any[];
}

// const { TextArea } = Input;

const Odps: React.FC<Props> = ({
  ruleKind,
  datasourceId: $datasourceId,
  tableDimColumn,
  tableExtraColumn,
}) => {
  // 枚举源数据
  const [dataSource, setDataSource] = useState([]);
  const [datasourceId, setDatasourceId] = useState($datasourceId || '');
  // 获取枚举值
  const { runAsync, loading } = useRequest(IndicatorDatasourceGatewayService.searchIndicatorDatasourceByCondition, {
    manual: true,
  });
  useAsyncEffect(async () => {
    try {
      const { data } = await runAsync({
        datasourceType: ruleKind,
        pageNum: 1,
        pageSize: 100,
      });
      setDataSource(data?.datasourceDTOList);
    } catch (error) {
      setDataSource([]);
      console.log(error);
    }
  }, [ruleKind]);

  const getOptions1 = useMemo(() => dataSource?.map(o => ({ label: o.name, value: o.datasourceId })), [dataSource]);

  const getOptions2 = useMemo(() => {
    const lineData = dataSource?.filter((i) => i.datasourceId === datasourceId)?.[0]?.columnConfig?.dataColumns;
    return lineData?.map(o => ({ label: o.name, value: o.field }));
  }, [dataSource, datasourceId]);

  const getOptions3 = useMemo(() => {
    const lineData = dataSource?.filter((i) => i.datasourceId === datasourceId)?.[0]?.columnConfig?.dataColumns || [];
    const newLineData = uniqBy([...lineData, ...tableDimColumn], 'field');
    return newLineData?.map(o => ({ label: o.name, value: `${o.name}/${o.field}` }));
  }, [dataSource, datasourceId, tableDimColumn]);

  const getOptions4 = useMemo(() => {
    const lineData = dataSource?.filter((i) => i.datasourceId === datasourceId)?.[0]?.columnConfig?.dataColumns || [];
    const newLineData = uniqBy([...lineData, ...tableExtraColumn], 'field');
    return newLineData?.map(o => ({ label: o.name, value: `${o.name}/${o.field}` }));
  }, [dataSource, datasourceId, tableExtraColumn]);

  return (
    <>
      <Form.Item
        label="数据源"
        name="datasourceId"
        rules={[{ required: true, message: '请选择数据源' }]}
      >
        <Select
          loading={loading}
          placeholder="请选择"
          options={getOptions1}
          onChange={(v) => setDatasourceId(v)}
        />
      </Form.Item>
      <Form.Item
        label="指标值字段"
        name="tableColumn"
        rules={[{ required: true, message: '请输入指标值字段' }]}
      >
        <SelectTagOrSingle options={getOptions2} isSingle />
      </Form.Item>
      {
        ['HOLOGRES', 'ODPS', 'LAKE_INDICATOR_CODE'].includes(ruleKind)&&
        <Form.Item
          label="维度数据字段"
          name="tableDimColumn"
        >
          <SelectMenuAdd options={getOptions3} />
        </Form.Item>
      }
      {
        ruleKind === 'ODPS' ?
          <>
            <Form.Item
              label="辅助数据字段"
              name="tableExtraColumn"
            >
              <SelectMenuAdd options={getOptions4} />
            </Form.Item>
          </> : null
      }
      {
        ruleKind === 'HOLOGRES' ?
          <Form.Item
            label="SQL"
            name="expression"
            className="no-pre-event"
            rules={[{ required: true, message: '请输入SQL' }]}
          >
            <MonacoEditor
              height="300"
              language="javascript"
              theme="vs-dark"
              options={{
                selectOnLineNumbers: true,
              }}
            />
          </Form.Item> : null
      }
    </>
  );
};

export default Odps;
