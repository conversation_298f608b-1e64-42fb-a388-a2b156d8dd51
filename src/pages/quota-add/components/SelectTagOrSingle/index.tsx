import React, { useEffect, useState } from 'react';
import type { SelectProps } from 'antd';
import {
  Select,
} from 'antd';

interface Props {
  options?: SelectProps['options'];
  value?: string[];
  onChange?: (e: any) => void;
  isSingle?: boolean;
}

/**
 * select tags 单选、多选组件
 * @param param0
 * @returns
 */

const SelectTagOrSingle: React.FC<Props> = ({
  options,
  value: $value,
  onChange,
  isSingle = false,
}) => {
  const [value, setValue] = useState($value);

  useEffect(() => {
    setValue($value);
  }, [$value]);

  const onHandleChange = (v) => {
    const newV = isSingle && v.length > 1 ? v.slice(v.length - 1) : v;
    setValue(newV);
    onChange && onChange(newV);
  };

  return (
    <Select
      mode="tags"
      // style={{ width: '100%' }}
      value={value}
      placeholder="请选择"
      onChange={onHandleChange}
      options={options}
    />
  );
};

export default SelectTagOrSingle;
