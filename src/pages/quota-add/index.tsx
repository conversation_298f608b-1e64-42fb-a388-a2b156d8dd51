import React from 'react';
import PageForm from './components';
import './index.less';
import store from './store';

interface HistoryProp {
  match?: {
    params?: {
      source?: string;
      shopId?: string;
    };
  };
}

const Index: React.FC<HistoryProp> = ({
  match,
}) => {
  console.log('%c 🥚 match: ', 'font-size:20px;background-color: #FFDD4D;color:#fff;', match);
  return (<div className="container"><PageForm /></div>);
};

// export default <store.Container component={Index} />

export default () => (
  <store.Container
    component={Index}
  />
);
