import { createStore } from '@alife/kobe-store-next';
import {
  parseRuleGroupFormToData,
  parseDataToRuleGroupForm,
} from '@/common/components/rule-group/quota-helper';
import IndicatorGatewayService from '@/_docplus/target/service/gateway/IndicatorGatewayService';
import { QueryIndicatorRequest } from '@/_docplus/target/types/gateway';
import moment from 'moment';

import { getUrlParam } from '@/common/utils/index';
import { sourceTypeEum } from './const';

export default createStore({
  store: {
    // 公式数据
    currentBusinessInfoSimpleData: [],
    // tableDimColumn可能自定义数据
    tableDimColumn: [],
    tableExtraColumn: [],
    originalIndicatorData: null,
    defaultFormData: {
      businessGroup: 'C33_DIRECT',
      businessLine: 'C33_DEFAULT',
      timeType: 'MONTH',
      indicatorId: '', // 编辑状态下的指标id
      ruleGroupType: 'EXPRESSION', // 规则类型
      name: '',
      description: '',
      indicatorCode: '',
      indicatorType: 'COMPLEX_INDICATOR', // 生产方式
      indicatorValueType: 'NUMBER', // 配置类型
      status: false,
      expressionObj: {},
      datasourceId: '', // 数据源
      tableColumn: undefined, // 指标值字段
      expression: '', // sql
      // odps独有字段
      tableDimColumn: [],
      tableExtraColumn: [],
      // 取值/计算方式
      configType: undefined,
      groupIndicatorAggType: undefined,
      sortType: undefined,
      groupRuleDim: undefined,
      groupTargetType: 'ALL_DEPT_STAFF',
      groupValueType: undefined,
      tableColumnGroupIndicator: undefined,
      itemCode: undefined,
      staffRelationType: undefined,
      indicatorAggType: undefined,
      traceCycle: undefined,
      lakeIndicatorCode: undefined,
      sqlTemplateCode: undefined,
      versionList: [],
      ruleVersion: '',
    },
    // url链接数据
    urlParam: {
      indicatorId: '',
      type: '',
    },
  },

  initDefaultForm() {
    const r = {
      businessGroup: 'C33_DIRECT',
      businessLine: 'C33_DEFAULT',
      timeType: 'MONTH',
      indicatorId: '', // 编辑状态下的指标id
      ruleGroupType: 'EXPRESSION', // 规则类型
      name: '',
      description: '',
      indicatorCode: '',
      indicatorType: 'COMPLEX_INDICATOR', // 生产方式
      indicatorValueType: 'NUMBER', // 配置类型
      status: false,
      expressionObj: {},
      datasourceId: '', // 数据源
      tableColumn: undefined, // 指标值字段
      expression: '', // sql
      // odps独有字段
      tableDimColumn: [],
      tableExtraColumn: [],
      // 取值/计算方式
      configType: undefined,
      groupIndicatorAggType: undefined,
      sortType: undefined,
      groupRuleDim: undefined,
      groupTargetType: 'ALL_DEPT_STAFF',
      groupValueType: undefined,
      tableColumnGroupIndicator: undefined,
      itemCode: undefined,
      staffRelationType: undefined,
      indicatorAggType: undefined,
      traceCycle: undefined,
      lakeIndicatorCode: undefined,
      sqlTemplateCode: undefined,
      versionList: [],
      ruleVersion: '',
    };
    console.log('初始化表单数据-->', r);

    this.setStore({
      defaultFormData: r,
    });
    return r;
  },

  /** 获取公式选择数据 */
  async getCurrentIndicatorByCondition(options: QueryIndicatorRequest) {
    const d = await IndicatorGatewayService.queryIndicatorsByCondition({
      ...options,
    }).then((res) => res.data?.indicatorDTOList || []);
    const arr = d
      .filter((v) => v.indicatorType !== 'CAL_ITEM_INDICATOR') // 复用指标不能选计算项指标
      .map((v) => ({ key: v.indicatorId, value: v.name, timeType: v.timeType }));

    this.setStore({
      currentBusinessInfoSimpleData: arr,
    });
  },



  // 版本切换方法
  switchToVersion(version: string, form?: any) {
    const { originalIndicatorData, defaultFormData } = this.store;
    if (!originalIndicatorData || !version || !form) return;
    
    const formData = parseDataToRuleGroupForm(originalIndicatorData, version);
    
    const updateData = {
      ...defaultFormData,
      businessGroup: originalIndicatorData.businessGroup,
      businessLine: originalIndicatorData.businessLine,
      name: originalIndicatorData.name,
      description: originalIndicatorData.description,
      indicatorCode: originalIndicatorData.indicatorCode,
      indicatorId: originalIndicatorData.indicatorId,
      ...(formData || {}),
      ruleVersion: moment(version.replace('-', ''), 'YYYYMM'),
    };
    
    form.setFieldsValue(updateData);
  },

  // 解析-组装 form数据
  async pareForm(values) {
    console.log('pareForm-->', values);
    const {
      timeType, // 指标周期
      businessGroup,
      businessLine,
      ruleGroupType, // 规则类型
      name,
      description,
      indicatorCode, // code
      indicatorType, // 生产方式
      tableColumn, // 指标值字段
      tableColumnGroupIndicator,
      datasourceId, // 数据源
      indicatorValueType, // 字段类型
      // expressionObj, // 公式组件
      // 取值/计算方式
      configType,
    } = values;
    const pageData = parseRuleGroupFormToData(values);

    let indicatorTypeV = indicatorType === 'COMPLEX_INDICATOR' ? 'COMPLEX_INDICATOR' : 'ATOMIC_DIC';
    let tableColumnV = tableColumn?.[0] || '';
    let classificationLabel;

    if (indicatorType === 'GROUP_INDICATOR') {
      indicatorTypeV = 'GROUP_INDICATOR';
      classificationLabel = 'RANK';
      tableColumnV = tableColumnGroupIndicator || '';
    }
    if (indicatorType === 'CAL_ITEM_INDICATOR') {
      indicatorTypeV = 'CAL_ITEM_INDICATOR';
    }
    return {
      businessGroup,
      businessLine,
      timeType,
      ruleGroupType, // 规则类型
      name,
      description,
      indicatorCode,
      indicatorType: indicatorTypeV, // 生产方式
      classificationLabel, // 排名专用指标
      indicatorValueType,
      sourceType: sourceTypeEum[indicatorType],
      datasourceId,
      tableColumn: tableColumnV,
      status: 'PUBLISH',
      productDomain: 'COMMISSION',
      indicatorRules: pageData?.indicatorRules,
      // odps独有字段
      tableDimColumn: pageData?.tableDimColumn || [],
      tableExtraColumn: pageData?.tableExtraColumn || [],
      configType,
    };
  },

  // 获取指标详情
  async getCurrentIndicatorDetail(indicatorId: any) {
    try {
      const d = await IndicatorGatewayService.queryIndicatorsByCondition({
        includeIndicatorRule: true,
        includeIndicatorDatasource: true,
        indicatorIds: [indicatorId],
      });
      this.initDefaultForm();

      if (d?.success) {
        const resultData = d?.data?.indicatorDTOList?.[0];
        const formData = parseDataToRuleGroupForm(resultData);

        const defaultData = {
          indicatorId: resultData.indicatorId, // 编辑状态下的指标id
          // 基础信息
          businessGroup: resultData?.businessGroup || 'C33_DIRECT',
          businessLine: resultData?.businessLine || 'C33_DEFAULT',
          timeType: 'MONTH',
          name: resultData?.name,
          description: resultData?.description,
          indicatorCode: resultData?.indicatorCode,
          // 数据生产
          indicatorType: formData?.indicatorType, // 生产方式
          ruleGroupType:
            resultData?.indicatorType === 'COMPLEX_INDICATOR'
              ? formData?.ruleGroupType || resultData?.indicatorRules?.[0]?.ruleCondition?.qlRuleCondition?.ruleGroupType
              : undefined, // 规则类型
          indicatorValueType: resultData?.indicatorValueType,
          // upperLimit: resultData?.upperLimit, // 上限
          // lowLimit: resultData?.lowLimit, // 下限
          expressionObj: formData?.expressionObj, // 公式
          datasourceId: resultData?.datasourceId, // 数据源
          tableColumn: resultData?.tableColumn ? [resultData?.tableColumn] : undefined, // 指标值字段
          expression: formData?.expression || resultData?.indicatorRules?.find(
            (item) => item.ruleCondition?.ruleType === 'SQL_EXPRESSION',
          )?.ruleCondition?.sqlRuleCondition?.expression, // sql
          // odps独有字段
          tableDimColumn: formData?.tableDimColumn,
          tableExtraColumn: formData?.tableExtraColumn,
          // 取值/计算方式
          configType: resultData?.configType || undefined,
          groupIndicatorAggType: formData?.groupIndicatorAggType || undefined,
          sortType: formData?.sortType || undefined,
          groupRuleDim: formData?.groupRuleDim || undefined,
          groupTargetType: formData?.groupTargetType || 'ALL_DEPT_STAFF',
          groupValueType: formData?.groupValueType || undefined,
          tableColumnGroupIndicator: resultData?.tableColumn || undefined,
          itemCode:
            (resultData?.indicatorRules?.[0]?.ruleCondition as any)?.calItemRuleCondition?.itemCode?.split(',') ||
            undefined,
          staffRelationType:
            (resultData?.indicatorRules?.[0]?.ruleCondition as any)?.calItemRuleCondition
              ?.staffRelationType || undefined,
          indicatorAggType:
            (resultData?.indicatorRules?.[0]?.ruleCondition as any)?.calItemRuleCondition
              ?.indicatorAggType || undefined,
          traceCycle:
            (resultData?.indicatorRules?.[0]?.ruleCondition as any)?.calItemRuleCondition?.traceCycle,
            
          lakeIndicatorCode: (resultData?.indicatorRules?.[0]?.ruleCondition as any)?.lakeRuleCondition?.lakeIndicatorCode,
          sqlTemplateCode: (resultData?.indicatorRules?.[0]?.ruleCondition as any)?.lakeRuleCondition?.sqlTemplateCode,
          versionList: resultData?.indicatorRules?.map(rule => {
            const version = (rule?.ruleCondition as any)?.ruleVersion;
            return version && version.length === 6 ? `${version.slice(0, 4)}-${version.slice(4, 6)}` : version;
          }).filter(Boolean) || [],
          ruleVersion: (() => {
            const version = (resultData?.indicatorRules?.[0]?.ruleCondition as any)?.ruleVersion || '';
            return version && version.length === 6 ? moment(version, 'YYYYMM') : version;
          })(),
        };

        this.setStore({
          defaultFormData: defaultData as any,
          tableDimColumn: resultData?.tableDimColumn || [],
          tableExtraColumn: resultData?.tableExtraColumn || [],
          originalIndicatorData: resultData, // 版本切换
        });
      }
    } catch (error) {
      console.log(error);
    }
  },

  async setup() {
    const urlParam: any = getUrlParam();
    this.setStore({
      defaultFormData: this.initDefaultForm(),
      urlParam,
    });
  },

  hasVersionHistory() {
    const { originalIndicatorData } = this.store;
    return originalIndicatorData && originalIndicatorData.indicatorRules && originalIndicatorData.indicatorRules.length > 0;
  },

  getFirstVersionInfo() {
    const { originalIndicatorData } = this.store;
    if (!originalIndicatorData || !originalIndicatorData.indicatorRules || originalIndicatorData.indicatorRules.length === 0) {
      return null;
    }

    const firstRule = originalIndicatorData.indicatorRules[0];
    const ruleCondition = firstRule.ruleCondition;

    let indicatorType = '';
    let ruleType = ruleCondition?.ruleType || '';

    if (ruleType === 'SQL_EXPRESSION') {
      indicatorType = 'HOLOGRES';
    } else if (ruleType === 'LAKE_INDICATOR_CODE') {
      indicatorType = 'LAKE_INDICATOR_CODE';
    } else if (ruleType === 'ODPS_IMPORT') {
      indicatorType = 'ODPS';
    } else if (ruleType === 'QL_EXPRESSION') {
      indicatorType = 'COMPLEX_INDICATOR';
    } else if (ruleType === 'GROUP_RULE') {
      indicatorType = 'GROUP_INDICATOR';
    } else if (ruleType === 'CAL_ITEM_CODE') {
      indicatorType = 'CAL_ITEM_INDICATOR';
    } else {
      const sourceType = originalIndicatorData.sourceType;
      const originalIndicatorType = originalIndicatorData.indicatorType;

      if (originalIndicatorType === 'ATOMIC_DIC' && sourceType === 'lake') {
        indicatorType = 'LAKE_INDICATOR_CODE';
        ruleType = 'LAKE_INDICATOR_CODE';
      } else if (originalIndicatorType === 'ATOMIC_DIC' && sourceType === 'dic') {
        indicatorType = 'ODPS';
        ruleType = 'ODPS_IMPORT';
      } else if (originalIndicatorType === 'ATOMIC_DIC' && sourceType === 'holo') {
        indicatorType = 'HOLOGRES';
        ruleType = 'SQL_EXPRESSION';
      } else {
        indicatorType = originalIndicatorType;
      }
    }
   console.log(originalIndicatorData.configType,'originalIndicatorData.configType');
   
    return {
      indicatorType,
      ruleType,
      indicatorValueType: originalIndicatorData.indicatorValueType,
      sourceType: originalIndicatorData.sourceType,
      configType: originalIndicatorData.configType
    };
  },
 
  getAllowedProductionTypes(editType: string) {
    if (editType === 'add' || editType === '') {
      return null;
    }

    if (!this.hasVersionHistory()) {
      return null; 
    }

    const firstVersionInfo = this.getFirstVersionInfo();
    if (!firstVersionInfo) return null;

    const { ruleType, indicatorType } = firstVersionInfo;

    return [indicatorType];
  },

  isIndicatorValueTypeEditable(editType: string) {
    if (editType === 'add' || editType === '') {
      return true;
    }
    return !this.hasVersionHistory();
  },


});
