export const sourceTypeEum = {
  COMPLEX_INDICATOR: '',
  HOLOGRES: 'holo',
  ODPS: 'dic',
  LAKE_INDICATOR_CODE: "lake"
};

// 取值/计算方式枚举
export const configTypeOptions = [
  {
    label: '该人每月最新分区值相加',
    value: 'SUM',
  },
  {
    label: '该人每月最新分区值相加 / 月数',
    value: 'AVG',
  },
  {
    label: '该人整个周期最新日分区值',
    value: 'LATEST',
  },
];

// 用于排名的指标-计算逻辑
export const groupIndicatorAggTypeOptions = [
  {
    label: '累计值',
    value: 'SUM',
  },
  {
    label: '平均值',
    value: 'AVG',
  },
  // {
  //   label: '最新值',
  //   value: 'LATEST',
  // },
];

// 用于排名的指标-排序方式
export const sortTypeOptions = [
  {
    label: '指标值从大到小',
    value: 'DESC',
  },
  {
    label: '指标值从小到大',
    value: 'ASC',
  },
];

// 排名人员范围
export const groupRuleDimOptions = [
  {
    label: '与待查询人 同行业',
    value: 'industry',
  },
  {
    label: '与待查询人 同行业+同岗位',
    value: 'industry&roleName',
  },
  {
    label: '与待查询人 同行业+同组织+同岗位',
    value: 'industry&department&roleName',
  },
];

// 排名人员范围-范围指定人员类型
export const GROUP_TARGET_TYPE_OPTIONS = [
  {
    label: '当前周期组织全员',
    value: 'ALL_DEPT_STAFF',
  },
  {
    label: '老员工',
    value: 'OLD_STAFF',
  },
  {
    label: '新员工',
    value: 'NEW_STAFF',
  },
];

// 排名结果输出
export const groupValueTypeOptions = [
  {
    label: '名次',
    value: 'RANK',
  },
  {
    label: '名次比例',
    value: 'RANK_PERCENTAGE',
  },
];
