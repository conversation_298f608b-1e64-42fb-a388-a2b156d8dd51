import React from 'react';
import { getInitParam } from '@/common/configStore';
import { CommonBlockWrap } from '@/common/components/CommonBlockWrap';
import store from './store';
import ModuleDetail from './components';

import './index.less';

const Index: React.FC = () => {
  return (
    <div className="detail-warp">
      <h3>业绩模块详情</h3>
      <CommonBlockWrap>
        <ModuleDetail />
      </CommonBlockWrap>
    </div>
  );
};

export default () => (
  <store.Container
    component={Index}
    initParam={() => ({
      ...getInitParam(),
    })}
  />
);
