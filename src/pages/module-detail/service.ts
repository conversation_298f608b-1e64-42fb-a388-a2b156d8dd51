import fetch from '@alife/amap-fetch';

/**
 * 获取明细指标类型列表
 * @param param 
 * @returns 
 */
export function getIndicatorTypeList(params) {
    return fetch({
        apiKey: 'kbcommprod.KpiPolicyQueryGatewayService.queryPolicyModifyConfig',
        params,
    });
}

/**
 * 获取模块详情
 * @param params
 * @returns
 */
export function queryByCode(params) {
  return fetch({
    params: {
      action: 'kbcommprod.CalItemQueryGatewayService.queryByCode',
      bizContent: {
        ...params,
      },
    },
  });
}

/**
 * 保存模块
 * @param params CalItemInfoDTO 类型，模块详细信息
 * @returns
 */
export function saveModule(params) {
  return fetch({
    params: {
      action: 'kbcommprod.CalItemManageGatewayService.save',
      bizContent: {
        ...params,
      },
    },
  });
}