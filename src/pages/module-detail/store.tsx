/* eslint-disable max-len */
import { createStore } from '@alife/kobe-store-next';
import JSCook<PERSON> from 'js-cookie';
import { message } from 'antd';
import { ActionTypeEnum, EXP_WIN_DEFAULT_CONFIG_INTERFACE, tracertInfo } from '@/common/Types';
import service from '@/_docplus/target/service/gateway/KpiPolicyQueryGatewayService';
import manageService from '@/_docplus/target/service/gateway/KpiPolicyManageGatewayService';
import { getIndicatorTypeList, queryByCode } from './service';
import {
  DEFAULT_INDICATOR_LIST_PAGE_SIZE,
  DETAIL_INDICATOR_TYPE_LIST_OPTIONS,
  AGENT_INDICATOR_TYPE_LIST_OPTIONS,
} from '@/common/constants';
import { generateRandomString } from '@/common/utils';
import { POLICY_TYPE_PARAMS } from '@/common/constants';

export default createStore({
  store: {
    editType: ActionTypeEnum.ADD,
    itemCode: '',

    dimension: [],
    selectProduct: 'KPI',
    business: ['ENCOURAGE', 'TO_HOME', 'BL', 'BATTALION'],

    calItemList: [],

    tracertInfo: {
      // 产品域
      domain: JSCookie.get('ASSESS_PLAN_PRODUCT_CODE'),
      // 业务域
      businessLine: null,
      // 其它所有信息
      othersInfo: {
        // 方案名称
        planName: null,
        // 方案状态
        planStatus: 'WAIT_SUBMIT',
        // 考核周期:
        planCycleStart: null,
        planCycleEnd: null,
      },
      // 操作类型（新建/复制新建/编辑）
      operateType: 'add',
      // 打开页面的时间戳
      pageStartTime: Date.parse(new Date() as any),
    } as tracertInfo,
  },

  // 查询指标
  async queryIndicators(businessGroup) {
    try {
      const res = await service.queryIndicatorList({
        businessGroup,
        pageSize: DEFAULT_INDICATOR_LIST_PAGE_SIZE,
        pageStart: 0,
      });

      // message.success({ content: '成功', key: 'loading' });
      const arr = res.data?.map((v: any) => ({
        label: v.indicatorName,
        value: v.indicatorId,
        type: 'Indicator',
        indicatorType: v.indicatorType,
        classificationLabel: v?.label,
      }));

      return {
        currentBusinessInfoSimpleData: arr || [],
      };
    } catch (error) {

    }
  },

  /** 获取唯一ID */
  async generateUniqueId(tip = true) {
    try {
      const d = await manageService.generateItemId();
      if (tip) {
        message.success({ content: '成功', key: 'loading' });
      }
      return d?.data || '';
    } catch (error) {
      // 如果接口挂了，随机生成一个 19 位的字符串
      return generateRandomString(19);
    }
  },

  // 查询模块详情
  async queryModuleDetail(itemCode ) {
    try {
      let d = await queryByCode({ itemCode });

      return d?.data?.data;
    } catch (error) {
      console.log('queryModuleDetail error', error);
      return {};
    }
  },

  // 设置当前tracertInfo的内容
  setTracertInfo(params: Partial<tracertInfo>) {
    this.setStore({
      tracertInfo: {
        ...this.store.tracertInfo,
        ...params,
      },
    });
  },

  /**
   * 切换业务域
   * @param businessGroup
   */
  async handleBusinessGroupChange(businessGroup) {
    this.queryIndicators(businessGroup).then((indicators) => {
      this.setStore({
        ...indicators,
      });
    });
  },

  /**
   * 启动
   */
  async setup() {
    const { type = ActionTypeEnum.SEE, itemCode = '' } = this.param || {};

    this.setStore({
      editType: type,
      itemCode,
      tracertInfo: {
        ...this.store.tracertInfo,
        operateType: type,
      },
    });

    if (type !== ActionTypeEnum.ADD && itemCode) {
      const detail = await this.queryModuleDetail(itemCode);

      console.log('detail', detail);
      console.log('calItemList', detail.calItemList);

      this.setStore({
        ...(detail as any),
      });
    }
  },
});
