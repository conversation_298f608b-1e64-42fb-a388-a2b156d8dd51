
/** 产品域枚举 */
export enum ProductDomainEnum {
  /** 绩效 */
  KPI = 'KPI',
  /** 北极星 */
  PROJECT = 'PROJECT',
  /** 项目战报 */
  PROJECT_WR = 'PROJECT_WR',
  /** 荣誉 */
  HONOUR = 'HONOUR',
  /** 激励赛 */
  ENCOURAGE = 'ENCOURAGE',
}

/** 锚点ID */
export enum DetailAnchorEnum {
  /** 基础信息 */
  BaseInfo = 'detail-add-base',
  /** 考核对象 */
  Object = 'detail-add-object',
  /** 考核结果 */
  Module = 'appraisal-results',
  /** 薪酬结果 */
  Result = 'detail-result',
  /** 其他配置 */
  Other = 'detail-add-other',
}
