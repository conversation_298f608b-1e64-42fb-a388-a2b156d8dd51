.detail-add-warp {
  padding-bottom: 60px;
  position: relative;

  .anchor-warp {
    position: absolute;
    right: 0;
    top: 90px;
    background: #FFF;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 3%);
  }

  .detail-add-base {
    .check-time {
      .ant-form-item-control-input-content {
        display: flex;

        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }

  .detail-done-warp {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 60px;
    left: 0;
    z-index: 9;
    background: #FFF;
    border-top: 1px solid #EEE;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 24px;

    .ant-form-item {
      margin-bottom: 0;

      .ant-col-offset-8 {
        margin-left: 0;
      }
    }
  }

  .detail-add-object {
    background: #FFF;

    .circleType {
      margin-left: 100px;
    }

    .result-preview {
      span {
        color: #1a66ff;
        cursor: pointer;
      }
    }
  }

  .detail-add-frame {
    background: #FFF;
    padding: 24px;
    width: 100%;
    margin-top: 24px;
  }

  .detail-add-show {
    background: #FFF;
    padding: 24px;
    width: 100%;
    margin-top: 24px;
  }

  .detail-add-other {
    background: #FFF;
    padding: 24px;
    width: 100%;
    margin-top: 24px;
  }

  .detail-add-excitation {
    background: #FFF;
    padding: 24px;
    width: 100%;
    margin-top: 24px;
  }

  .sub-assess-item {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: normal;

    span {
      margin-right: 8px;
    }
  }

  .without_select_modules {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    p {
      color: rgba(0, 0, 0, 50%);
    }

    .wrap_selectTypes {
      display: flex;
      margin: 24px 0;

      .single_item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 0 36px;
        cursor: pointer;

        img {
          width: 66px;
          height: 66px;
        }

        span {
          margin-top: 24px;
          font-weight: bold;

          .default {
            font-weight: normal;
            color: rgba(0, 0, 0, 50%);
          }
        }
      }
    }
  }
}

.ant-cascader-menus {
  .ant-cascader-menu:first-of-type {
    .ant-cascader-menu-item {
      .ant-cascader-checkbox {
        display: none;
      }
    }
  }
}

.sub_title {
  padding: 16px 0;
}

.pre-event {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.8;

  .anticon {
    pointer-events: all !important;
  }
}

.planDetailSpinWrapper {
  padding-top: 72px;
  text-align: center;
}

.version-picker-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 12px;

  .version-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    padding: 0 12px;
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    user-select: none;

    &:hover {
      background-color: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }

    &.version-item-selected {
      background-color: #1890ff;
      border-color: #1890ff;
      color: #ffffff;
      
      &:hover {
        background-color: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &.version-item-disabled {
      cursor: default;
      opacity: 0.6;
      
      &:hover {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        color: inherit;
      }
    }
  }
}