/* eslint-disable max-len */
import React, { useEffect } from 'react';
import {
  Dropdown,
  Input,
  MenuProps,
  Button,
  Popconfirm,
  message,
  Form,
  InputNumber,
  Tooltip,
} from 'antd';
import {
  CopyOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  HolderOutlined,
  PlusOutlined,
  UpOutlined,
} from '@ant-design/icons';
import { useSetState } from 'ahooks';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';
import { AppraisalFormModal } from '@/common/components/AppraisalFormModal';
import classNames from 'classnames';
import { IndicatorItemType } from '@/common/components/RelatedIndicator';
import JSCookie from 'js-cookie';
import { isEmpty } from 'lodash';
import { AppraisalItem } from '../appraisal-item';
import store from '../../store';
import { variableGroup } from './helpr';

import './index.less';

interface Iprops {
  subAssessItemShow: boolean;
  constantResultFormData: any;
  moduleType: string;
  // form: FormInstance<any>
  [x: string]: any;
}

const IndicatorList = SortableContainer(({ children }) => (
  <div className="u-d-flex appraisal-result">{children}</div>
));

const IndicatorListItemHandler = SortableHandle(() => (
  <HolderOutlined className=" u-cursor-pointer handler touch" />
));

const IndicatorListItem = SortableElement((props: any) => (
  <div
    className={classNames('u-bgc-fafafa u-br-4 u-mb-16 u-mr-16 appraisal-result__item', {
      unfold: props?.status,
    })}
  >
    <div className="u-flex-ai-center appraisal-result__item_child">
      <IndicatorListItemHandler />
      <div className="wrapInput">
        <Input
          bordered={false}
          placeholder="考核模块名称"
          type="text"
          defaultValue={props?.name}
          onBlur={(event) => {
            props.onFormChange(event, 'name');
          }}
        />
      </div>
      <div className="slide_line" />
      <div className="wrapOperation">
        <EditOutlined className="u-cursor-pointer" onClick={props.onEdit} />
        <div onClick={props.onChangeStatus}>
          {props?.status ? (
            <UpOutlined className="u-cursor-pointer " />
          ) : (
            <DownOutlined className="u-cursor-pointer " />
          )}
        </div>
      </div>
    </div>
    <AppraisalItem
      value={props.modules}
      options={props.options}
      business={props.business}
      selectProduct={props.selectProduct}
      onChange={(data) => {
        props.onFormChange(data, 'modules', 'others');
      }}
      needAutoGenerate={props.needAutoGenerate}
      switchSeeFormular={props.switchSeeFormular}
      appraisalResult={props.appraisalResult}
      seeAppraisalDetails={() => props.onEdit()}
      constantResultFormData={props.constantResultFormData}
      setModuleResult={props.setModuleResult}
      periodTypeWatch={props?.periodTypeWatch}
      periodValueWatch={props?.periodValueWatch}
      periodValueMultiWatch={props?.periodValueMultiWatch}
      businessGroup={props?.businessGroup}
      calendarMode={props?.calendarMode}
    />
  </div>
));

export const AppraisalResults: React.FC<Iprops> = (props) => {
  const indexStore = store.useStore();
  const { subAssessItemShow, constantResultFormData, form } = props;
  const {
    currentBusinessInfoSimpleData,
    dimension,
    tracertInfo,
    businessGroup,
    selectProduct,
    role,
  } = indexStore.store;
  const businessFormData = Form.useWatch('business', form);
  const isMoreLevel = dimension?.filter(
    (it) => it.code === JSCookie.get('ASSESS_PLAN_PRODUCT_CODE'),
  )?.[0]?.moreLevel;
  /** 拆解选择的业务域 */
  const business = isMoreLevel ? businessFormData?.[0] : businessFormData;

  const [state, setState] = useSetState<{
    indicators?: IndicatorItemType[];
    editFromData?: IndicatorItemType;
    editFormIndex?: number;
    indicatorsStatus?: Array<{ status: boolean }>;
    resultType?: 'custom' | 'auto';
    itemOptions?: any; // 考核项在考核模块中考核结果的枚举源
    [key: string]: any;
  }>({
    editFormIndex: null,
    editFromData: null,
    indicators: props.value || [],
    resultType: null,
    itemOptions: [],
    indicatorsStatus: props.value?.map(() => ({ status: subAssessItemShow })) || [],
  });

  useEffect(() => {
    if (props.value?.length > 0) {
      const curBlockMoudles = props.value.map((ele) => {
        if (ele?.modules?.length > 0) {
          const BlockMoudles = ele.modules?.map((it, idx) => ({
            type: it?.moduleType,
            id: it?.id ? `$${it?.id}` : '',
            name: it?.name || `未命名考核项${idx + 1}`,
            weight: it?.ruleGroup?.weight?.textExpression || '',
            configType: it?.formatType === 'NUMBER' ? 'NUMBERIC' : it?.formatType,
          }));
          if (ele.ruleGroup?.showType === 'EXPRESSION') {
            // eslint-disable-next-line no-param-reassign
            ele.ruleGroup.expression.expression = variableGroup(BlockMoudles);
          }
          return ele;
        }
        return ele;
      });
      const curBlockMoudlesOptions = props.value.map((ele) => {
        if (ele?.modules?.length > 0) {
          const BlockMoudlesOptions = ele.modules?.map((it, idx) => ({
            configType: 'NUMBERIC',
            label: it?.name || `未命名考核项${idx + 1}`,
            type: 'CheckItem',
            value: it?.id || '',
            weight: it?.ruleGroup?.weight?.textExpression || null,
          }));
          return BlockMoudlesOptions;
        }
        return [];
      });
      setState({
        indicators: curBlockMoudles,
        itemOptions: curBlockMoudlesOptions,
        indicatorsStatus: props.value?.map(() => ({ status: subAssessItemShow })) || [],
      });
    } else {
      setState({
        indicators: [],
        itemOptions: [],
        indicatorsStatus: [],
      });
    }
  }, [JSON.stringify(props.value), state.resultType]);

  useEffect(() => {
    if (props.onChange) {
      props.onChange(state.indicators);
    }
  }, [JSON.stringify(state.indicators)]);

  // 修改每一个考核结果小块的展开和收缩
  useEffect(() => {
    setState((prevState) => {
      const tempStatusList = prevState.indicatorsStatus?.map(() => ({ status: subAssessItemShow }));
      return {
        indicatorsStatus: tempStatusList,
      };
    });
  }, [subAssessItemShow]);

  // 当考核项名称修改
  const assignVarMap = (obj, newVarMap) => {
    // 判断对象是否为对象或数组
    if (typeof obj === 'object' && obj !== null) {
      // 如果是数组，则遍历每个项并递归调用assignVarMap函数
      if (Array.isArray(obj)) {
        for (let i = 0; i < obj.length; i++) {
          assignVarMap(obj[i], newVarMap);
        }
      } else {
        // 遍历对象的每个属性
        for (const key in obj) {
          if (obj.hasOwnProperty(key) && obj[key] != null) {
            const value = obj[key];
            if (key === 'varMap') {
              Object.keys(value).forEach((id) => {
                if (newVarMap[id]) {
                  value[id] = newVarMap[id];
                }
              });
            } else if (typeof value === 'object') {
              // 如果属性值是对象，则递归调用assignVarMap函数
              assignVarMap(value, newVarMap);
            }
          }
        }
      }
    }
  };

  const onModulesChange = (indicator) => {
    if (indicator?.modules?.length > 0) {
      const newVarMap = indicator?.modules?.reduce((r, m) => {
        r[`$${m.id}`] = m.name;
        return r;
      }, {});
      assignVarMap(indicator?.ruleGroup, newVarMap);
    }
  };

  return (
    <>
      <IndicatorList
        useDragHandle
        axis="xy"
        helperClass="appraisal-result__item-helper"
        onSortEnd={({ oldIndex, newIndex }) => {
          setState((prevState) => ({
            indicators: arrayMoveImmutable([...prevState.indicators], oldIndex, newIndex),
            indicatorsStatus: arrayMoveImmutable(
              [...prevState.indicatorsStatus],
              oldIndex,
              newIndex,
            ),
          }));
        }}
      >
        {state.indicators?.map((indicator, index) => (
          <IndicatorListItem
            periodTypeWatch={props?.periodTypeWatch}
            periodValueWatch={props?.periodValueWatch}
            periodValueMultiWatch={props?.periodValueMultiWatch}
            key={`item-${index}-${indicator?.name}`}
            index={index}
            options={currentBusinessInfoSimpleData}
            status={state.indicatorsStatus?.[index]?.status}
            constantResultFormData={props.constantResultFormData}
            name={indicator?.name}
            business={business}
            selectProduct={selectProduct}
            weight={indicator?.ruleGroup?.weight}
            appraisalResult={indicator?.ruleGroup?.expression?.expression}
            modules={indicator.modules}
            needAutoGenerate={indicator.resultFormat?.expressionType !== 0}
            switchSeeFormular={indicator?.ruleGroup?.showType === 'EXPRESSION'}
            setModuleResult={(data) => {
              setState((prevState) => {
                const newIndicators = [...prevState.indicators];
                newIndicators[index].ruleGroup.expression = {
                  ...newIndicators[index].ruleGroup.expression,
                  expression: data,
                };
                return { indicators: newIndicators };
              });
            }}
            onFormChange={(event, key, type = 'input') => {
              const targetValue = type === 'input' ? event.target.value : event;
              setState((prevState) => {
                const newIndicators = [...prevState.indicators];
                if (key === 'name' || key === 'modules') {
                  newIndicators[index][key] = targetValue;
                }
                if (key === 'modules') {
                  onModulesChange(newIndicators[index]);
                }
                return { indicators: newIndicators };
              });
            }}
            onEdit={() => {
              const curExpType = indicator?.resultFormat?.expressionType;
              if (!isEmpty(curExpType) || curExpType === 0) {
                setState({ resultType: curExpType === 0 ? 'auto' : 'custom' });
              }
              
              setState((prevState) => ({
                editFromData: prevState.indicators[index],
                editFormIndex: index,
              }));
            }}
            onChangeStatus={() => {
              setState((prevState) => {
                const newIndicatorsStatus = [...prevState.indicatorsStatus];
                newIndicatorsStatus[index].status = !newIndicatorsStatus?.[index]?.status;
                return {
                  indicatorsStatus: newIndicatorsStatus,
                };
              });
            }}
            businessGroup={businessGroup}
            calendarMode={props?.calendarMode}
            disabled={props?.disabled}
          />
        ))}
      </IndicatorList>

      <AppraisalFormModal
        drawerType="appraisalBlock"
        open={state.editFormIndex !== null}
        formData={state.editFromData}
        options={currentBusinessInfoSimpleData}
        tracertInfo={tracertInfo}
        business={business}
        selectProduct={props.moduleType}
        onConfirm={(newFormData) => {
          setState((prevState) => {
            const indicators = [...prevState.indicators];
            indicators[state.editFormIndex] = { ...prevState.editFromData, ...newFormData };
            return {
              indicators,
              editFormIndex: null,
              editFromData: null,
            };
          });
        }}
        onCancel={() => setState({ editFormIndex: null })}
        periodTypeWatch={props?.periodTypeWatch}
        periodValueWatch={props?.periodValueWatch}
        periodValueMultiWatch={props?.periodValueMultiWatch}
        businessGroup={businessGroup}
        calendarMode={props?.calendarMode}
        role={role}
      />
    </>
  );
};
