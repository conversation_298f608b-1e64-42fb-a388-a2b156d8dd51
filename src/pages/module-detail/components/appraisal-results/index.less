.appraisal-result {
  flex-wrap: wrap;
  align-items: stretch;

  &__item,
  &__item--add {
    width: 368px;
    flex-shrink: 0;
  }

  &__item {
    background-color: #fafafa;
    border: 1px solid rgba(0, 0, 0, 10%);
    border-radius: 4px;
    transition: height 0.5 ease-in-out;
    height: 52px;

    .touch {
      position: relative;
      z-index: 999;
      left: -16px;
      opacity: 0;
      cursor: pointer;
      transition: opacity 0.2 ease-in-out;
    }

    &_child {
      height: 50px;
      padding: 9px 0;
      box-sizing: border-box;
      border-bottom: 1px solid transparent;

      &:hover > .touch {
        opacity: 1;
        color: rgba(0, 0, 0, 85%);
      }

      .ant-input-number {
        width: 44px;
        height: 24px;
      }

      .wrapInput {
        width: 272px;
        box-sizing: border-box;

        .ant-input-borderless {
          background-color: #fafafa;
          font-weight: 500;
          font-size: 14px;

          &:focus {
            border: 1px solid #d9d9d9 !important;
            background-color: #fff;
          }
        }

        input {
          height: 32px;
          padding: 0 !important;
          padding-left: 8px !important;
        }
      }

      .slide_line {
        margin: 0 8px;
        height: 22px;
        width: 1px;
        border-right: 1px solid rgba(0, 0, 0, 10%);
      }

      .wrapPower {
        height: 26px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        .is-indicator .ant-input-number {
          width: 46px;
          border-right-width: 1px;
          border-radius: 2px;

          input {
            color: #595959;
          }
        }

        .ant-input-number {
          border-right-width: 0;
          color: #595959;
          background-color: #f0f0f0;
          width: 30px;
          line-height: 20px;

          &-group-addon {
            padding: 0 4px 0 0;
            background-color: #f0f0f0;
            color: #595959;
            font-size: 12px;
          }

          &-borderless {
            background-color: white;
            text-align: center;
          }

          &-disabled {
            input {
              color: black !important;
            }
          }

          &-focused {
            border: 1px solid #1a66ff !important;
            background-color: #fff;
          }

          input {
            color: #595959;
            background: transparent;
            height: 22px;
            line-height: 20px;
            box-sizing: border-box;
            font-size: 12px;
            padding: 0 !important;
            text-align: center;
          }
        }

        .ant-form-item {
          margin-bottom: 0;
        }
      }

      .wrapOperation {
        flex: 1;
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding-left: 6px;
        margin-right: 16px;

        .anticon {
          color: #666;
          margin-left: 16px;
        }
      }
    }

    .appraisal-item {
      height: 0;
      overflow: hidden;
    }

    &:hover {
      .appraisal-result__item_child {
        .touch {
          opacity: 1;
          color: rgba(0, 0, 0, 85%);
        }
      }
    }
  }

  .unfold {
    height: auto !important;

    .appraisal-result__item_child {
      border-bottom-color: rgba(0, 0, 0, 10%);
    }

    .appraisal-item {
      height: auto !important;
    }
  }
}
