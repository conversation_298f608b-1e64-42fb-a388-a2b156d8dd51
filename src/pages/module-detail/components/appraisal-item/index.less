.appraisal-item {
  flex-wrap: wrap;

  &__wrap {
    flex: 1;
    display: flex;
    justify-items: end;
    margin: 16px 15px;

    &--item--add {
      flex: 1;
      background-color: #fafafa;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    height: 44px;
    border-bottom: none;

    &:hover &--touch {
      opacity: 1;
      color: rgba(0, 0, 0, 85%);
    }

    &--touch {
      opacity: 0;
      cursor: pointer;
      transition: opacity 0.2 ease-in-out;
    }

    &_child {
      width: 338px;
      border-radius: 4px;
      border: 1px solid rgba(0, 0, 0, 10%);
      background-color: white;
      height: 44px;
      padding: 9px 0;
      padding-left: 16px;
      box-sizing: border-box;

      .ant-input-number {
        width: 44px;
        height: 24px;
      }

      .wrapInput {
        width: 255px;
        box-sizing: border-box;

        .ant-input-borderless {
          background-color: transparent;
          font-weight: 500;
          font-size: 14px;

          &:focus {
            border: 1px solid #d9d9d9 !important;
            background-color: #fff;
          }
        }

        .ant-input-number-focused {
          border: 1px solid #d9d9d9 !important;
          background-color: #fff;
        }

        input {
          height: 32px;
          padding: 0 !important;
          padding-left: 32px;
        }
      }

      .slide_line {
        margin: 0 8px;
        height: 22px;
        width: 1px;
        border-right: 1px solid rgba(0, 0, 0, 10%);
      }

      .wrapPower {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        .is-indicator .ant-input-number {
          width: 46px;
          border-right-width: 1px;
          border-radius: 2px;

          input {
            color: #595959;
          }
        }

        .ant-form-item {
          margin-bottom: 0;
        }

        .ant-input-number {
          border-right-width: 0;
          color: #595959;
          width: 30px;
          line-height: 20px;

          &-group-addon {
            padding: 0 4px 0 0;
            background-color: transparent;
            color: #595959;
            font-size: 12px;
          }

          &-borderless {
            background-color: white;
            text-align: center;
          }

          &-disabled {
            input {
              color: black !important;
            }
          }

          &-focused {
            border: 1px solid #1a66ff !important;
            background-color: #fff;
          }

          input {
            color: #595959;
            height: 22px;
            line-height: 20px;
            box-sizing: border-box;
            font-size: 12px;
            padding: 0 !important;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .wrapOperation {
        flex: 1;
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding-left: 22px;
        padding-right: 16px;

        .anticon {
          color: #666;
          margin-left: 16px;
        }
      }
    }
  }

  .unfold {
    min-height: 234px;
    border-bottom: 1px solid rgba(0, 0, 0, 10%);
  }
}

.wrap-expressTooltip {
  width: 600px;

  .ant-form-item {
    margin-bottom: 0;

    .p-l-0 {
      padding-right: 0;
      text-align: center;
    }
  }
}
