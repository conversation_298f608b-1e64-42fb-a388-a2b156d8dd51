/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import { useBoolean } from 'ahooks';
import {
  Form,
  Select,
  Button,
  message,
  Spin,
  Input,
} from 'antd';
import moment from 'moment';
import manageService from '@/_docplus/target/service/gateway/KpiPolicyManageGatewayService';
import { saveModule } from '../service';
import CommonStructure from '@/common/components/CommonStructure';
import { CommonBlockWrap } from '@/common/components/CommonBlockWrap';
import { planStateEnum, ActionTypeEnum, BusinessGroupEnum } from '@/common/Types';
import { KpiPolicyEditGatewayRequest } from '@/_docplus/target/types/gateway';
import store from '../store';
import { DetailAnchorEnum } from '../helper';
import { transFormModuleToItem, transFormItemToModule } from './helper';
import './index.less';
import { AppraisalResults } from './appraisal-results';
import ModuleVersionSelector from '@/common/components/ModuleVersionSelector';

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};

const { Option } = Select;

const Index: React.FC = () => {
  const [form] = Form.useForm();
  const history = useHistory();
  const itemTypeWatch = Form.useWatch('itemType', form);
  const versionWatch = Form.useWatch('version', form);
  // 删除 periodValue 和 periodValueMulti 相关代码
  const [versionList, setVersionList] = useState<string[]>([]); // 已创建的版本列表

  const indexStore = store.useStore();

  const {
    editType,
    calItemList,
  } = indexStore.store;

  // const [loading, { setFalse: setLoaded }] = useBoolean(editType !== ActionTypeEnum.ADD);
  const [loading, { setFalse: setLoaded }] = useBoolean(false);

  
  // 删除 allIndicList 相关代码

  const [isRequestLoading, setIsRequestLoading] = useState(false);
  const createItemCode = React.useRef();

  const jumpHome = () => {
    history.push(`/module`);
  };

  const checkModuleName = (value) => {
    if (value?.modules?.length) {
      let flag = value.modules.every((item) => item?.name !== null && item?.name !== '');
      if (!flag) {
        message.error({ content: `考核模块名称不能为空，请先配置` });
        return false;
      }
      try {
        value.modules?.forEach((element) => {
          if (element?.modules?.length) {
            flag = element?.modules?.every((item) => item?.name !== null && item?.name !== '');
            if (!flag) {
              throw new Error('考核项名称存在空值');
            }
          }
        });
      } catch (error) {
        message.error({ content: `考核项名称不能为空，请先配置` });
        return false;
      }
    }
    return true;
  };

  const onFinishFailed = (values) => {
    console.log('onFinishFailed', values.values);
    console.log('modules', JSON.stringify(values));
    message.error({ content: '请检查表单校验报错' });
  };

  const onFinish = async (value) => {
    // 当考核模块或者里面有模块名字为空的时候提出警告
    if (!checkModuleName(value)) {
      return;
    }

    setIsRequestLoading(true);

    // 确定操作类型
    let operationType = 'CREATE_NEW';
    if (editType !== ActionTypeEnum.ADD) {
      const currentVersion = moment(value.version).format('YYYY-MM');
      const versionExists = calItemList.some(item => item.version === currentVersion);
      operationType = versionExists ? 'UPDATE' : 'CREATE_VERSION';
    }

    const request = {
      businessGroup: value?.businessGroup,
      calItem: {
        ...transFormModuleToItem(value.modules)[0],
        version: moment(value.version).format('YYYY-MM'),
        itemCode: value.itemCode,
        itemType: value.itemType,
        
      },
      operationType,
    };
    
    try {
      const res = await saveModule(request);
      const { data } = res;
      if (data.success) {
        message.success({ content: '保存成功' });
        setIsRequestLoading(false);
        jumpHome();
      } else {
        message.error({ content: data.resultMessage });
      }
    } catch (e) {
      message.error(e.message || '保存失败');
      setIsRequestLoading(false);
    }
  };

  const isLook = useMemo(() => {
    const showTag = [ActionTypeEnum.ADD, ActionTypeEnum.COPY, ActionTypeEnum.EDIT];
    return !showTag.includes(editType);
  }, [editType]);

  // useEffect(() => {


  // }, [itemTypeWatch]);

  // 监听 calItemList 变化，单独设置版本列表
  useEffect(() => {
    if (!calItemList?.length) return;
    setVersionList(calItemList.map((item) => item.version));
  }, [calItemList]);

  // 处理表单数据更新的方法
  const handleFormDataUpdate = async () => {
    if (!calItemList?.length) return;

    let matchedItem;
    if (versionWatch) {
      // 版本格式化为字符串进行比对
      const versionStr = moment(versionWatch).format('YYYY-MM');
      matchedItem = calItemList.find(item => item.version === versionStr);
    }

    if (!matchedItem) {
      matchedItem = calItemList[0];
    }

    if (matchedItem) {
      // 先清空 modules 字段
      form.setFieldsValue({ modules: [] });
      
      // 异步处理，确保清空操作完成后再赋值
      await new Promise(resolve => setTimeout(resolve, 0));
      
      const modules = transFormItemToModule([matchedItem]);
      const formd: any = {
        version: versionWatch || moment(matchedItem.version, 'YYYY-MM'),
        businessGroup: matchedItem.businessGroup,
        itemType: matchedItem.itemType,
        itemCode: matchedItem.itemCode,
        modules,
      };
      form.setFieldsValue(formd);
      indexStore.handleBusinessGroupChange(matchedItem.businessGroup);
    }
  };

  // 监听模块版本切换，自动回填表单内容
  useEffect(() => {
    handleFormDataUpdate();
  }, [versionWatch, calItemList]);

  const onValuesChange = (formChange) => {
    // console.log('%c Line:1119 🍺 formChange', 'color:#465975', formChange);
  };

  /** 默认form>modules数据 ---start */
  const constantResultFormData = {
    id: null,
    name: null,
    itemType: null,
    ruleGroup: {
      weight: null,
      expression: {
        expression: null,
      },
      indicatorRules: null,
      arrayExpressionObj: null,
      indicatorSortExtension: null,
      showType: 'EXPRESSION',
    },
    resultFormat: {
      expressionType: 1,
      formatType: 'NUMBER',
      precision: 2,
      unitName: '无',
      thousandthShow: false,
    },
  };
  /** 默认form>modules数据 ---end */

  // 公式处理
  // 公式名称处理
  // 公式转换

  const handleItemTypeChange = async () => {
    const itemTypeVal = form.getFieldValue('itemType');
    indexStore
      .generateUniqueId()
      .then((val) => {
        form.setFieldsValue({
          itemType: itemTypeVal || 'NUMERIC',
          modules: [{
            "id": val,
            "name": null,
            "itemType": "NORMAL",
            "ruleGroup": {
                "weight": null,
                "expression": {
                    "expression": null
                },
                "indicatorRules": null,
                "arrayExpressionObj": null,
                "indicatorSortExtension": null,
                "showType": itemTypeVal === 'NUMERIC' ? "EXPRESSION" : "ARRAY",
                ruleGroupType: itemTypeVal === 'NUMERIC' ? "EXPRESSION" : "ARRAY",
            },
            "resultFormat": {
                "expressionType": 1,
                "formatType": "NUMBER",
                "precision": 2,
                "unitName": "无",
                "thousandthShow": false
            },
            "level": 2,
            "sortNum": 1,
            "modules": []
          }],
        });
      })
      .catch(() => {
        message.error('唯一ID获取失败,请稍后重试');
      });
  };

  return (
    <div className="detail-add-warp">
      <Form
        form={form}
        {...layout}
        className="form-warp"
        onFinish={(forms) => {
          onFinish({ ...forms });
        }}
        onFinishFailed={onFinishFailed}
        onValuesChange={onValuesChange}
        scrollToFirstError
        disabled={editType === ActionTypeEnum.SEE}
      >
        <CommonStructure.Section
          id={DetailAnchorEnum.BaseInfo}
          title="基础信息"
          className="detail-add-base"
        >
          <CommonBlockWrap>
            <Form.Item label="模块版本" name="version" rules={[{ required: true }]}> 
              <ModuleVersionSelector
                editType={editType}
                versionList={versionList}
                onVersionListChange={setVersionList}
                disabled={editType === ActionTypeEnum.SEE}
              />
            </Form.Item>
            <Form.Item label="业务域" name="businessGroup" rules={[{ required: true }]}> 
              <Select
                placeholder="请选择"
                allowClear
                options={BusinessGroupEnum}
                onChange={(val) => {
                  // setBusinessEnum(val); // 移除 businessEnum 相关代码
                  indexStore.handleBusinessGroupChange(val);
                }}
                disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT}
              />
            </Form.Item>
            {/* <Form.Item label="模块名称" name="limitWord" rules=[
              {
                required: true,
                message: '请输入模块名称',
              },
            ]>
              <Input
                placeholder="模块名称"
                maxLength={50}
              />
            </Form.Item> */}
            <Form.Item name="itemType" label="模块类型" rules={[{ required: true }]}> 
              <Select
                placeholder="请选择"
                disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT}
                onChange={handleItemTypeChange}
              >
                <Option value="NUMERIC" key="MONTH">
                  数值
                </Option>
                <Option value="LEVEL" key="MULTI_MONTH">
                  目标档位
                </Option>、
                <Option value="RANK" key="MULTI_MONTH">
                  牌级
                </Option>
              </Select>
            </Form.Item>
            <Form.Item label="自定义code" name="itemCode" rules={[{ required: true, message: '请输入自定义code' }]}>  
              <Input placeholder="请输入自定义code" disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT} style={{ width: '200px' }}/>
            </Form.Item>
          </CommonBlockWrap>
        </CommonStructure.Section>

        {loading ? (
          <Spin />
        ) : (
          <CommonStructure.Section
            id={DetailAnchorEnum.BaseInfo}
            title="模块逻辑"
          >
              <CommonBlockWrap>
                <Form.Item name="modules" noStyle>
                  <AppraisalResults
                    subAssessItemShow
                    form={form}
                    constantResultFormData={constantResultFormData}
                    // 删除 periodValue 和 periodValueMulti 相关变量声明和赋值
                    disabled={editType === ActionTypeEnum.SEE}
                    moduleType={itemTypeWatch}
                    calendarMode="relative"
                  />
                </Form.Item>
              </CommonBlockWrap>

            {!isLook && (
              <div className="detail-done-warp">
                <Form.Item wrapperCol={{ ...layout.wrapperCol, offset: 8 }}>
                    <Button type="primary" htmlType="submit" loading={isRequestLoading}>
                      保存
                    </Button>
                  </Form.Item>
              </div>
            )}
          </CommonStructure.Section>
        )}
      </Form>
    </div>
  );
};

// eslint-disable-next-line no-restricted-syntax
export default Index;
