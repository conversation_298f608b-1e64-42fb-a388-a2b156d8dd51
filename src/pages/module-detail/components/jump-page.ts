export const urlEnum = {
  daily: 'https://xy.daily.elenet.me/',
  ppe: 'https://ppe-xy.ele.me/',
  production: 'https://xy.ele.me/',
};

export const useJumpPage = () => {
  const shortLink = 'xy-kbzcfagl';
  const hostname = new URL(window.location.href)?.hostname;

  const jumpHome = () => {
    if (hostname?.includes('daily')) {
      globalThis.location.replace(`${urlEnum.daily}${shortLink}`);
    } else if (hostname?.includes('ppe')) {
      globalThis.location.replace(`${urlEnum.ppe}${shortLink}`);
    } else {
      globalThis.location.replace(`${urlEnum.production}${shortLink}`);
    }
  };

  return {
    jumpHome,
  };
};
