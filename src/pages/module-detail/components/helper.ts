import moment from 'moment';

/** 处理考核周期提交数据 */
export const formatPeriodFormToData = (time, periodType: string, isStart = false) => {
  if (!time) return null;
  if (periodType === 'MONTH') {
    if (isStart) {
      return time.format('YYYY-MM-01');
    }
    return moment(time).add(1, 'months').format('YYYY-MM-01');
  } else if (periodType === 'QUARTER') {
    if (isStart) {
      return moment(time[0]).format('YYYY-MM-01');
    }
    return moment(time[1]).add(1, 'months').format('YYYY-MM-01');
  } else if (periodType === 'CUSTOMIZE') {
    if (isStart) {
      return moment(time[0]).format('YYYY-MM-DD');
    }
    return moment(time[1]).format('YYYY-MM-DD');
  }
  return undefined;
};

/** 处理考核周期回显数据 */
export const formatPeriodDataToForm = (periodType: string, startTime, endTime) => {
  if (periodType === 'MONTH') {
    return moment(startTime);
  } else if (periodType === 'QUARTER') {
    return [moment(startTime), moment(endTime).subtract(1, 'month').startOf('month')];
  } else if (periodType === 'CUSTOMIZE') {
    return [moment(startTime), moment(endTime)];
  }
  return null;
};

export const transFormModuleToItem = (modules: any[]) =>
  modules?.map(
    (
      module, // 考核项
    ) => ({
      itemId: module.id,
      sortNum: module.sortNum,
      itemName: module.name,
      itemType: 'NORMAL',
      itemCode: module.itemCode,
      level: module.level,
      ruleGroupInfo: module.ruleGroup,
      resultFormat: module.resultFormat,
      calItems: transFormModuleToItem(module.modules),
      extraIndicators: module.extraIndicators,
      description: module.description,
      onlySelfIndicator: module.onlySelfIndicator,
    }),
  );

export const transFormItemToModule = (items: any[]) =>
  items?.map(
    (
      item, // 考核项
    ) => ({
      id: item.itemId,
      sortNum: item.sortNum,
      name: item.itemName,
      itemCode: item.itemCode,
      moduleType: 'NORMAL',
      level: item.level,
      ruleGroup: item.ruleGroupInfo,
      resultFormat: item.resultFormat,
      modules: transFormItemToModule(item.calItems),
      extraIndicators: item.extraIndicators,
      description: item.description,
      onlySelfIndicator: item.onlySelfIndicator,
    }),
  );
