import React from 'react';
import { But<PERSON>, Row, Form, Table, Tooltip, TableColumnsType } from 'antd';
import { useAntdTable } from 'ahooks';
import moment from 'moment';
import { openParentPage } from '@alife/kb-biz-util';
import { BusinessGroupEnumMap, ModuleTypeEnum } from '@/common/Types';
import { TableActions } from '@/common/components/TableActions';
import CommonStructure from '@/common/components/CommonStructure';
import { pageQuery } from './service';
import type { CalItemInfoDTO } from '@/_docplus/target/types/gateway';
import { QuotaSeach } from './components';

import './style.less';

/** Tooltip通用展示逻辑 */
function columnWithTooltip(value: string) {
  return (
    <Tooltip placement="topLeft" title={value}>
      {value}
    </Tooltip>
  );
}
/** 列表跳转逻辑 */
function tableAction(action: 'see' | 'edit' | 'copy' | 'add', itemCode?: string) {
  openParentPage({
    url: itemCode
      ? `/module-detail?itemCode=${itemCode}&type=${action}`
      : `/module-detail?type=${action}`,
    project: 'mp-kpi-assess-plan',
  });
}

/** 获取生产方式中文名 */
function getIndicatorTypeName({
  indicatorType,
  sourceType,
}: {
  indicatorType: 'COMPLEX_INDICATOR' | 'ATOMIC_DIC' | 'GROUP_INDICATOR' | 'CAL_ITEM_INDICATOR';
  sourceType: any;
}) {
  if (indicatorType === 'GROUP_INDICATOR') {
    return '分组指标';
  }
  if (indicatorType === 'COMPLEX_INDICATOR') {
    return '按照规则生成';
  }
  if (indicatorType === 'CAL_ITEM_INDICATOR') {
    return '计算结果';
  }
  if (indicatorType === 'ATOMIC_DIC') {
    if (sourceType === 'dic') {
      return '基于ODPS导入';
    }
    if (sourceType === 'holo') {
      return 'HOLO';
    }
    if (sourceType === 'object') {
      return '人工导入';
    }
    if (sourceType === 'lake') {
      return '数据湖';
    }
  }
  return '';
}

const Index: React.FC = () => {
  // 表单数据
  const [formIns] = Form.useForm();

  const {
    tableProps,
    search,
    // refresh,
  } = useAntdTable(
    async ({ current, ...params }, filterData) => {
      try {
        const { data } = await pageQuery({
          ...filterData,
          itemCode: filterData?.itemCode || undefined,
          includeIndicatorRule: false,
          includeIndicatorDatasource: true,
          businessGroup: filterData?.businessGroup,
          pageSize: params.pageSize,
          pageNum: current,
          version: filterData?.version ? moment(filterData?.version).format('YYYY-MM') : undefined,
        });
        if (data.success) {
          return {
            total: data?.data?.pageInfo?.totalCount,
            list: data?.data?.calItemList,
          };
        } else {
          return {
            total: 0,
            list: [],
          };
        }
      } catch (error) {
        return {
          total: 0,
          list: [],
        };
      }
    },
    {
      form: formIns,
      defaultParams: [{ current: 1, pageSize: 10 }],
    },
  );

  const columns: TableColumnsType = [
    {
      title: '模块code',
      width: 230,
      dataIndex: 'itemCode',
    },
    {
      title: '模块名称',
      dataIndex: 'itemName',
      width: 250,
      ellipsis: true,
      render: (_, { itemName }) => columnWithTooltip(itemName),
    },
    {
      title: '业务域',
      dataIndex: 'businessGroup',
      width: 120,
      render: (_, { businessGroup }) => BusinessGroupEnumMap[businessGroup],
    },
    {
      title: '模块类型',
      dataIndex: 'itemType',
      width: 100,
      ellipsis: true,
      render: (_, { itemType }) => ModuleTypeEnum[itemType],
    },
    {
      title: '修改时间',
      dataIndex: 'gmtModified',
      width: 180,
      render: (_, { gmtModified }) => moment(gmtModified).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '修改人',
      dataIndex: 'modifiedName',
      width: 100,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      width: 180,
      render: (_, { gmtCreate }) => moment(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '创建人',
      dataIndex: 'createName',
      width: 100,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 110,
      fixed: 'right',
      render: (_, record) => (
        <TableActions
          buttons={[
            <Button
              key="edit"
              type="link"
              onClick={() => tableAction('see', record.itemCode)}
            >
              查看
            </Button>,
            <Button
              key="copy"
              type="link"
              onClick={() => tableAction('edit', record.itemCode)}
            >
              修改
            </Button>,
          ]}
        />
      ),
    },
  ];

  return (
    <CommonStructure className="quota-index">
      <CommonStructure.Header className="header">
        <Row align="bottom">
          <h2 className="u-mb-0">业绩模块</h2>
        </Row>
      </CommonStructure.Header>

      <QuotaSeach form={formIns} reset={search.reset} submit={search.submit} />

      <CommonStructure.Section>
        <Row justify="space-between">
          <Button
            className="u-ml-auto add-btn"
            type="primary"
            onClick={() => tableAction('add')}
          >
            新增模块
          </Button>
        </Row>

        <Table
          {...(tableProps as any)}
          columns={columns}
          rowKey="itemCode"
          scroll={{ x: 1500 }}
          pagination={{
            ...(tableProps.pagination || {}),
            className: 'u-mb-0',
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`,
            pageSizeOptions: [10, 50, 100, 200],
          }}
        />
      </CommonStructure.Section>
    </CommonStructure>
  );
};

export default Index;
