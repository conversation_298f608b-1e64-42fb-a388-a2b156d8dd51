import React from 'react';
import { Form, Input, Row, Col, Button, FormProps, Select, DatePicker } from 'antd';
import CommonStructure from '@/common/components/CommonStructure';
import { FormInstance } from 'antd/es/form/Form';
import './index.less';
import { BusinessGroupEnum } from '@/common/Types';

interface Props extends FormProps {
  form: FormInstance;
  reset: () => void;
  submit: () => void;
}

const QuotaSeach: React.FC<Props> = (props) => (
  <CommonStructure.Filter>
    {() => (
      <Form
        form={props.form}
        name="nest-messages"
        onFinish={props.submit}
        className="quota-seach"
      >
        <div>
          <Row gutter={48}>
            <Col span={8}>
              <Form.Item label="模块名称" name="itemName">
                <Input
                  type="text"
                  allowClear
                  placeholder="请输入模块名称"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="模块code" name="itemCode" >
                <Input
                  type="text"
                  allowClear
                  placeholder="请输入模块code"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="业务域" name="businessGroup">
                <Select
                  placeholder="请选择"
                  allowClear
                  options={BusinessGroupEnum}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={48}>

            <Col span={8}>
              <Form.Item label="模块版本" name="version">
                <DatePicker picker="month" placeholder="请选择版本" style={{ width: '100%'}}/>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="模块类型" name="itemType">
                <Select
                  placeholder="请选择"
                  allowClear
                  options={[{ value: 'NUMERIC', label: '数值' }, { value: 'LEVEL', label: '目标档位' }, { value: 'RANK', label: '牌级' }]}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24} className="u-ta-right">
              <Form.Item>
                <Button
                  className="mr-10"
                  htmlType="button"
                  onClick={() => {
                    props.form.resetFields();
                    props.reset();
                  }}
                >
                  重置
                </Button>
                <Button type="primary" htmlType="submit" onClick={props.submit}>查询</Button>
              </Form.Item >
            </Col>
          </Row>
        </div>
      </Form>
    )}
  </CommonStructure.Filter>
);

export default QuotaSeach;
