import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Divider, Space } from 'antd';
import { PageHeaderProps } from 'antd/es/page-header';
import classnames from 'classnames';
import { useHistory } from 'react-router-dom';

import './index.less';

interface Props extends PageHeaderProps {
  showBack?: boolean;
  bordered?: boolean;
}

const Header: React.FC<Props> = ({
  children,
  className,
  showBack = false,
  bordered = true,
  ...props
}) => {
  const history = useHistory();
  return (
    <PageHeader
      className={
        classnames({
          'page-header-bottom': bordered,
          [`${className}`]: !!className,
        })
      }
      onBack={
        showBack ? () => history.goBack() : null
      }
      backIcon={(
        <Space>
          {/* <ArrowLeftOutlined /> */}
          返回
          <Divider type="vertical" />
        </Space>
      )}
      {...props}
    >
      {children}
    </PageHeader>
  );
};

export default Header;
