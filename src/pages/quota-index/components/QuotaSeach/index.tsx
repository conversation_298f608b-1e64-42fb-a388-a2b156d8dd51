import React from 'react';
import { Form, Input, Row, Col, Button, FormProps, Select } from 'antd';
import CommonStructure from '@/common/components/CommonStructure';
import { FormInstance } from 'antd/es/form/Form';
import { BusinessGroupEnum } from '@/common/Types';
import './index.less';

interface Props extends FormProps {
  form: FormInstance;
  reset: () => void;
  submit: () => void;
}

const QuotaSeach: React.FC<Props> = (props) => (
  <CommonStructure.Filter>
    {() => (
      <Form
        form={props.form}
        name="nest-messages"
        onFinish={props.submit}
        className="quota-seach"
      >
        <div>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label="指标名称" name="keyWord">
                <Input
                  type="text"
                  allowClear
                  placeholder="请输入指标名称"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="指标编号" name="indicatorIds" >
                <Input
                  type="text"
                  allowClear
                  placeholder="请输入指标编号"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="业务域" name="businessGroup" >
                <Select
                  defaultValue={''}
                  options={[{ value: '', label: '全部' }].concat(BusinessGroupEnum)}
                  placeholder="请选择业务域"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24} className="u-ta-right">
              <Form.Item>
                <Button
                  className="mr-10"
                  htmlType="button"
                  onClick={() => {
                    props.form.resetFields();
                    props.reset();
                  }}
                >
                  重置
                </Button>
                <Button type="primary" htmlType="submit" onClick={props.submit}>查询</Button>
              </Form.Item >
            </Col>
          </Row>
        </div>
      </Form>
    )}
  </CommonStructure.Filter>
);

export default QuotaSeach;
