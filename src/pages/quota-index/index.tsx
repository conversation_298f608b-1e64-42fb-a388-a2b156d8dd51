import React from 'react';
import { But<PERSON>, Row, Form, Table, Tooltip, TableColumnsType } from 'antd';
import { useAntdTable } from 'ahooks';
import moment from 'moment';
import { openParentPage } from '@alife/kb-biz-util';

import { TableActions } from '@/common/components/TableActions';
import CommonStructure from '@/common/components/CommonStructure';
import IndicatorGatewayService from '@/_docplus/target/service/gateway/IndicatorGatewayService';
import { IndicatorDTO } from '@/_docplus/target/types/gateway';
import { QuotaSeach } from './components';

import './style.less';

/** Tooltip通用展示逻辑 */
function columnWithTooltip(value: string) {
  return (
    <Tooltip placement="topLeft" title={value}>
      {value}
    </Tooltip>
  );
}
/** 列表跳转逻辑 */
function tableAction(action: 'detail' | 'edit' | 'copy' | 'create', indicatorId?: string) {
  openParentPage({
    url: indicatorId
      ? `/quota-add?indicatorId=${indicatorId}&type=${action}`
      : `/quota-add?type=${action}`,
    project: 'mp-kpi-assess-plan',
  });
}

/** 获取生产方式中文名 */
function getIndicatorTypeName({
  indicatorType,
  sourceType,
}: {
  indicatorType: 'COMPLEX_INDICATOR' | 'ATOMIC_DIC' | 'GROUP_INDICATOR' | 'CAL_ITEM_INDICATOR';
  sourceType: any;
}) {
  if (indicatorType === 'GROUP_INDICATOR') {
    return '分组指标';
  }
  if (indicatorType === 'COMPLEX_INDICATOR') {
    return '按照规则生成';
  }
  if (indicatorType === 'CAL_ITEM_INDICATOR') {
    return '计算结果';
  }
  if (indicatorType === 'ATOMIC_DIC') {
    if (sourceType === 'dic') {
      return '基于ODPS导入';
    }
    if (sourceType === 'holo') {
      return 'HOLO';
    }
    if (sourceType === 'object') {
      return '人工导入';
    }
    if (sourceType === 'lake') {
      return '数据湖';
    }
  }
  return '';
}

const Index: React.FC = () => {
  // 表单数据
  const [formIns] = Form.useForm();

  const {
    tableProps,
    search,
    // refresh,
  } = useAntdTable(
    async ({ current, ...params }, filterData) => {
      try {
        const { data } = await IndicatorGatewayService.queryIndicatorsByCondition({
          ...filterData,
          indicatorIds: filterData?.indicatorIds ? [filterData?.indicatorIds] : null,
          includeIndicatorRule: false,
          includeIndicatorDatasource: true,
          businessGroup: filterData?.businessGroup,
          pageSize: params.pageSize,
          pageNum: current,
        });
        return {
          total: data?.pageInfoDTO?.totalCount,
          list: data?.indicatorDTOList,
        };
      } catch (error) {
        return {
          total: 0,
          list: [],
        };
      }
    },
    {
      form: formIns,
      defaultParams: [{ current: 1, pageSize: 10 }],
    },
  );

  const columns: TableColumnsType<IndicatorDTO> = [
    {
      title: '指标名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 250,
      ellipsis: true,
      render: (_, { name }) => columnWithTooltip(name),
    },
    {
      title: '指标编号',
      width: 230,
      dataIndex: 'indicatorId',
    },
    {
      title: '生产方式',
      dataIndex: 'indicatorType',
      width: 120,
      render: (_, record) =>
        getIndicatorTypeName({
          indicatorType: record?.indicatorType,
          sourceType: record?.sourceType,
        } as any),
    },
    {
      title: '修改时间',
      dataIndex: 'gmtModified',
      width: 180,
      render: (_, record) =>
        record?.gmtModified ? moment(record?.gmtModified).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '修改人',
      dataIndex: 'modifiedName',
      width: 100,
      ellipsis: true,
      render: (_, record) => columnWithTooltip(record?.modifiedName),
    },
    {
      title: '操作',
      width: 110,
      fixed: 'right',
      render: (_, record) => (
        <TableActions
          buttons={[
            <Button
              key="edit"
              type="link"
              disabled={
                getIndicatorTypeName({
                  indicatorType: record?.indicatorType,
                  sourceType: record?.sourceType,
                }) === '人工导入'
              }
              onClick={() => tableAction('edit', record.indicatorId)}
            >
              编辑
            </Button>,
            <Button
              key="detail"
              type="link"
              disabled={
                getIndicatorTypeName({
                  indicatorType: record?.indicatorType,
                  sourceType: record?.sourceType,
                }) === '人工导入'
              }
              onClick={() => tableAction('detail', record.indicatorId)}
            >
              详情
            </Button>,
            <Button
              key="copy"
              type="link"
              disabled={
                getIndicatorTypeName({
                  indicatorType: record?.indicatorType,
                  sourceType: record?.sourceType,
                }) === '人工导入'
              }
              onClick={() => tableAction('copy', record.indicatorId)}
            >
              复制
            </Button>,
          ]}
        />
      ),
    },
  ];

  return (
    <CommonStructure className="quota-index">
      <CommonStructure.Header className="header">
        <Row align="bottom">
          <h2 className="u-mb-0">指标管理</h2>
        </Row>
      </CommonStructure.Header>

      <QuotaSeach form={formIns} reset={search.reset} submit={search.submit} />

      <CommonStructure.Section>
        <Row justify="space-between">
          <Button
            className="u-ml-auto add-btn"
            type="primary"
            onClick={() => tableAction('create')}
          >
            新增指标
          </Button>
        </Row>

        <Table
          {...(tableProps as any)}
          columns={columns}
          rowKey="indicatorId"
          scroll={{ x: 1500 }}
          pagination={{
            ...(tableProps.pagination || {}),
            className: 'u-mb-0',
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`,
            pageSizeOptions: [10, 50, 100, 200],
          }}
        />
      </CommonStructure.Section>
    </CommonStructure>
  );
};

export default Index;
