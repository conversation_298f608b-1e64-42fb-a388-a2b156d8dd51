// export enum StrategyJobRoleType {
//   KNIGHT,
//   IVR,
//   OUTER
// }


// export enum StrategyJobStatus {
//   INIT,
//   DOING,
//   DONE
// }

// export enum StrategyStatus {
//   DRAFT,
//   NOT_REVIEW,
//   REVIEW_PASS,
//   REVIEW_FAIL,
//   EFFECTIVE,
//   SUSPEND,
//   EXPIRE
// }

// export enum PoiChannel {
//   RESTAURANT,
//   NEW_RETAIL
// }

// export enum StrategyType {
//   CORRECT,
//   INSPECT
// }

// export enum TargetDatabaseType {
//   ALL,
//   ELE,
//   KOUBEI
// }

// export enum LaunchType {
//   ONCE,
//   REALTIME,
//   TIMING
// }


// export enum StrategyUserType {
//   ELE,
//   ALIBABA
// }

// export enum DataObtainType {
//   AI_QUERY,
//   MANUAL_IMPORT
// }


// export interface JobFormInterface {
//   id?: number,
//   strategyId?: number,
//   roleType: StrategyJobRoleType,
//   seq: number,
//   isDeleted?: boolean,
//   status: StrategyJobStatus,
//   createdAt: string
// }

// export interface BaseFormInterface {
//   id?: number | string | null,
//   name: string | null,
//   roleType: StrategyJobRoleType | null,
//   startTime: string | null,
//   endTime: string | null,
//   priority: number | null,
//   price: string | number | null,
//   maxPrice: number | null,
//   conformPoiCount: number | null,
//   totalFee: number | null,
//   isDeleted: boolean | null,
//   status: StrategyStatus | null,
//   creatorId: number | null,
//   isRotation: boolean | null,
//   isSkipPhoneSaleTask: boolean | null,
//   isHideExcellent: boolean | null,
//   poiSourceIds: number[] | null,
//   poiChannel: PoiChannel | null,
//   cityIds: number[] | null,
//   poiCreatedAtStart: string | null,
//   poiCreatedAtEnd: string | null,
//   majorCategoryIds: number[] | null,
//   minorCategoryIds: number[] | null,
//   strategyType: StrategyType | null,
//   targetDatabases: TargetDatabaseType[] | null,
//   launchType: LaunchType | null,
//   intervalTime: number | null,
//   userType: StrategyUserType | null,
//   creatorName: string | null,
//   creatorEmail: string | null,
//   isValidPoi: boolean | null,
//   isBusiness: boolean | null,
//   isCompletePoi: boolean | null,
//   mixSourceIds: string[] | null,
//   mixDistrictIds: number[] | null,
//   confidenceLevels: string[] | null,
//   inserted?: boolean,
//   importPois?: [{
//     id: string,
//     type: number
//   }],
//   dataObtainType: DataObtainType
//   kbBatchIds: number[],
//   kbBatchItems: any,
//   strategyFlowType: number
// }

// export interface PriceFormInterface {
//   id: number | null,
//   strategyId: number | null,
//   price: number,
//   maxPrice: number | null,
//   time: number,
//   isDeleted: boolean
// }


// export interface StrategyInfosInterface {
//   key?: any
//   baseInfo: BaseFormInterface
//   priceInfos: PriceFormInterface[]
//   jobResponses: JobFormInterface[]
// }

// export interface TabType {
//   desc: string,
//   name: string
//   value: number
//   count?: number
//   showCount?: boolean
// }

// export interface AllEnum {
//   StrategyFuseStatus: TabType[] | null
//   StrategyType: TabType[] | null
//   TargetDatabaseType: TabType[] | null
//   StrategyJobRoleType: TabType[] | null
//   LaunchType: TabType[] | null
//   StrategyStatus: TabType[] | null
// }

// export interface QueryForm {
//   searchNameOrId: string
//   creatorName: string
//   date: string[]
//   targetDatabase: number
//   strategyType: number
//   roleType: number
//   strategyFuseStatus: number
// }

// export interface QueryFilter {
//   page: number
//   pageSize: number
//   query: QueryForm
// }
