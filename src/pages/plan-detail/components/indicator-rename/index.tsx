import React from "react";
import { Form, Row, Col, Button, Select, Input, Space } from "antd";
import { PlusOutlined } from '@ant-design/icons';

export default (props: { options: any[]}) => {
  return (
    <>
      <p>* 可按需对本政策中用到的指标重命名，仅对本政策生效。</p>
      <p>* 若关联指标处已重命名，无需在此处重新命名，系统即以关联指标处重命名结果为准。</p>
       <Space direction="vertical" style={{ width: '75%'}}>
        <Row style={{ backgroundColor: '#fafafa', lineHeight: '40px', paddingLeft: '16px'}}>
          <Col span={10}>指标名称</Col>
          <Col span={10}>指标重命名</Col>
          <Col span={4}>操作</Col>
        </Row>
        <Form.List name="indicatorRenameConfig">
          {(fields, { add, remove }) => (
            <div>
              {fields.map(({ key, name, ...restField }) => (
                <Row key={key}>
                  <Col span={10}>
                    <Form.Item
                      wrapperCol={{ span: 20 }}
                      {...restField}
                      name={[name, 'indicator']}
                      rules={[{ required: true, message: '请选择指标' }]}
                    >
                      <Select
                        options={props.options || []}
                        placeholder="请选择"
                        labelInValue
                      />
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item
                      wrapperCol={{ span: 20 }}
                      {...restField}
                      name={[name, 'rename']}
                      rules={[{ required: true, message: '请输入重命名' }]}
                    >
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Button type="link" onClick={() => remove(name)}>删除</Button>
                  </Col>
                </Row>
              ))}
            <Button style={{ minWidth: '300px'}} onClick={() => add()} icon={<PlusOutlined />}>添加指标</Button>
            </div>
          )}
        </Form.List>
      </Space>
  </>)
}