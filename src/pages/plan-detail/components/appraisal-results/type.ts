interface Expression {
  varMap?: { [index: string]: string };
  textExpression?: string;
  expression?: string;
  type?: string;
  funcName?: string;
  upperLimit?: Expression;
  lowLimit?: Expression;
  ql?: string;
  varIds?: string[];
  expressionWithWeight?: string;
}
export interface moduleItem {
  /** 模块｜组件id */
  id: string;
  /** 模块名称 */
  name: string;
  /** 模块类型 */
  type: number;
  /** 权重 */
  weight?: Expression;
  /** 模块数据类型 */
  configType: string;
}
