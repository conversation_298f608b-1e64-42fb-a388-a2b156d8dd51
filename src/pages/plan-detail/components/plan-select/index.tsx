/**
 * 政策方案选择组件
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Select, Spin } from 'antd';
import moment, { Moment } from 'moment';
import PlanManageFacade from '@/_docplus/target/service/gateway/KpiPolicyQueryGatewayService';
import debounce from 'lodash/debounce';

interface IProps {
  businessGroup: 'C33_AGENT' | 'C33_DIRECT';
  periodType: string;
  periodValue: Moment;
  periodValueMulti: Moment[];
  onChange?: Function;
  value?: any[];
  initialItems?: any[]; // 政策详情接口返回的选中项，加在依赖政策前面
}
const PlanSelect = (props: IProps) => {
  const { businessGroup, periodType, periodValue, periodValueMulti, initialItems = [] } = props;

  const [fetching, setFetching] = useState(false);
  const [planList, setPlanList] = useState([]);
  useEffect(() => {
    getPlanList();
  }, [periodType, periodValue, periodValueMulti]);

  /**
   * 获取政策方案列表
   */
  const getPlanList = async (keyWords: string = '') => {
    let beginPeriod;
    let endPeriod;

    setPlanList([]);
    if (periodType === 'MONTH' && periodValue instanceof moment) {
      beginPeriod = periodValue.format('YYYYMM');
      endPeriod = periodValue.format('YYYYMM');
    }
    if (periodType === 'MULTI_MONTH' && periodValueMulti?.length === 2) {
      beginPeriod = periodValueMulti[0].format('YYYYMM');
      endPeriod = periodValueMulti[1].format('YYYYMM');
    }
    if (!beginPeriod && !endPeriod) {
      // 未选择绩效周期时，不请求
      return;
    }

    setFetching(true);
    const res = await PlanManageFacade.queryPolicyList({
      pageNo: 1,
      pageSize: 20,
      businessGroup,
      kpiPolicyName: keyWords,
      kpiPolicyStatus: 'PASS',
      beginPeriod,
      endPeriod,
    });
    setFetching(false);
    setPlanList(res?.data?.kpiPolicyDTOList || []);
  };

  const options = useMemo(() => {
    const map = {};
    const list = [];
    // 去重，详情里选中的和列表接口查出来的可能有重复，所以需要去重
    [...initialItems, ...planList].forEach((item) => {
      if (!map[item.policyId]) {
        map[item.policyId] = item;
        list.push(item);
      }
    });
    return list.map((item) => {
      return {
        label: item.policyName,
        value: item.policyId,
      };
    });
  }, [initialItems, planList]);

  return (
    <Select
      mode="multiple"
      filterOption={false}
      onSearch={debounce(getPlanList, 300)}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      {...props}
      // 下拉列表过滤已选中的选项
      options={options}
      onChange={(value) => {
        props.onChange(value);
      }}
      placeholder="请先选择绩效周期"
    />
  );
};
export default PlanSelect;
