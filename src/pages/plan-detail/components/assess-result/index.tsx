/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useCallback, useEffect, useState } from 'react';
import { Col, Form, Input, Radio, Row } from 'antd';
import { useSetState } from 'ahooks';
import { FormulaWithSearch } from '@/common/components/FormulaWithSearch';
import { Matrix } from '@/common/components/matrix';
import { RuleGroup } from '@/common/components/rule-group';
import { AssessResultMap, AssessResultNameEnum } from '@/common/Types';
import { DomainCalendarType } from '@/common/components/formula/constant';
import { regularCheck } from '@/common/utils';
import AutoGenerateFormula from '@/common/components/FormulaAutoGen';
import { NumFormatFormSection } from '@/common/components/NumFormatForm';
import { FoldableSection } from '@/common/components/FoldableSection';
import JSCookie from 'js-cookie';
import store from '../../store';

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};

const matrixLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 16 },
};

const Index: React.FC<any> = (props) => {
  const indexStore = store.useStore();
  const {
    currentBusinessInfoSimpleData,
    tracertInfo,
    selectProduct,
    productMoreLevel,
    businessGroup,
  } = indexStore.store;

  const { form = null, tracertname = null, expressionType = 1 } = props || {};
  const modules = Form.useWatch([props.name, 'modules'], form);
  const ruleGroupExpression = Form.useWatch(
    [props.name, 'ruleGroup', 'expression', 'expression'],
    form,
  );
  const showType = Form.useWatch([props.name, 'ruleGroup', 'showType']);
  const [state, setState] = useSetState({
    moduleOptions: [],
    moduleData: null,
    resultType: 'custom',
    projrct_WR_expressionType: null,
  });
  const [resultFormulaType, setResultFormulaType] = useState('公式');

  const indicatorList = useCallback(
    () => [...currentBusinessInfoSimpleData, ...state.moduleOptions],
    [currentBusinessInfoSimpleData, state.moduleOptions],
  );

  useEffect(() => {
    let moduleOptions = [];
    let moduleData = null;
    if (modules?.length) {
      moduleOptions = modules?.map((it, idx) => ({
        configType: it?.formatType === 'NUMBER' ? 'NUMBERIC' : it?.formatType,
        label: it?.name || `未命名模块${idx + 1}`,
        type: 'CheckModule',
        value: it?.id || null,
        weight: it?.ruleGroup?.weight?.textExpression || null,
      }));
      moduleData = modules?.map((it, idx) => ({
        type: it?.moduleType,
        id: it?.id ? `#${it?.id}` : null,
        name: it?.name || `未命名模块${idx + 1}`,
        weight: it?.ruleGroup?.weight || null,
        configType: it?.formatType === 'NUMBER' ? 'NUMBERIC' : it?.formatType,
      }));
    }
    if (props?.name === AssessResultNameEnum.COMM) {
      const resultOption = {
        label: '考核结果公式',
        type: 'CheckResult',
        value: 'executeKpiResult',
        configType: 'NUMBERIC',
      };
      const resultData = {
        configType: 'NUMBERIC',
        type: 'COMMON',
        id: 'executeKpiResult',
        name: '考核结果公式',
      };
      moduleOptions = moduleOptions.concat(resultOption);
      moduleData = moduleData?.length > 0 ? moduleData.concat(resultData) : [].concat(resultData);
    }
    setState({ moduleOptions, moduleData });
  }, [JSON.stringify(modules)]);

  const formulaTypeOnChange = (value) => {
    form.setFieldsValue({
      resultFormat: {
        expressionType: value === 'custom' ? 0 : 1,
      },
    });
  };

  useEffect(() => {
    if (expressionType === 0) {
      setState({ resultType: 'auto', projrct_WR_expressionType: 'single' });
    } else {
      setState({ resultType: 'custom', projrct_WR_expressionType: 'multiple' });
    }
  }, [expressionType]);

  const businessLevel = Form.useWatch('business', form);

  const renderByShowType = () => {
    if (showType === 'EXPRESSION') {
      return (
        <>
          <Form.Item
            name={[props.name, 'expressionCheck']}
            label="考核结果"
            {...layout}
            rules={[
              {
                required: true,
                validator: (_, value) => regularCheck(indicatorList(), value),
              },
            ]}
            style={{ marginBottom: 0 }}
            colon={false}
          >
            <AutoGenerateFormula
              moduleData={state.moduleData}
              ruleGroupExpression={ruleGroupExpression}
              options={indicatorList()}
              formName={[props.name, 'ruleGroup', 'expression', 'expression']}
              tracertInfo={tracertInfo}
              name={`${tracertname}/${resultFormulaType}/考核结果`}
              resultType={state.resultType}
              disabled={state.resultType === 'custom'}
              formulaTypeOnChange={formulaTypeOnChange}
              productDomain={selectProduct}
              businessLevel={productMoreLevel ? businessLevel?.[0] : businessLevel}
              periodTypeWatch={props?.periodTypeWatch}
              periodValueWatch={props?.periodValueWatch}
              periodValueMultiWatch={props?.periodValueMultiWatch}
              businessGroup={businessGroup}
            />
          </Form.Item>
          <Form.Item label=" " colon={false} {...layout}>
            <Row gutter={24}>
              <Col span={24}>
                <FoldableSection>
                  <Form.Item
                    name={[props.name, 'ruleGroup', 'expression', 'lowLimit']}
                    label="下限"
                    // validateTrigger={['onBlur']}
                    rules={[
                      {
                        validator: (_, value) => regularCheck(indicatorList(), value),
                      },
                    ]}
                    labelCol={{ span: 4 }}
                    colon={false}
                  >
                    <FormulaWithSearch
                      // formulaDebug
                      tracertInfo={tracertInfo}
                      options={indicatorList()}
                      name={`${tracertname}/${resultFormulaType}/下限`}
                      periodTypeWatch={props?.periodTypeWatch}
                      periodValueWatch={props?.periodValueWatch}
                      periodValueMultiWatch={props?.periodValueMultiWatch}
                      businessGroup={businessGroup}
                    />
                  </Form.Item>
                  <Form.Item
                    name={[props.name, 'ruleGroup', 'expression', 'upperLimit']}
                    label="上限"
                    // validateTrigger={['onBlur']}
                    rules={[
                      {
                        validator: (_, value) => regularCheck(indicatorList(), value),
                      },
                    ]}
                    labelCol={{ span: 4 }}
                    colon={false}
                  >
                    <FormulaWithSearch
                      tracertInfo={tracertInfo}
                      options={indicatorList()}
                      name={`${tracertname}/${resultFormulaType}/上限`}
                      periodTypeWatch={props?.periodTypeWatch}
                      periodValueWatch={props?.periodValueWatch}
                      periodValueMultiWatch={props?.periodValueMultiWatch}
                      businessGroup={businessGroup}
                    />
                  </Form.Item>
                  <Form.Item
                    label="公式状态"
                    name={['resultFormat', 'expressionType']}
                    noStyle
                    hidden
                    initialValue={expressionType}
                  >
                    <Input />
                  </Form.Item>
                  <NumFormatFormSection
                    labelCol={{ span: 4 }}
                    name={[props.name, 'resultFormat']}
                  />
                </FoldableSection>
              </Col>
            </Row>
          </Form.Item>
        </>
      );
    } else if (showType === 'ARRAY') {
      return (
        <>
          <Form.Item
            label=" "
            {...props}
            colon={false}
            {...matrixLayout}
            style={{ marginBottom: 0 }}
          >
            <Matrix
              tracertInfo={tracertInfo}
              formName={[props.name, 'ruleGroup', 'arrayExpressionObj', 'arrayExpressionObj']}
              defaultData={form.getFieldValue([
                props.name,
                'ruleGroup',
                'arrayExpressionObj',
                'arrayExpressionObj',
              ])}
              options={indicatorList()}
              form={form}
              tracertname={`${tracertname}/${resultFormulaType}`}
              periodTypeWatch={props?.periodTypeWatch}
              periodValueWatch={props?.periodValueWatch}
              periodValueMultiWatch={props?.periodValueMultiWatch}
              businessGroup={businessGroup}
            />
          </Form.Item>
          <Form.Item label=" " colon={false} {...matrixLayout}>
            <Row gutter={24}>
              <Col span={24}>
                <FoldableSection>
                  <Form.Item
                    name={[props.name, 'ruleGroup', 'arrayExpressionObj', 'lowLimit']}
                    label="下限"
                    {...layout}
                    rules={[
                      {
                        validator: (_, value) => regularCheck(indicatorList(), value),
                      },
                    ]}
                    colon={false}
                  >
                    <FormulaWithSearch
                      tracertInfo={tracertInfo}
                      options={indicatorList()}
                      name={`${tracertname}/${resultFormulaType}/下限`}
                      periodTypeWatch={props?.periodTypeWatch}
                      periodValueWatch={props?.periodValueWatch}
                      periodValueMultiWatch={props?.periodValueMultiWatch}
                      businessGroup={businessGroup}
                    />
                  </Form.Item>
                  <Form.Item
                    name={[props.name, 'ruleGroup', 'arrayExpressionObj', 'upperLimit']}
                    label="上限"
                    {...layout}
                    rules={[
                      {
                        validator: (_, value) => regularCheck(indicatorList(), value),
                      },
                    ]}
                    colon={false}
                  >
                    <FormulaWithSearch
                      tracertInfo={tracertInfo}
                      options={indicatorList()}
                      name={`${tracertname}/${resultFormulaType}/上限`}
                      periodTypeWatch={props?.periodTypeWatch}
                      periodValueWatch={props?.periodValueWatch}
                      periodValueMultiWatch={props?.periodValueMultiWatch}
                      businessGroup={businessGroup}
                    />
                  </Form.Item>
                  <NumFormatFormSection
                    labelCol={{ span: 4 }}
                    name={[props.name, 'resultFormat']}
                  />
                </FoldableSection>
              </Col>
            </Row>
          </Form.Item>
        </>
      );
    } else if (showType === 'RULES') {
      return (
        <>
          <Form.Item
            {...props}
            label=" "
            colon={false}
            {...matrixLayout}
            style={{ marginBottom: 0 }}
          >
            <RuleGroup
              tracertInfo={tracertInfo}
              formName={[props.name, 'ruleGroup', 'indicatorRules']}
              options={indicatorList()}
              defaultFormData={[
                {
                  resultExpressionType: 'EXPRESSION',
                },
              ]}
              form={form}
              tracertname={`${tracertname}/${resultFormulaType}`}
              periodTypeWatch={props?.periodTypeWatch}
              periodValueWatch={props?.periodValueWatch}
              periodValueMultiWatch={props?.periodValueMultiWatch}
              businessGroup={businessGroup}
            />
          </Form.Item>
          <Form.Item label=" " colon={false} {...matrixLayout}>
            <Row gutter={24}>
              <Col span={24}>
                <FoldableSection>
                  <NumFormatFormSection
                    labelCol={{ span: 4 }}
                    name={[props.name, 'resultFormat']}
                  />
                </FoldableSection>
              </Col>
            </Row>
          </Form.Item>
        </>
      );
    }
    return null;
  };

  return (
    <>
      <Form.Item
        name={[props.name, 'ruleGroup', 'showType']}
        label="计算方式"
        {...layout}
        colon={false}
        required
      >
        <Radio.Group>
          {AssessResultMap[selectProduct]?.map((it) => (
            <Radio
              value={it.value}
              key={`show-type-${it.key}`}
              onChange={(val) => {
                setResultFormulaType(it.text);
              }}
            >
              {it.text}
            </Radio>
          ))}
        </Radio.Group>
      </Form.Item>
      {renderByShowType()}
    </>
  );
};

export default Index;
