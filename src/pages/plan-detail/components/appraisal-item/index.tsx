/* eslint-disable max-len */
import React, { useEffect } from 'react';
import {
  Input,
  Button,
  Popconfirm,
  Popover,
  message,
  Tooltip,
  Form,
  InputNumber,
  Dropdown,
  MenuProps,
} from 'antd';
import { DeleteOutlined, EditOutlined, HolderOutlined, PlusOutlined } from '@ant-design/icons';
import { useSetState } from 'ahooks';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';
import { AppraisalFormModal } from '@/common/components/AppraisalFormModal';
import classNames from 'classnames';
import { IndicatorItemType } from '@/common/components/RelatedIndicator';
import { variableGroup } from '../appraisal-results/helpr';
import store from '../../store';

import './index.less';

interface Iprops {
  constantResultFormData: any;
  options: any;
  switchSeeFormular: boolean;
  // 是否需要自动生成
  needAutoGenerate?: boolean;
  // 查看考核模块详情
  seeAppraisalDetails: () => void;
  // form: FormInstance<any>
  [x: string]: any;
}

const IndicatorList = SortableContainer(({ children }) => (
  <div className="u-d-flex appraisal-item">{children}</div>
));

const IndicatorListItemHandler = SortableHandle(() => (
  <HolderOutlined className=" u-cursor-pointer handler appraisal-item__item--touch" />
));

const IndicatorListItem = SortableElement((props: any) => (
  <div
    className={classNames('u-bgc-fafafa u-br-4 u-mt-16 appraisal-item__item', {
      unfold: props?.status,
    })}
  >
    <IndicatorListItemHandler />
    <div className="u-flex-ai-center appraisal-item__item_child">
      <div className="wrapInput">
        <Input
          bordered={false}
          placeholder="考核项名称"
          type="text"
          defaultValue={props?.name}
          onBlur={(event) => {
            props.onFormChange(event, 'name');
          }}
        />
      </div>
      <div className="slide_line" />
      <div className="wrapOperation">
        <EditOutlined className="u-cursor-pointer" onClick={props.onEdit} />
        <Popconfirm title="确认删除当前考核项吗？" onConfirm={props.onDelete}>
          <DeleteOutlined className="u-cursor-pointer " />
        </Popconfirm>
      </div>
    </div>
  </div>
));

export const AppraisalItem: React.FC<Iprops> = (props) => {
  const { business, selectProduct, needAutoGenerate } = props;
  const indexStore = store.useStore();
  const { tracertInfo } = indexStore.store;
  const [state, setState] = useSetState<{
    indicatorItems?: IndicatorItemType[];
    editFromData?: IndicatorItemType;
    editFormIndex?: number;
    buttonLoading: boolean;
    /** 查看公式的默认考核结果 */
    // appraisalResult: any;
    /** 查看公式-公式改变同步函数 */
    // onAppraisalChange?: () => void
    [key: string]: any;
  }>({
    editFormIndex: null,
    editFromData: null,
    indicatorItems: props?.value || [],
    itemOptions: [],
    itemData: null, // 拓展预留
    buttonLoading: false,
    // appraisalResult: props.appraisalResult,
  });
  // 点击添加考核项目选中的回调
  const onselectItem = () => {
    setState({ buttonLoading: true });
    indexStore
      .generateUniqueId()
      .then((val) => {
        setState((prevState) => ({
          indicatorItems: [].concat(state.indicatorItems, {
            ...props.constantResultFormData,
            moduleType: 'NORMAL',
            id: val,
            level: 3,
            sortNum: prevState.indicatorItems.length + 1,
          }),
          buttonLoading: false,
        }));
      })
      .catch(() => {
        message.error('唯一ID获取失败,请稍后重试');
        setState({ buttonLoading: false });
      });
  };

  useEffect(() => {
    const tempItemData = state.indicatorItems?.map((it, idx) => ({
      type: it?.moduleType,
      id: it?.id ? `$${it?.id}` : '',
      name: it?.name || `未命名考核项${idx + 1}`,
      weight: it?.ruleGroup?.weight?.textExpression || '',
      configType: it?.formatType === 'NUMBER' ? 'NUMBERIC' : it?.formatType,
    }));

    if (props.onChange) {
      if (needAutoGenerate && tempItemData?.length) {
        props.setModuleResult(variableGroup(tempItemData));
      }
      props.onChange(state.indicatorItems);
    }
    if (props?.value?.length) {
      setState({
        itemOptions: props?.value?.map((it, idx) => ({
          configType: 'NUMBERIC',
          label: it?.name || `未命名考核项${idx + 1}`,
          type: 'CheckItem',
          value: it?.id || '',
          weight: it?.ruleGroup?.weight?.textExpression || '',
        })),
        itemData: tempItemData,
      });
    } else {
      setState({ itemOptions: [], itemData: null });
    }
  }, [JSON.stringify(state.indicatorItems)]);

  return (
    <>
      <IndicatorList
        useDragHandle
        axis="y"
        helperClass="appraisal-item__item-helper"
        onSortEnd={({ oldIndex, newIndex }) => {
          setState((prevState) => ({
            indicatorItems: arrayMoveImmutable([...prevState.indicatorItems], oldIndex, newIndex),
          }));
        }}
      >
        {state.indicatorItems?.map((indicator, index) => (
          <IndicatorListItem
            key={`item-${index}-${indicator?.name}`}
            index={index}
            options={props.options}
            name={indicator?.name}
            weight={indicator?.ruleGroup?.weight}
            onFormChange={(event, key) => {
              const targetValue = event.target.value;
              setState((prevState) => {
                const newIndicators = [...prevState.indicatorItems];
                if (key === 'name') {
                  newIndicators[index][key] = targetValue;
                } else if (key === 'weight') {
                  newIndicators[index].ruleGroup = {
                    weight: {
                      varMap: {},
                      textExpression: targetValue,
                      expression: targetValue,
                      type: 'COMMON',
                      funcName: null,
                      expressionWithWeight: targetValue,
                      ql: `v = ${targetValue};\nreturn v;`,
                      varIds: [],
                    },
                  };
                }
                return { indicatorItems: newIndicators };
              });
            }}
            onEdit={() => {
              setState((prevState) => ({
                editFromData: prevState.indicatorItems[index],
                editFormIndex: index,
              }));
            }}
            onDelete={() => {
              setState((prevState) => {
                const newIndicators = [...state.indicatorItems];
                newIndicators.splice(index, 1);
                return { indicatorItems: [...newIndicators] };
              });
            }}
          />
        ))}
        <div className="appraisal-item__wrap">
          <Button
            type="dashed"
            className="appraisal-item__wrap--item--add"
            icon={<PlusOutlined />}
            onClick={onselectItem}
            loading={state?.buttonLoading}
          >
            添加考核项
          </Button>
        </div>
      </IndicatorList>

      <AppraisalFormModal
        options={props.options}
        drawerType="appraisalItem"
        open={state.editFormIndex !== null}
        tracertInfo={tracertInfo}
        formData={state.editFromData}
        business={business}
        selectProduct={selectProduct}
        onConfirm={(newFormData) => {
          setState((prevState) => {
            const indicatorItems = [...prevState.indicatorItems];
            indicatorItems[state.editFormIndex] = newFormData;
            return {
              indicatorItems,
              editFormIndex: null,
              editFromData: null,
            };
          });
        }}
        onCancel={() => setState({ editFormIndex: null })}
        periodTypeWatch={props?.periodTypeWatch}
        periodValueWatch={props?.periodValueWatch}
        periodValueMultiWatch={props?.periodValueMultiWatch}
        businessGroup={props?.businessGroup}
      />
    </>
  );
};
