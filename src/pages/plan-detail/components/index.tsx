/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { useHistory } from 'react-router-dom';
import { useBoolean } from 'ahooks';
import {
  Form,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Button,
  message,
  Anchor,
  Space,
  Checkbox,
  Spin,
  Modal,
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import moment, { Moment } from 'moment';
import { isEqual } from 'lodash';
import { CommonUpload } from '@alife/mp-oss-upload';
import { BUCKET_PID, BUCKET_NAME } from '@/common/constants';
import { CalItemInfoDTO, KpiPolicyEditGatewayRequest } from '@/_docplus/target/types/gateway';
import { planStateEnum, AssessResultNameEnum, ActionTypeEnum } from '@/common/Types';
import {
  handleRuleFormToData,
  handleRuleDataToForm,
  debounce,
  monthListToStr,
  strToMonthList,
  lazyValidate,
} from '@/common/utils';
import manageService from '@/_docplus/target/service/gateway/KpiPolicyManageGatewayService';
import CommonStructure from '@/common/components/CommonStructure';
import { CommonBlockWrap } from '@/common/components/CommonBlockWrap';
import { BUSINESS_DOMAIN } from '@/pages/index/components/seach-list/constant';
import store from '../store';
import AssessResult from '../components/assess-result';
import { DetailAnchorEnum } from '../helper';
import AssessObject from '@/common/components/AssessObject';
import IndicatorRename from './indicator-rename';
import { transFormModuleToItem, transFormItemToModule } from './helper';
import { AppraisalResults } from './appraisal-results';
import PlanSelect from './plan-select';
import './index.less';

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};

const { Option } = Select;
const { TextArea } = Input;
const { Link } = Anchor;

const Index: React.FC = () => {
  const [form] = Form.useForm();
  const history = useHistory();
  const periodTypeWatch = Form.useWatch('periodType', form);
  const periodValueWatch: Moment | null = Form.useWatch('periodValue', form);
  const periodValueMultiWatch: Moment[] | null = Form.useWatch('periodValueMulti', form);
  const [businessEnum, setBusinessEnum] = useState<string>('');

  const latestPeriodTypeAndValueRef = useRef({
    periodTypeWatch,
    periodValueWatch,
    periodValueMultiWatch,
  });
  latestPeriodTypeAndValueRef.current = {
    periodTypeWatch,
    periodValueWatch,
    periodValueMultiWatch,
  };

  const indexStore = store.useStore();

  const {
    planId,
    role,
    editType,
    currentBusinessInfoSimpleData,
    ruleModuleIndic,
    ruleItemIndic,
    planDetails,
    indicatorTypeList,
    businessGroup,
  } = indexStore.store;

  const [loading, { setFalse: setLoaded }] = useBoolean(editType !== ActionTypeEnum.ADD);

  const allIndicList = [
    ...(ruleModuleIndic || []),
    ...(ruleItemIndic || []),
    ...(currentBusinessInfoSimpleData || []),
  ];

  const [isRequestLoading, setIsRequestLoading] = useState(false);
  const createPlanId = useRef();

  const jumpHome = () => {
    history.push(`/?role=${role}`);
  };

  const checkModuleName = (value, type) => {
    if (value?.modules?.length) {
      let flag = value.modules.every((item) => item?.name !== null && item?.name !== '');
      if (!flag) {
        message.error({ content: `${type}考核模块名称不能为空，请先配置` });
        return false;
      }
      try {
        value.modules?.forEach((element) => {
          if (element?.modules?.length) {
            flag = element?.modules?.every((item) => item?.name !== null && item?.name !== '');
            if (!flag) {
              throw new Error('考核项名称存在空值');
            }
          }
        });
      } catch (error) {
        message.error({ content: `${type}考核项名称不能为空，请先配置` });
        return false;
      }
    }
    return true;
  };

  const onFinishFailed = (values) => {
    console.log('onFinishFailed', values.values);
    message.error({ content: '请检查表单校验报错' });
  };

  const onFinish = async (value, withPublish?: boolean) => {
    if (!value.kpiPolicyName) {
      message.error('请输入方案名称');
      return;
    }
    // 当考核模块或者里面有模块名字为空的时候提出警告
    if (!checkModuleName(value.kpiItem, '考核结果')) {
      return;
    }

    if (!checkModuleName(value.commItem, '薪酬结果')) {
      return;
    }

    setIsRequestLoading(true);

    let itemIds = [];
    if (editType === ActionTypeEnum.ADD) {
      itemIds = await Promise.all([
        indexStore.generateUniqueId(false),
        indexStore.generateUniqueId(false),
      ]);
    } else {
      itemIds = [planDetails?.kpiItem?.itemId, planDetails?.commItem?.itemId];
    }

    const kpiItem: any = {
      sortNum: 1,
      itemId: itemIds?.[0],
      itemName: '考核结果',
      itemType: 'NORMAL',
      ruleGroupInfo: handleRuleFormToData(value.kpiItem.ruleGroup, allIndicList),
      resultFormat: value.kpiItem.resultFormat,
      calItems: transFormModuleToItem(value.kpiItem.modules),
    };

    const commItem: any = {
      sortNum: 1,
      itemId: itemIds?.[1],
      itemName: '薪酬结果',
      itemType: 'NORMAL',
      ruleGroupInfo: handleRuleFormToData(value.commItem.ruleGroup, allIndicList),
      resultFormat: value.commItem.resultFormat,
      calItems: transFormModuleToItem(value.commItem.modules),
    };

    const periodType = value?.periodType;

    const request: Partial<KpiPolicyEditGatewayRequest> = {
      businessGroup: value?.businessGroup,
      kpiPolicyName: value.kpiPolicyName,
      kpiPolicyDesc: value.kpiPolicyDesc,
      policyOssKey: value.policyOssKey?.[0]?.ossFileName,
      detailIndicatorTypeList: value?.detailIndicatorTypeList,
      preDependencyPolicyList: value?.preDependencyPolicyList,
      periodType,
      objectType: [value.objectType],
      objectOssKey: value.objectOssKey?.[0]?.ossFileName,
      kpiItem,
      commItem,
      indicatorRenameConfig: value.indicatorRenameConfig?.map((item, index) => ({
        indicatorId: item.indicator?.value,
        name: item.indicator?.label,
        rename: item.rename,
        sortNum: index,
      })),
    };

    if (value?.objectType === 'RULE') {
      // 圈选人员
      request.objectRuleList = value?.objectRuleList;
      request.objectWhiteList = value?.objectWhiteList?.map((item) => {
        if (typeof item === 'string') {
          const res = planDetails?.kpiPolicy?.objectWhiteList?.find((w) => w.id === item);
          return res;
        } else {
          return {
            id: item.value,
            name: item.label,
          };
        }
      });
      request.objectBlackList = value?.objectBlackList?.map((item) => {
        if (typeof item === 'string') {
          const res = planDetails?.kpiPolicy?.objectBlackList?.find((w) => w.id === item);
          return res;
        } else {
          return {
            id: item.value,
            name: item.label,
          };
        }
      });
    }

    if (periodType === 'MONTH') {
      request.periodValue = moment(value?.periodValue).format('YYYYMM');
    }

    // 多月
    if (periodType === 'MULTI_MONTH') {
      request.periodValue = monthListToStr([
        moment(value?.periodValueMulti?.[0]).format('YYYYMM'),
        moment(value?.periodValueMulti?.[1]).format('YYYYMM'),
      ])?.join(',');
    }

    // 如果是服务商政策，需要标识业务类型，且考核模块和考核项必须有一个选择费用项
    if (role === 'agent') {
      // 服务商基础信息里面增加业务域选项，不需要硬性判断
      // request.businessGroup = 'C33_AGENT';
      const { modules } = value.kpiItem;
      const errList = [];
      modules.forEach((mod) => {
        if (!!mod.itemCode) {
          // 如果考核模块选择了费用项，那么考核项选不选都可以
        } else {
          if (mod.modules.length === 0) {
            // 只有考核模块，没有选考核项
            errList.push(`${mod.name} 没选【费用项】`);
          } else {
            // 选了费用项，那么考核项的每一项都必须选费用项
            mod.modules.forEach((item) => {
              if (!item.itemCode) {
                errList.push(`${mod.name} - ${item.name} 没选【费用项】`);
              }
            });
          }
        }
      });
      if (errList.length) {
        Modal.warning({
          title: '考核项提示',
          content: (
            <ol style={{ padding: 0 }}>
              {errList.map((msg) => (
                <li>{msg}</li>
              ))}
            </ol>
          ),
        });
        setIsRequestLoading(false);
        return;
      }
    } else {
      request.businessGroup = 'C33_DIRECT';
    }

    if (editType === ActionTypeEnum.EDIT) {
      // 编辑时带ID
      createPlanId.current = planId as any;
    }

    // 复制政策 -> 删除第一个考核模块 -> 创建一个新的考核模块
    // 此时 sortNum 会重复，导致服务端校验报错
    // 这里简单处理下，把 sortNum 重置位数组下标 + 1
    const overrideSortNum = (arr: CalItemInfoDTO[]) => {
      if (!arr?.length) return;

      arr.forEach((item, index) => {
        if (typeof item?.sortNum !== 'number') {
          return;
        }

        // eslint-disable-next-line no-param-reassign
        item.sortNum = index + 1;

        if (item.calItems?.length) {
          overrideSortNum(item.calItems);
        }
      });
    };
    overrideSortNum(request?.kpiItem?.calItems);
    overrideSortNum(request?.commItem?.calItems);
    try {
      request.kpiPolicyId = createPlanId.current;
      const res = await manageService.save(request);
      if (res?.data && !createPlanId.current) {
        createPlanId.current = res?.data as any;
      }
      if (withPublish && createPlanId.current) {
        await manageService.manage({
          bizAction: planStateEnum.PASS,
          kpiPolicyId: createPlanId.current,
          businessGroup: request?.businessGroup,
        });
        message.success({ content: '方案发布成功', key: 'release', duration: 10 });
      } else {
        message.success({ content: '保存成功' });
      }
      setIsRequestLoading(false);
      jumpHome();
    } catch (e) {
      setIsRequestLoading(false);
    }
  };

  const isLook = useMemo(() => {
    const showTag = [ActionTypeEnum.ADD, ActionTypeEnum.COPY, ActionTypeEnum.EDIT];
    return !showTag.includes(editType);
  }, [editType]);

  useEffect(() => {
    form.setFieldsValue({
      periodType: 'MONTH',
      detailIndicatorTypeList: [],
      kpiItem: {
        modules: [],
        ruleGroup: {
          expression: {
            // 公式
            expression: null,
          },
          // indicatorRules: [{}], // 规则组，这里使用initalValue的[{}]
          arrayExpressionObj: null, // 矩阵
          showType: 'EXPRESSION',
        },
        resultFormat: {
          formatType: 'NUMBER', // 数值类型
          precision: 2, // 数据精度
        },
      },
      commItem: {
        modules: [],
        ruleGroup: {
          expression: {
            expression: null,
          },
          // arrayExpressionObj: null,
          showType: 'EXPRESSION',
        },
        resultFormat: {
          formatType: 'NUMBER', // 数值类型
          precision: 2, // 数据精度
        },
      },
      indicatorRenameConfig: [],
    });
  }, []);

  useEffect(() => {
    if (planDetails) {
      // JSCookie.set('ASSESS_PLAN_PRODUCT_CODE', planDetails?.businessDomain);
      // 默认把考核模块、考核项 拆分填充到公式列表
      // defauleSetIndic(planDetails?.modules);
      const kpiItem = {
        ruleGroup: handleRuleDataToForm(planDetails.kpiItem?.ruleGroupInfo),
        resultFormat: planDetails.kpiItem?.resultFormat,
        modules: transFormItemToModule(planDetails.kpiItem?.calItems),
      };

      const commItem = {
        ruleGroup: handleRuleDataToForm(planDetails.commItem?.ruleGroupInfo),
        resultFormat: planDetails.commItem?.resultFormat,
        modules: transFormItemToModule(planDetails.commItem?.calItems),
      };

      const kpiPolicy = planDetails?.kpiPolicy || {};
      const objectOssKey = kpiPolicy.objectOssKey && [
        {
          uid: kpiPolicy.objectOssKey,
          // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
          name: kpiPolicy.objectOssKey.substring(kpiPolicy.objectOssKey?.lastIndexOf('/') + 1),
          ossFileName: kpiPolicy.objectOssKey,
          url: kpiPolicy.objectOssUrl,
          status: 'done',
        },
      ];

      const policyOssKey = kpiPolicy.policyOssKey && [
        {
          uid: kpiPolicy.policyOssKey,
          // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
          name: kpiPolicy.policyOssKey.substring(kpiPolicy.objectOssKey?.lastIndexOf('/') + 1),
          ossFileName: kpiPolicy.policyOssKey,
          url: kpiPolicy.policyOssUrl,
          status: 'done',
        },
      ];

      const periodType = kpiPolicy?.commDurationUnit;

      const formd: any = {
        businessGroup: kpiPolicy?.policyType === 'C33' ? 'C33_AGENT' : 'C31_AGENT',
        kpiPolicyName: kpiPolicy.policyName,
        kpiPolicyDesc: kpiPolicy.policyDesc,
        policyOssKey,
        detailIndicatorTypeList: kpiPolicy?.detailIndicatorTypeList,
        preDependencyPolicyList:
          kpiPolicy?.preDependencyPolicyList?.map((item) => item.policyId) || [],
        periodType,
        objectOssKey,
        objectType: kpiPolicy?.objectType?.[0] || 'CUSTOMIZE',
        indicatorRenameConfig: planDetails.indicatorRenameConfigDTOList?.map((item) => ({
          indicator: {
            value: item.indicatorId,
            label: item.name,
          },
          rename: item.rename,
        })),
        objectRuleList: kpiPolicy?.objectRuleList,
        objectWhiteList: kpiPolicy?.objectWhiteList?.map((item) => item.id),
        objectBlackList: kpiPolicy?.objectBlackList?.map((item) => item.id),
        kpiItem,
        commItem,
      };

      if (periodType === 'MONTH') {
        formd.periodValue = moment(kpiPolicy?.periodValue);
      }

      // 多月
      if (periodType === 'MULTI_MONTH') {
        formd.periodValueMulti = strToMonthList(kpiPolicy?.periodValue);
      }

      form.setFieldsValue(formd);
      form.validateFields();

      // useWatch 存在时序问题，会导致排名指标回填不对，见这个 bug：
      // https://aone.alibaba-inc.com/v2/project/1185953/bug/55591067
      // 这里简单处理下：
      // 1. 前提：「绩效周期类型」和「绩效周期」字段永远透出，否则 antd 的 form 永远不会回填，导致 useWatch 永远没有值
      // 2. 这里增加一个 loaded 状态，在 loaded 变为 true 时，才挂载公式编辑器组件
      // 3. 如果是需要回填的编辑/查看操作，在这里轮询 useWatch 的结果，直到结果显示回填成功才挂载
      const checkFormIsReady = () => {
        const { periodTypeWatch, periodValueWatch, periodValueMultiWatch } =
          latestPeriodTypeAndValueRef.current;
        return !!periodTypeWatch && !!(periodValueWatch || periodValueMultiWatch);
      };
      lazyValidate(checkFormIsReady).then(setLoaded);
    }
  }, [planDetails]);

  /**
   * 清空考核模块
   */
  const clearModule = () => {
    form.setFieldsValue({
      kpiItem: {
        modules: [],
        ruleGroup: {
          expression: {
            // 公式
            expression: null,
          },
          // indicatorRules: [{}], // 规则组，这里使用initalValue的[{}]
          arrayExpressionObj: null, // 矩阵
          showType: 'EXPRESSION',
        },
        resultFormat: {
          formatType: 'NUMBER', // 数值类型
          precision: 2, // 数据精度
        },
      },
      commItem: {
        modules: [],
        ruleGroup: {
          expression: {
            expression: null,
          },
          // arrayExpressionObj: null,
          showType: 'EXPRESSION',
        },
        resultFormat: {
          formatType: 'NUMBER', // 数值类型
          precision: 2, // 数据精度
        },
      },
    });
  };

  const onValuesChange = (formChange) => {
    // console.log('%c Line:1119 🍺 formChange', 'color:#465975', formChange);
  };

  /** 默认form>modules数据 ---start */
  const constantResultFormData = {
    id: null,
    name: null,
    moduleType: null,
    ruleGroup: {
      weight: null,
      expression: {
        expression: null,
      },
      indicatorRules: null,
      arrayExpressionObj: null,
      indicatorSortExtension: null,
      showType: 'EXPRESSION',
    },
    resultFormat: {
      expressionType: 1,
      formatType: 'NUMBER',
      precision: 2,
      unitName: '无',
      thousandthShow: false,
    },
  };
  /** 默认form>modules数据 ---end */

  // 公式处理
  const calculationFnForExpression = (value) => {
    let expressionVal = value;
    const expressionValRegExp = /@\d+\[\w+\|[,\d]+\]/g;

    if (expressionVal && typeof expressionVal === 'string') {
      // const matches = expressionVal?.match(expressionValRegExp);
      expressionVal = expressionVal?.replace(expressionValRegExp, '');
    }
    return expressionVal;
  };

  // 公式名称处理
  const calculationFnForOthers = (key, obj) => {
    const expressionValRegExp = /@\d+\[\w+\|[,\d]+\]/g;
    const varMapVal = obj?.varMap;
    const textExpressionVal = obj?.textExpression;
    const expressionVal = obj?.expression;

    if (expressionVal && typeof expressionVal === 'string') {
      const expressionArr = [];
      const matches = expressionVal?.match(expressionValRegExp);
      matches?.forEach((item) => {
        item?.match(/@\d+/g)?.forEach((item) => {
          expressionArr.push(item);
        });
      });

      if (!expressionArr?.length) {
        return obj[key];
      }

      if (varMapVal && varMapVal.constructor === Object && Object.keys(varMapVal).length !== 0) {
        let textExpressionReturn = textExpressionVal;
        const textExpressionArr = [];
        const varMapReturn = {};
        const varMapKeys = Object.keys(varMapVal);

        varMapKeys?.forEach((item) => {
          if (!expressionArr.includes(item)) {
            varMapReturn[item] = varMapVal[item];
          } else {
            textExpressionArr.push(varMapVal[item]);
          }
        });

        if (key === 'varMap') {
          return varMapReturn;
        }

        if (key === 'textExpression') {
          textExpressionArr?.forEach((item) => {
            textExpressionReturn = textExpressionReturn?.replace(item, '');
          });
          return textExpressionReturn;
        }
      }
    }

    return obj[key];
  };

  // 公式转换
  const replaceExpressionAndOtherValues = (
    obj,
    calculationFnForExpression,
    calculationFnForOthers,
  ) => {
    if (Array.isArray(obj)) {
      const newArr = [];

      for (let i = 0; i < obj.length; i++) {
        newArr.push(
          replaceExpressionAndOtherValues(
            obj[i],
            calculationFnForExpression,
            calculationFnForOthers,
          ),
        );
      }
      return newArr;
    } else if (typeof obj === 'object') {
      const newObj = {};

      for (const key in obj) {
        if (key === 'expression') {
          newObj[key] =
            typeof obj[key] === 'object' && obj[key]
              ? replaceExpressionAndOtherValues(
                  obj[key],
                  calculationFnForExpression,
                  calculationFnForOthers,
                )
              : calculationFnForExpression(obj[key]);
        } else if (key === 'textExpression') {
          newObj[key] = calculationFnForOthers(key, obj);
        } else if (key === 'varMap') {
          newObj[key] = calculationFnForOthers(key, obj);
        } else if (typeof obj[key] === 'object' && obj[key]) {
          newObj[key] = replaceExpressionAndOtherValues(
            obj[key],
            calculationFnForExpression,
            calculationFnForOthers,
          );
        } else {
          newObj[key] = obj[key];
        }
      }
      return newObj;
    }
    return obj;
  };

  const handlePeriodChange = async () => {
    let { kpiItem } = form.getFieldsValue();
    let { commItem } = form.getFieldsValue();
    if (editType === ActionTypeEnum.COPY) {
      // 复制时，需要根据周期重新获取考核模块,因为指标是带周期的，比如现在是11月，我复制了10月份的政策方案，但是方案里的指标可能选的都是10月份的，所以需要重新获取，重新获取时，服务端会根据前端传入的周期，把指标里的时间都给替换掉
      const vals = form.getFieldsValue();
      const { periodType, periodValue, periodValueMulti } = vals;
      let pv;
      if (periodType === 'MONTH' && periodValue) {
        pv = moment(periodValue).format('YYYYMM');
      }

      // 多月
      if (periodType === 'MULTI_MONTH' && periodValueMulti) {
        pv = monthListToStr([
          moment(periodValueMulti?.[0]).format('YYYYMM'),
          moment(periodValueMulti?.[1]).format('YYYYMM'),
        ])?.join(',');
      }

      if (pv) {
        // 先清空考核模块
        clearModule();
        // 然后通过接口重新获取与当前周期匹配的考核模块
        const { planDetails: detail } = await indexStore.queryPlanDetail(planId, editType, {
          periodType,
          periodValue: pv,
        });

        kpiItem = {
          ruleGroup: handleRuleDataToForm(detail.kpiItem?.ruleGroupInfo),
          resultFormat: detail.kpiItem?.resultFormat,
          modules: transFormItemToModule(detail.kpiItem?.calItems),
        };

        commItem = {
          ruleGroup: handleRuleDataToForm(detail.commItem?.ruleGroupInfo),
          resultFormat: detail.commItem?.resultFormat,
          modules: transFormItemToModule(detail.commItem?.calItems),
        };

        form.setFieldsValue({
          kpiPolicyName: detail?.kpiPolicy?.policyName || '',
        });
      }
    }

    const kpiItemNew = replaceExpressionAndOtherValues(
      kpiItem,
      calculationFnForExpression,
      calculationFnForOthers,
    );
    const commItemNew = replaceExpressionAndOtherValues(
      commItem,
      calculationFnForExpression,
      calculationFnForOthers,
    );

    if (!(isEqual(commItem, commItemNew) && isEqual(kpiItem, kpiItemNew))) {
      message.info('排名类指标已被清除，请重新配置！');
    }

    form.setFieldsValue({
      kpiItem: kpiItemNew,
      commItem: commItemNew,
      preDependencyPolicyList: [],
    });

    form.validateFields();
  };

  // 圈选途径规则
  const getExtraOptions = () => {
    const isAgent = role === 'agent';
    const isBusinessEnumC31 = businessEnum === 'C31_AGENT';
    const isPolicyTypeC31 = planDetails?.kpiPolicy?.policyType === 'C31';

    if (isAgent) {
      // 如果业务枚举存在且是 C31_AGENT
      if (businessEnum) {
        return isBusinessEnumC31 ? [] : [{ label: '渠道业务部', value: 'C33_AGENT' }];
      }
      // 如果业务枚举不存在且 KPI 政策类型是 C31
      return isPolicyTypeC31 ? [] : [{ label: '渠道业务部', value: 'C33_AGENT' }];
    }

    // 如果不是代理角色
    return [{ label: '圈选人员', value: 'RULE' }];
  };

  return (
    <div className="detail-add-warp">
      <Anchor className="anchor-warp">
        <Link href={`#${DetailAnchorEnum.BaseInfo}`} title="基础信息" />
        <Link href={`#${DetailAnchorEnum.Object}`} title="执行对象" />
        <Link href={`#${DetailAnchorEnum.Module}`} title="考核结果公式" />
        <Link href={`#${DetailAnchorEnum.Result}`} title="薪酬结果公式" />
        <Link href={`#${DetailAnchorEnum.Other}`} title="指标重命名" />
      </Anchor>
      <Form
        form={form}
        {...layout}
        className="form-warp"
        onFinish={(forms) => {
          onFinish({ ...forms }, true);
        }}
        onFinishFailed={onFinishFailed}
        onValuesChange={onValuesChange}
        scrollToFirstError
        disabled={editType === ActionTypeEnum.SEE}
      >
        <CommonStructure.Section
          id={DetailAnchorEnum.BaseInfo}
          title="基础信息"
          className="detail-add-base"
        >
          <CommonBlockWrap>
            {/* 服务商才展示业务域 */}
            {role === 'agent' ? (
              <Form.Item label="业务域" name="businessGroup" rules={[{ required: true }]}>
                <Select
                  placeholder="请选择"
                  allowClear
                  options={BUSINESS_DOMAIN}
                  onChange={(val) => {
                    setBusinessEnum(val);
                    indexStore.handleBusinessGroupChange(val);
                  }}
                  disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT}
                />
              </Form.Item>
            ) : null}
            <Form.Item name="periodType" label={'绩效周期类型'} rules={[{ required: true }]}>
              <Select
                placeholder="请选择"
                disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT}
                onChange={handlePeriodChange}
              >
                <Option value="MONTH" key="MONTH">
                  单月
                </Option>
                <Option value="MULTI_MONTH" key="MULTI_MONTH">
                  多月
                </Option>
              </Select>
            </Form.Item>

            {periodTypeWatch === 'MONTH' && (
              <Form.Item
                label="绩效周期"
                name="periodValue"
                rules={[
                  {
                    required: true,
                    message: '请选择绩效周期',
                  },
                ]}
              >
                <DatePicker
                  picker="month"
                  onChange={handlePeriodChange}
                  disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT}
                />
              </Form.Item>
            )}

            {periodTypeWatch === 'MULTI_MONTH' && (
              <Form.Item
                label="绩效周期"
                name="periodValueMulti"
                rules={[
                  {
                    required: true,
                    message: '请选择绩效周期',
                  },
                ]}
              >
                <DatePicker.RangePicker
                  picker="month"
                  onChange={handlePeriodChange}
                  disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT}
                />
              </Form.Item>
            )}
            {/* 直营时才展示政策依赖 */}
            {role === 'direct' && (
              <Form.Item name="preDependencyPolicyList" label="政策依赖">
                <PlanSelect
                  initialItems={planDetails?.kpiPolicy?.preDependencyPolicyList || []}
                  businessGroup="C33_DIRECT"
                  periodType={periodTypeWatch}
                  periodValue={periodValueWatch}
                  periodValueMulti={periodValueMultiWatch}
                />
              </Form.Item>
            )}

            <Form.Item name="kpiPolicyName" label="方案名称" rules={[{ required: true }]}>
              <Input placeholder="请输入" />
            </Form.Item>

            <Form.Item
              className="detail-indicator-type-list"
              name="detailIndicatorTypeList"
              label="明细指标类型"
              rules={[{ required: true }]}
            >
              <Checkbox.Group options={indicatorTypeList} />
            </Form.Item>

            <Form.Item name="policyOssKey" label="项目方案" extra="支持扩展名：.pdf">
              <CommonUpload
                className="policy_oss_key"
                ossConfigId={BUCKET_PID}
                bucketName={BUCKET_NAME}
                accept=".pdf"
                maxCount={1}
                filePath="permanent/2147483647/kpi"
              >
                <Button icon={<UploadOutlined />}>上传文件</Button>
              </CommonUpload>
            </Form.Item>
            <Form.Item name="kpiPolicyDesc" label="说明">
              <TextArea
                placeholder="请输入内容"
                style={{ height: 120 }}
                showCount
                maxLength={4000}
              />
            </Form.Item>
          </CommonBlockWrap>
        </CommonStructure.Section>

        <CommonStructure.Section
          id={DetailAnchorEnum.Object}
          title="执行对象"
          className="detail-add-object"
        >
          <CommonBlockWrap>
            {/* 如果是服务商，执行对象除了自定义，还要加一个【渠道业务部】 */}
            {/* businessEnum区分服务商旺铺跟商户通 */}
            <AssessObject
              tempUrl={
                role === 'direct'
                  ? 'https://a.amap.com/smallBiz/static/agent-xy/上传考核对象模板.xlsx'
                  : 'https://a.amap.com/smallBiz/static/agent-xy/服务商上传考核对象模板.xlsx'
              }
              extraOptions={getExtraOptions()}
              periodType={periodTypeWatch}
              periodValue={periodValueWatch}
              periodValueMulti={periodValueMultiWatch}
              kpiPolicy={planDetails?.kpiPolicy}
              disabled={editType === ActionTypeEnum.SEE}
            />
          </CommonBlockWrap>
        </CommonStructure.Section>

        {loading ? (
          <Spin />
        ) : (
          <>
            <CommonStructure.Section
              id={DetailAnchorEnum.Module}
              title="考核结果公式"
              className={periodValueWatch || periodValueMultiWatch ? '' : 'pre-event'}
            >
              <h3 className="sub_title">步骤1：配考核模块（非必填）</h3>
              <CommonBlockWrap>
                <Form.Item name={[AssessResultNameEnum.KPI, 'modules']} noStyle>
                  <AppraisalResults
                    subAssessItemShow
                    form={form}
                    constantResultFormData={constantResultFormData}
                    periodTypeWatch={periodTypeWatch}
                    periodValueWatch={periodValueWatch}
                    periodValueMultiWatch={periodValueMultiWatch}
                    disabled={editType === ActionTypeEnum.SEE}
                  />
                </Form.Item>
              </CommonBlockWrap>
              <h3 className="sub_title">步骤2：配考核结果公式</h3>
              <Row gutter={24}>
                <Col span={24}>
                  <CommonBlockWrap>
                    <AssessResult
                      expressionType={planDetails?.resultFormat?.expressionType}
                      form={form}
                      name={AssessResultNameEnum.KPI}
                      periodTypeWatch={periodTypeWatch}
                      periodValueWatch={periodValueWatch}
                      periodValueMultiWatch={periodValueMultiWatch}
                    />
                  </CommonBlockWrap>
                </Col>
              </Row>
            </CommonStructure.Section>

            <CommonStructure.Section
              id={DetailAnchorEnum.Result}
              title={role === 'direct' ? '薪酬结果公式' : '结算结果公式'}
              className={periodValueWatch || periodValueMultiWatch ? '' : 'pre-event'}
            >
              <h3 className="sub_title">步骤1：配考核模块（非必填）</h3>
              <CommonBlockWrap>
                <Form.Item name={[AssessResultNameEnum.COMM, 'modules']} noStyle>
                  <AppraisalResults
                    subAssessItemShow
                    form={form}
                    constantResultFormData={constantResultFormData}
                    periodTypeWatch={periodTypeWatch}
                    periodValueWatch={periodValueWatch}
                    periodValueMultiWatch={periodValueMultiWatch}
                    disabled={editType === ActionTypeEnum.SEE}
                  />
                </Form.Item>
              </CommonBlockWrap>
              <h3 className="sub_title">
                步骤2：{role === 'direct' ? '配薪酬结果公式' : '配结算结果公式'}
              </h3>
              <Row gutter={24}>
                <Col span={24}>
                  <CommonBlockWrap>
                    <AssessResult
                      expressionType={planDetails?.resultFormat?.expressionType}
                      form={form}
                      name={AssessResultNameEnum.COMM}
                      periodTypeWatch={periodTypeWatch}
                      periodValueWatch={periodValueWatch}
                      periodValueMultiWatch={periodValueMultiWatch}
                    />
                  </CommonBlockWrap>
                </Col>
              </Row>
            </CommonStructure.Section>

            <CommonStructure.Section
              id={DetailAnchorEnum.Other}
              title="指标重命名"
              className="detail-add-other"
            >
              <CommonBlockWrap>
                <IndicatorRename options={allIndicList} />
              </CommonBlockWrap>
            </CommonStructure.Section>

            {!isLook && (
              <div className="detail-done-warp">
                <Space>
                  <Form.Item wrapperCol={{ ...layout.wrapperCol, offset: 8 }}>
                    <Button type="primary" htmlType="submit" loading={isRequestLoading}>
                      保存并发布
                    </Button>
                  </Form.Item>
                  <Form.Item wrapperCol={{ ...layout.wrapperCol, offset: 8 }}>
                    <Button type="default" htmlType="button" onClick={jumpHome}>
                      返回列表
                    </Button>
                  </Form.Item>
                  <Form.Item wrapperCol={{ ...layout.wrapperCol, offset: 8 }}>
                    <Button
                      type="primary"
                      htmlType="button"
                      loading={isRequestLoading}
                      onClick={() => {
                        form
                          .validateFields()
                          .then(() => {
                            debounce(onFinish(form.getFieldsValue(), false), 2000);
                          })
                          .catch(() => {
                            message.error({ content: '请检查表单校验报错' });
                          });
                      }}
                    >
                      保存并退出
                    </Button>
                  </Form.Item>
                </Space>
              </div>
            )}
          </>
        )}
      </Form>
    </div>
  );
};

// eslint-disable-next-line no-restricted-syntax
export default Index;
