import React from 'react';
import { Button, Table, Space, Modal, Row, message, Form, Badge } from 'antd';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useAntdTable } from 'ahooks';
import {
  planStateEnum,
  planStateListEnum,
  containButtonStateMap,
  planStateActionEnum,
  periodTypeListEnum,
  ActionTypeEnum,
  BusinessLineEnum,
  planStatusBadge,
} from './enum';
import CommonStructure from '@/common/components/CommonStructure';
import { PERIOD_TIME_FORMAT } from '@/common/constants';
import TargetPlanQueryGatewayService from '@/_docplus/target/service/gateway/TargetPlanQueryGatewayService';
import TargetPlanOperateGatewayService from '@/_docplus/target/service/gateway/TargetPlanOperateGatewayService';
import { openParentPage } from '@alife/kb-biz-util';
import SeachList from './components/seach-list';

import './index.less';

const { confirm } = Modal;

const Index: React.FC = () => {
  const [formIns] = Form.useForm();

  const { tableProps, search, refresh } = useAntdTable(
    async ({ current, pageSize }, filterData) => {
      try {
        const { data } = await TargetPlanQueryGatewayService.queryTargetPlans({
          planName: filterData?.planName?.trim() || undefined,
          businessGroup: filterData?.businessGroup,
          businessLine: filterData?.businessLine,
          periodType: filterData?.periodType,
          periodValue: filterData?.periodValue
            ? moment(filterData?.periodValue)?.format('YYYYMM')
            : undefined,
          pageSize: pageSize,
          pageNo: current,
        });
        return {
          total: data?.pageInfoDTO?.totalCount,
          list: data?.targetPlanInfoDTOList,
        };
      } catch (error) {
        return {
          total: 0,
          list: [],
        };
      }
    },
    {
      form: formIns,
      manual: false,
      defaultPageSize: 10,
    },
  );

  const columns = [
    {
      title: '目标方案',
      dataIndex: 'planName',
      key: 'planName',
      width: 240,
      fixed: 'left',
    },
    {
      title: '目标周期类型',
      dataIndex: 'periodType',
      key: 'periodType',
      render: (_, record) => periodTypeListEnum[record?.periodType],
    },
    {
      title: '生效时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (_, record) =>
        record?.startTime && record?.endTime
          ? `${moment(record.startTime).format('YYYY-MM-DD')}~${moment(record.endTime).format(
              'YYYY-MM-DD',
            )}`
          : '--',
    },
    {
      title: '业务线',
      dataIndex: 'businessLine',
      key: 'businessLine',
      render: (_, record) => BusinessLineEnum[record?.businessLine],
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      key: 'creatorName',
    },
    {
      title: '状态',
      dataIndex: 'planStatus',
      key: 'planStatus',
      render: (_, record) => (
        <Badge
          color={planStatusBadge[record?.planStatus]}
          text={planStateListEnum[record?.planStatus]}
        ></Badge>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      render: (_, record) =>
        record?.gmtCreate ? moment(record?.gmtCreate)?.format(PERIOD_TIME_FORMAT) : '--',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      render: (_, record) => (
        <Space size="middle" wrap className="btn-list">
          <Button
            size="small"
            type="link"
            onClick={() => openAdd(ActionTypeEnum.EDIT, record?.planId)}
            disabled={containButtonStateMap.EDIT.includes(record?.planStatus)}
          >
            编辑
          </Button>
          <Button
            size="small"
            type="link"
            onClick={() => openAdd(ActionTypeEnum.SEE, record?.planId)}
          >
            查看
          </Button>
          <Button
            size="small"
            type="link"
            onClick={() => changeStatus(record?.planId, planStateEnum.PUBLISH)}
            disabled={containButtonStateMap.RELEASE.includes(record?.planStatus)}
          >
            发布
          </Button>
          <Button
            size="small"
            type="link"
            onClick={() => changeStatus(record?.planId, planStateEnum.STOP)}
            disabled={containButtonStateMap.STOP.includes(record?.planStatus)}
          >
            停用
          </Button>
        </Space>
      ),
    },
  ];

  // 跳转新增/编辑/详情页
  const openAdd = (type = ActionTypeEnum.ADD, planId = '') => {
    const project = 'mp-kpi-assess-plan';
    const base = type === ActionTypeEnum.SEE ? '/target-detail' : '/target-add';
    const queryParams = `${planId ? `?planId=${planId}` : ''}`;
    openParentPage({ url: `${base}${queryParams}`, project });
  };

  // 操作发布/停用
  const changeStatus = async (planId: string, planStatus: string) => {
    confirm({
      title: `确认${planStateActionEnum[planStatus]}目标？`,
      icon: <ExclamationCircleOutlined />,
      onOk() {
        handleActionFunc(planId, planStatus);
      },
    });
  };

  const handleActionFunc = async (planId, planStatus) => {
    try {
      const res = await TargetPlanOperateGatewayService.manageTargetPlan({
        planId,
        bizAction: planStatus,
      });
      if (!res?.success) {
        return;
      }
      if (planStatus === planStateEnum.PUBLISH) {
        message.success('目标发布成功');
      } else if (planStatus === planStateEnum.STOP) {
        message.success('停用成功');
      }
      setTimeout(() => {
        refresh();
      }, 500);
    } catch (error) {
      console.log(error, '请求异常');
    }
  };

  return (
    <CommonStructure className="target-list-index ">
      <CommonStructure.Header className="header">
        <Row align="bottom">
          <h2>目标管理</h2>
        </Row>
      </CommonStructure.Header>

      <SeachList form={formIns} reset={search.reset} submit={search.submit} />

      <CommonStructure.Section
        extra={
          <Button type="primary" onClick={() => openAdd(ActionTypeEnum.ADD)}>
            新建目标方案
          </Button>
        }
      >
        <Table
          {...(tableProps as any)}
          columns={columns}
          rowKey={(record) => `${record.planId}`}
          scroll={{ x: 1500 }}
          pagination={{
            ...(tableProps.pagination || {}),
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
        />
      </CommonStructure.Section>
    </CommonStructure>
  );
};

export default Index;
