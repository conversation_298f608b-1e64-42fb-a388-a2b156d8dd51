export enum ActionTypeEnum {
  SEE = 'see',
  EDIT = 'edit',
  ADD = 'add',
}

export enum planStateListEnum {
  INIT = '待生效',
  EFFECTING = '生效中',
  INVALID = '已完结',
}

export enum periodTypeListEnum {
  YEAR = '年',
  QUARTER = '季',
  MONTH = '单月',
  MULTI_MONTH = '多月',
  DAY = '日',
}

export const containButtonStateMap = {
  EDIT: ['EFFECTING'] /** 编辑 */,
  RELEASE: ['EFFECTING'] /** 发布 */,
  STOP: ['INIT'] /** 停用 */,
};

export enum planStateEnum {
  /**
   * 发布
   */
  PUBLISH = 'PUBLISH',
  /**
   * 停用
   */
  STOP = 'STOP',
}

export enum planStateActionEnum {
  PUBLISH = '发布',
  STOP = '停用',
}

export enum BusinessLineEnum {
  DEFAULT = '商户通-服务商',
  C33_DEFAULT = '商户通-直营',
  C31_DEFAULT = '旺铺-服务商',
  RECREATION = '休娱',
  CATERING = '美食',
  LOW_FREQUENCY = '低频教培',
  BEAUTY = '美业',
  MEDICAL_TREATMENT = '医疗',
  CLOUD_REGION = '云区/商运商推',
  CHANNEL = '渠道',
  BUSINESS_DISTRICT = '商圈',
  TELEPOHNE_SALE = '电销',
  HOME_DECORATION_RETAIL = '家装&零售',
  SKA = 'SKA',
}

export enum BusinessGroupEnum {
  C33_AGENT = '高德商户通-服务商',
  C33_DIRECT = '高德商户通-直营',
  C31_AGENT = '高德旺铺-服务商',
}

export enum planStatusBadge {
  INIT = 'orange',
  EFFECTING = 'blue',
  INVALID = 'grey',
}

/** 业务域类型 */
export const BusinessGroupOption = [
  {
    value: 'C33_DIRECT',
    label: '高德商户通-直营',
  },
  {
    value: 'C33_AGENT',
    label: '高德商户通-服务商',
  },
  {
    value: 'C31_AGENT',
    label: '高德旺铺-服务商',
  },
];

/** 业务线类型 */
export const BusinessLineOption = {
  C33_DIRECT: [
    {
      value: 'RECREATION',
      label: '休娱',
    },
    {
      value: 'CATERING',
      label: '美食',
    },
    {
      value: 'LOW_FREQUENCY',
      label: '低频教培',
    },
    {
      value: 'BEAUTY',
      label: '美业',
    },
    {
      value: 'MEDICAL_TREATMENT',
      label: '医疗',
    },
    {
      value: 'CLOUD_REGION',
      label: '云区/商运商推',
    },
    {
      value: 'CHANNEL',
      label: '渠道',
    },
    {
      value: 'BUSINESS_DISTRICT',
      label: '商圈',
    },
    {
      value: 'TELEPOHNE_SALE',
      label: '电销',
    },
    {
      value: 'HOME_DECORATION_RETAIL',
      label: '家装&零售',
    },
    {
      value: 'SKA',
      label: 'SKA',
    },
  ],
  C33_AGENT: [
    {
      value: 'DEFAULT',
      label: '商户通-服务商',
    },
  ],
  C31_AGENT: [
    {
      value: 'C31_DEFAULT',
      label: '旺铺-服务商',
    },
  ],
};

export const periodTypeOption = [
  {
    value: 'MONTH',
    label: '单月',
  },
  {
    value: 'MULTI_MONTH',
    label: '多月',
  },
  {
    value: 'YEAR',
    label: '年',
  },
  {
    value: 'QUARTER',
    label: '季',
  },
  {
    value: 'DAY',
    label: '日',
  },
];
