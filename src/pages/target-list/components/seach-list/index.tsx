import React, { useState } from 'react';
import { Form, FormProps, Row, Col, Button, Input, Select, DatePicker } from 'antd';
import { FormInstance } from 'antd/es/form/Form';
import CommonStructure from '@/common/components/CommonStructure';
import './index.less';
import { BusinessGroupOption, BusinessLineOption, periodTypeOption } from '../../enum';

interface Props extends FormProps {
  form: FormInstance;
  reset: () => void;
  submit: () => void;
}

const PlanSearch: React.FC<Props> = (props) => {
  const [businessGroupVal, setBusinessGroupVal] = useState<string>();

  const onBusinessGroupChange = (businessGroup) => {
    setBusinessGroupVal(businessGroup);
    props.form.setFieldsValue({ businessLine: undefined });
  };

  return (
    <CommonStructure.Filter>
      {() => (
        <Form form={props.form} onFinish={props.submit} className="target-list-seach">
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label="目标方案" name="planName">
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="businessGroup" label="业务域">
                <Select
                  placeholder="请选择"
                  allowClear
                  options={BusinessGroupOption}
                  onChange={onBusinessGroupChange}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="businessLine" label="业务线">
                <Select
                  placeholder="请选择"
                  allowClear
                  options={businessGroupVal ? BusinessLineOption[businessGroupVal] : []}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label="生效月份" name="periodValue">
                <DatePicker style={{ width: '100%' }} picker="month" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item hidden name="periodType" label="周期类型" initialValue={'MONTH'}>
                <Select placeholder="请选择" options={periodTypeOption} />
              </Form.Item>
            </Col>
            <Col span={8} className="u-ta-right">
              <Form.Item>
                <Button
                  className="mr-10"
                  htmlType="button"
                  onClick={() => {
                    props.form.resetFields();
                    setBusinessGroupVal(undefined);
                    props.reset();
                  }}
                >
                  重置
                </Button>
                <Button type="primary" htmlType="submit" onClick={props.submit}>
                  查询
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      )}
    </CommonStructure.Filter>
  );
};

export default PlanSearch;
