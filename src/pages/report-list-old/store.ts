import { createStore } from '@alife/kobe-store-next';
import <PERSON><PERSON><PERSON><PERSON> from 'js-cookie';
import {
  DownloadKpiReportRequest,
  OpenAppealRequest,
  PlanAuthRequest,
  PlanStatusRequest,
} from '@/_docplus/target/types/gateway';
import { message } from 'antd';
import manageService from '@/_docplus/target/service/gateway/KpiPolicyManageGatewayService';

export interface IStore {}
export default createStore({
  store: {
    currentBusinessInfoSimpleData: [],
    dimension: [],
    productDimension: [],
    reportBussines: [],
    selectProduct: '',
    source: null,
    planSource: null,
    role: '', // direct：直营，agent：服务商，从url参数中获取
  },

  // 更新方案状态
  async updatePlanStatus(options: Partial<any>) {
    try {
      const d = await manageService.manage(options);
      return d;
    } catch (error) {
      return null;
    }
  },

  // 下载战报数据
  async downloadKpiReportData(options: Partial<DownloadKpiReportRequest>) {
    try {
      // const d = await NewKpiReportFacade.downloadKpiReportData({ request: options });
      // message.success({ content: '成功', key: 'loading' });
      // return d;
    } catch (error) {
      message.error({ content: error.errorMessage, key: 'loading' });
      return null;
    }
  },

  // 开启核算
  async openAppeal(options: Partial<OpenAppealRequest>) {
    try {
      // const d = await PlanManageFacade.openAppeal({ request: options });
      // message.success({ content: '成功', key: 'loading' });
      // return d;
    } catch (error) {
      message.error({ content: error.errorMessage, key: 'loading' });
      return null;
    }
  },

  // 更新项目方案状态
  async updateProjectStatus(request) {
    try {
      const d = await ProjectPlanSopushFacade.updateProjectStatus({ request });
      message.success({ content: '成功', key: 'loading' });
      return d;
    } catch (error) {
      message.error({ content: error.errorMessage, key: 'loading' });
      return null;
    }
  },

  /** 方案复制 */
  async copyProject(request) {
    // try {
    //   await ProjectPlanSopushFacade.copyProject({ request });
    // } catch (error) {
    //   message.error({ content: error.errorMessage, key: 'loading' });
    // }
  },

  async setup() {
    const { source, planSource = 'CENTER', role = 'direct' } = this.param || {};

    this.setStore({
      planSource,
    });
    if (source) {
      JSCookie.set('ASSESS_PLAN_PRODUCT_CODE', source);
      this.setStore({ source });
    }
    this.setStore({ role });
  },
});
