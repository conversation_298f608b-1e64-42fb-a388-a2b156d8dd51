import React, { useState } from 'react';
import { Table, Input, Button, Form, Space, Row, Modal, Popover, Select, DatePicker, message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import CommonStructure from '@/common/components/CommonStructure';
import AssessObject from '@/common/components/AssessObject';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import {
  planStateEnum,
  planStateListEnum,
  containButtonStateMap,
  planStateActionEnum,
  periodTypeListEnum,
  ActionTypeEnum,
} from '@/common/Types';
import { PERIOD_TIME_FORMAT, POLICY_TYPE_PARAMS } from '@/common/constants';
import { KpiPolicyDTO } from '@/_docplus/target/types/gateway';
import SearchList from './components/seach-list';
import store from './store';
import styles from './index.module.less';

const mockData = [
  {
    key: '1',
    name: '日看板',
    id: '1001',
    type: '表格',
    creator: '张三',
  },
  {
    key: '2',
    name: '月看板',
    id: '1002',
    type: '图表',
    creator: '李四',
  },
  {
    key: '3',
    name: '年看板',
    id: '1003',
    type: '表格',
    creator: '王五',
  },
];


const ReportList = () => {
  const [form] = Form.useForm();
  const storeIns = store.useStore();
  const [data, setData] = useState(mockData);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [createForm] = Form.useForm();
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingRecord, setEditingRecord] = useState<typeof mockData[number] | null>(null);

  const handleEdit = (record: typeof mockData[number]) => {
    setIsEditMode(true);
    setEditingRecord(record);
    createForm.setFieldsValue({
      name: record.name,
      version: '1.0', // 示例版本，可根据实际数据结构调整
      target: record.creator, // 示例执行对象，可根据实际数据结构调整
    });
    setCreateModalVisible(true);
  };

    const content =  (record) => (
      <Space>
        <Button
          size="small"
          type="link"
          onClick={() => changeStatus(record?.policyId, planStateEnum.PASS, record?.policyType)}
          // disabled={
          //   !record.canEdit || !containButtonStateMap.RELEASE.includes(record?.policyStatus)
          // }
        >
          发布
        </Button>
        <Button
          size="small"
          type="link"
          // onClick={() => changeStatus(record?.policyId, planStateEnum.INVALID, record?.policyType)}
          // disabled={!record.canEdit || !containButtonStateMap.STOP.includes(record?.policyStatus)}
        >
          停用
        </Button>
        <Button
          size="small"
          danger
          type="text"
          // onClick={() => changeStatus(record?.policyId, planStateEnum.DELETED, record?.policyType)}
          // disabled={!containButtonStateMap.DELETE.includes(record?.policyStatus)}
        >
          删除
        </Button>
      </Space>
    );

    const columns = [
    {
      title: '看板名称',
      dataIndex: 'policyName',
      key: 'policyName',
      width: 300,
      fixed: 'left',
    },
    {
      title: '看板ID',
      dataIndex: 'policyId',
      key: 'policyId',
      width: 200,
    },
    {
      title: '周期类型',
      dataIndex: 'commDurationUnit',
      key: 'commDurationUnit',
      render: (_, record) => periodTypeListEnum[record?.periodType],
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      render: (_, record) =>
        record?.gmtCreate ? moment(record.gmtCreate).format(PERIOD_TIME_FORMAT) : '--',
    },
    {
      title: '修改时间',
      dataIndex: 'gmtModified',
      key: 'gmtModified',
      render: (_, record) =>
        record?.gmtModified ? moment(record.gmtModified).format(PERIOD_TIME_FORMAT) : '--',
    },
    {
      title: '修改人',
      dataIndex: 'modifier',
      key: 'modifier',
    },
    {
      title: '状态',
      dataIndex: 'policyStatus',
      key: 'policyStatus',
      render: (_, record) => planStateListEnum[record.policyStatus],
    },
      {
      title: '操作',
      key: 'action',
      render: (_: unknown, record: typeof mockData[number]) => (
        <Space>
          {/* <Button type="link" onClick={() => window.open('https://pre-mp-next.amap.com/app/amap_web_c3_info/xy-report-builder/app-pc/render?reportId=reportId', '_blank')}>查看</Button> */}
          <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
          <Button type="link" onClick={() => window.open('https://pre-mp-next.amap.com/app/amap_web_c3_info/xy-report-builder/app-pc/builder?reportId=reportId', '_blank')}>搭建</Button>
          <Popover
            content={() => content(record)}
            overlayStyle={{
              zIndex: 9,
            }}
          >
            <Button size="small" className="u-px-0" type="link">
              更多
            </Button>
          </Popover>
        </Space>
      ),
    },
  ]?.filter((column) => column?.dataIndex);

    // 操作考核方案
  const changeStatus =  async (planId: string | number, planStatus: string, policyType?: string) => {
      Modal.confirm({
        title: `确认${planStateActionEnum[planStatus]}政策？`,
        icon: <ExclamationCircleOutlined />,
        onOk() {
          handleActionFunc(planId, planStatus, policyType);
        },
      });
  };

  const handleActionFunc = async (planId, planStatus, policyType) => {
      try {
        const res = await storeIns.updatePlanStatus({
          kpiPolicyId: planId,
          bizAction: planStatus,
          businessGroup:
            storeIns?.store?.role === 'agent' ? POLICY_TYPE_PARAMS[policyType] : 'C33_DIRECT',
        });
        if (!res?.success) {
          return;
        }
        if (planStatus === planStateEnum.PASS) {
          message.success({
            content: '政策发布成功',
            key: 'release',
            duration: 10,
          });
        } else if (planStatus === planStateEnum.INVALID) {
          message.success({ content: '停用成功', key: 'loading' });
        }

        const request = {
          ...formInfos.current,
          pageNo: pageInfo.current.current,
          pageSize: pageInfo.current.pageSize,
        };
        setTimeout(() => {
          listPlan(request);
        }, 1500);
      } catch (error) {
        message.error({ content: error.errorMessage, key: 'loading' });
      }
    };

  const onSearch = () => {
    const { name, id } = form.getFieldsValue();
    let filtered = mockData;
    if (name) {
      filtered = filtered.filter(item => item.name.includes(name));
    }
    if (id) {
      filtered = filtered.filter(item => item.id.includes(id));
    }
    setData(filtered);
  };

  const onReset = () => {
    form.resetFields();
    setData(mockData);
  };

  // 跳转详情页
  const handleCreate = (type = ActionTypeEnum.ADD, planId = '') => {
    const prefix =
      window.APP?.isFromKbServ === 'true' ? '/sale-pc/mp-kpi-assess-plan' : '/mp-kpi-assess-plan';
    if (type === ActionTypeEnum.ADD) {
      window.open(`${prefix}/report-detail?type=${type}&role=${storeIns?.store?.role}`);
    } else {
      window.open(`${prefix}/report-detail?type=${type}&planId=${planId}&role=${storeIns?.store?.role}`);
    }
  };

  const handleCreateOk = async () => {
    try {
      const values = await createForm.validateFields();
      // 这里可以添加新建或编辑逻辑
      if (isEditMode) {
        // 编辑逻辑
        console.log('编辑看板:', values, editingRecord);
      } else {
        // 新建逻辑
        console.log('新建看板:', values);
      }
      setCreateModalVisible(false);
      setConfirmModalVisible(true);
      createForm.resetFields();
    } catch (e) {}
  };

  const handleCreateCancel = () => {
    setCreateModalVisible(false);
    setIsEditMode(false);
    setEditingRecord(null);
    createForm.resetFields();
  };

  const handleConfirmOk = () => {
    setConfirmModalVisible(false);
    // 跳转到编辑页逻辑可在此添加
    window.open('https://pre-mp-next.amap.com/app/amap_web_c3_info/xy-report-builder/app-pc/builder', '_blank');
  };

  const handleConfirmCancel = () => {
    setConfirmModalVisible(false);
  };


  return (
    <CommonStructure>
    <CommonStructure.Header>
      <Row align="bottom">
        <h2>看板列表</h2>
      </Row>
    </CommonStructure.Header>

      <SearchList />


    <CommonStructure.Section
      extra={
        <Button type="primary" onClick={handleCreate}>
            新建看板
          </Button>
      }
    >
      <Table columns={columns} dataSource={data} rowKey="key" />
    </CommonStructure.Section>

    <Modal
        title={isEditMode ? "修改看板" : "新建看板"}
        open={createModalVisible}
        onOk={handleCreateOk}
        onCancel={handleCreateCancel}
        width={800}
        destroyOnClose
      >
        <Form form={createForm} layout="vertical">
          <Form.Item
            label="看板名称"
            name="name"
            rules={[{ required: true, message: '请输入看板名称' }]}
          >
            <Input placeholder="请输入看板名称" maxLength={100}/>
          </Form.Item>
           <Form.Item
            name="periodType"
            label="周期类型"
            rules={[{ required: true }]}
          >
            <Select placeholder="请选择">
              <Select.Option value="MONTH" key="MONTH">
                单月
              </Select.Option>
              {/* <Select.Option value="MULTI_MONTH" key="MULTI_MONTH">
                多月
              </Select.Option> */}
            </Select>
          </Form.Item>
          <Form.Item
            label="看板版本"
            name="periodValue"
            rules={[
              {
                required: true,
                message: '请选择目标生效月份',
              },
            ]}
          >
            <DatePicker picker="month" />
          </Form.Item>

          <AssessObject
            tempUrl='https://a.amap.com/smallBiz/static/agent-xy/上传考核对象模板.xlsx'
            extraOptions={[{ label: '圈选人员', value: 'RULE' }]}
            periodType='MONTH'
            periodValue={moment('2025-06-01')}
            periodValueMulti={[]}
            kpiPolicy={null}
            disabled={false}
          />
        </Form>
    </Modal>
    <Modal
      open={confirmModalVisible}
      onOk={handleConfirmOk}
      onCancel={handleConfirmCancel}
      okText="进入编辑"
      cancelText="暂不进入"
      title={null}
    >
      是否进入看板编辑
    </Modal>
  </CommonStructure>
  );
};

export default ReportList;
