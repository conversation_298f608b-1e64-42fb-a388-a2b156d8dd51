import React from 'react';
import { Form, FormProps, Space, Row, Col, Button, Input, Select, DatePicker } from 'antd';
import { FormInstance } from 'antd/es/form/Form';
import moment from 'moment';
import classNames from 'classnames';
import { planStateListMap } from '@/common/Types';
import CommonStructure from '@/common/components/CommonStructure';
import { BUSINESS_DOMAIN } from './constant';

import './index.less';

const { Option } = Select;
interface Props extends FormProps {
  form: FormInstance;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  handleSearch: (values) => void;
  storeIns?: any;
}

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const PlanSearch: React.FC<Props> = ({ form, handleSearch, storeIns }) => (
  <CommonStructure.Filter>
    {() => (
      <Form
        form={form}
        {...layout}
        className="plan-list-seach"
        style={{ paddingBottom: 24 }}
        onFinish={(forms) => {
          const fm = {
            kpiPolicyName: forms?.kpiPolicyName?.trim() || undefined,
            kpiPolicyId: forms?.kpiPolicyId?.trim() || undefined,
            periodType: forms?.periodType || undefined,
            periodValue: forms?.periodValue
              ? moment(forms?.periodValue).format('YYYYMM')
              : undefined,
            kpiPolicyStatus: forms?.kpiPolicyStatus || undefined,
            businessGroup: forms?.businessGroup?.join(',') || undefined,
          };
          handleSearch(fm);
        }}
      >
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item label="看板名称" name="kpiPolicyName">
              <Input placeholder="看板名称" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="看板ID" name="kpiPolicyId">
              <Input placeholder="看板ID" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="periodType" label="周期类型">
              <Select placeholder="请选择" allowClear>
                <Option value="MONTH">单月</Option>
                {/* <Option value="MULTI_MONTH">多月</Option> */}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="看板版本" name="periodValue">
              <DatePicker style={{ width: '100%' }} picker="month" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="状态" name="kpiPolicyStatus">
              <Select placeholder="请选择" allowClear>
                {planStateListMap?.map((it) => (
                  <Option value={it.value} key={`plan-status-${it.value}`}>
                    {it.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24} className={classNames('u-ta-right')}>
            <Space>
              <Button
                htmlType="button"
                onClick={() => {
                  form.resetFields();
                  handleSearch({});
                }}
              >
                重置
              </Button>
              <Button type="primary" htmlType="submit">
                查询
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    )}
  </CommonStructure.Filter>
);

export default PlanSearch;
