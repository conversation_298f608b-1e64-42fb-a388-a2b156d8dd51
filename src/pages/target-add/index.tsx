import React, { useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Form, Select, Input, DatePicker, Button, Space, message, Spin } from 'antd';
import moment from 'moment';
import { parse } from 'query-string';
import TargetPlanOperateGatewayService from '@/_docplus/target/service/gateway/TargetPlanOperateGatewayService';
import TargetPlanQueryGatewayService from '@/_docplus/target/service/gateway/TargetPlanQueryGatewayService';
import CommonStructure from '@/common/components/CommonStructure';
import { CommonBlockWrap } from '@/common/components/CommonBlockWrap';
import AssessObject from '@/common/components/AssessObject';
import { BusinessGroupOption, BusinessLineOption } from '../target-list/enum';
import QuotaSelect from './components/QuotaSelect';
import './index.less';

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 11 },
};

const TargetAdd = () => {
  const history = useHistory();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submiting, setSubmiting] = useState(false);

  // 目标方案id
  const planId = useMemo(() => {
    const urlParam = parse(window.location.search);
    return urlParam.planId;
  }, []);

  useEffect(() => {
    if (planId) {
      getTargetPlanDetail(planId).then((res) => {
        !!res && initFormValue(res);
      });
    }
  }, []);

  /**
   * 获取目标方案详情
   * @param planId
   * @returns
   */
  const getTargetPlanDetail = async (planId) => {
    try {
      setLoading(true);
      const detail = await TargetPlanQueryGatewayService.queryTargetPlanModel({
        planId,
        conditions: ['TARGET_PLAN_INDICCATOR'],
      });
      setLoading(false);
      if (detail.success) {
        return detail.data;
      } else {
        return null;
      }
    } catch (error) {
      console.log('catch--------', error);
      setLoading(false);
    }
  };

  /**
   * 变更业务域时，清楚业务线和指标
   */
  const handleBusinessGroupChange = () => {
    form.setFieldsValue({
      businessLine: null,
      targetIndicatorConfigRequestList: [],
    });
  };

  /**
   * 初始化表单数据
   * @param values
   */
  const initFormValue = (values) => {
    const { targetPlanInfo, indicatorInfos = [] } = values;

    form.setFieldsValue({
      businessGroup: targetPlanInfo.businessGroup,
      businessLine: targetPlanInfo.businessLine,
      periodType: targetPlanInfo.periodType,
      periodValue: moment(targetPlanInfo.periodValue),
      planName: targetPlanInfo.planName,

      targetIndicatorConfigRequestList: indicatorInfos.map((item) => ({
        ...item,
        name: item.indicatorName,
      })),
      objectOssKey: targetPlanInfo.ossKey && [
        {
          uid: targetPlanInfo.ossKey,
          // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
          name: targetPlanInfo.ossKey.substring(targetPlanInfo.ossKey?.lastIndexOf('/') + 1),
          ossFileName: targetPlanInfo.ossKey,
          url: targetPlanInfo.ossUrl,
          status: 'done',
        },
      ],
    });
  };

  /**
   * 下载模版
   */
  const downLoadTemplate = async () => {
    const values = form.getFieldsValue();
    if (
      !values.businessGroup ||
      !values.businessLine ||
      !values.targetIndicatorConfigRequestList?.length
    ) {
      message.error('请先完成基本信息');
      return;
    }

    TargetPlanOperateGatewayService.downloadTargetPlanTemplate({
      businessGroup: values.businessGroup,
      businessLine: values.businessLine,
      planName: values.planName,
      indicatorIds: values.targetIndicatorConfigRequestList.map((item) => item.indicatorId),
    })
      .then((res) => {
        if (res.success) {
          window.open(res.data.downloadUrl);
        } else {
          message.error(res.resultMessage);
        }
      })
      .catch((e) => {
        console.log('catch--------', e);
      });
  };

  /**
   * 提交表单
   * @param values
   */
  const handleSubmit = (values) => {
    setSubmiting(true);
    const data = {
      ...values,
      periodValue: moment(values?.periodValue).format('YYYYMM'),
      ossKey: values.objectOssKey?.[0]?.ossFileName,
      targetIndicatorConfigRequestList: [
        {
          targetIndicators: values.targetIndicatorConfigRequestList,
        },
      ],
      planId,
    };

    const { addTargetPlan, editTargetPlan } = TargetPlanOperateGatewayService;

    // 有 planId 走修改，否则走新增
    (planId ? editTargetPlan : addTargetPlan)(data)
      .then((res) => {
        setSubmiting(false);
        if (res.success) {
          message.success('提交成功');
          setTimeout(() => {
            history.push('/target-list');
          }, 500);
        }
      })
      .catch((e) => {
        setSubmiting(false);
      });
  };

  return (
    <CommonStructure>
      <Spin spinning={loading}>
        <Form
          {...layout}
          form={form}
          initialValues={{
            periodValue: '',
            periodType: 'MONTH',
          }}
          onFinish={handleSubmit}
          className="target-add-form"
        >
          <CommonStructure.Section title="基本信息">
            <CommonBlockWrap>
              <Form.Item
                label="业务域"
                name="businessGroup"
                rules={[
                  {
                    required: true,
                    message: '请选择业务域',
                  },
                ]}
              >
                <Select
                  options={BusinessGroupOption}
                  onChange={handleBusinessGroupChange}
                  disabled={!!planId}
                />
              </Form.Item>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                  prevValues.businessGroup !== curValues.businessGroup
                }
              >
                {(form) => {
                  const businessGroup = form.getFieldValue('businessGroup');
                  return (
                    <Form.Item
                      label="业务线"
                      name="businessLine"
                      rules={[
                        {
                          required: true,
                          message: '请选择业务线',
                        },
                      ]}
                    >
                      <Select options={BusinessLineOption[businessGroup]} disabled={!!planId} />
                    </Form.Item>
                  );
                }}
              </Form.Item>

              <Form.Item
                label="方案名称"
                name="planName"
                rules={[
                  {
                    required: true,
                    message: '请输入方案名称',
                  },
                ]}
              >
                <Input maxLength={50} />
              </Form.Item>
              <Form.Item
                hidden
                name="periodType"
                label={'绩效周期类型'}
                rules={[{ required: true }]}
              >
                <Select placeholder="请选择">
                  <Select.Option value="MONTH" key="MONTH">
                    单月
                  </Select.Option>
                  <Select.Option value="MULTI_MONTH" key="MULTI_MONTH">
                    多月
                  </Select.Option>
                </Select>
              </Form.Item>
              <Form.Item
                label="生效月份"
                name="periodValue"
                rules={[
                  {
                    required: true,
                    message: '请选择目标生效月份',
                  },
                ]}
              >
                <DatePicker picker="month" disabled={!!planId} />
              </Form.Item>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                  prevValues.businessGroup !== curValues.businessGroup
                }
              >
                {(form) => {
                  const businessGroup = form.getFieldValue('businessGroup');
                  return (
                    <Form.Item
                      label="设置指标"
                      name="targetIndicatorConfigRequestList"
                      rules={[
                        {
                          required: true,
                          message: '请选择指标',
                        },
                      ]}
                    >
                      <QuotaSelect businessGroup={businessGroup} />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </CommonBlockWrap>
          </CommonStructure.Section>

          <CommonStructure.Section title="执行对象">
            <CommonBlockWrap>
              <AssessObject
                tempUrl={downLoadTemplate}
                tempRemark="变更基本信息后，请重新下载模版"
              />
            </CommonBlockWrap>
          </CommonStructure.Section>

          <div className="tail-style">
            <Space>
              <Button onClick={() => history.push('/target-list')}>返回列表</Button>
              <Button htmlType="submit" type="primary" loading={submiting}>
                提交
              </Button>
            </Space>
          </div>
        </Form>
      </Spin>
    </CommonStructure>
  );
};

export default TargetAdd;
