import React, { useMemo, useState } from 'react';
import { Modal, Button, Transfer, Table, Switch } from 'antd';
import QuoSelectModal from '@/common/components/QuoSelectModal';
import { IndicatorDTO } from '@/_docplus/target/types/gateway';

interface IProps {
  value?: IndicatorDTO[];
  onChange?: Function;
  businessGroup: string;
}

const QuotaSelect = (props: IProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const tableColumns = [
    {
      title: '指标ID',
      dataIndex: 'indicatorId',
      key: 'indicatorId',
    },
    {
      title: '指标名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '操作',
      render: (text, record) => {
        return (
          <a type="text" onClick={() => handleDelete(record)}>
            删除
          </a>
        );
      },
    },
  ];

  const handleChange = (list) => {
    props.onChange(list);
  };

  const handleDelete = (record) => {
    const newList = props.value.filter((item) => item.indicatorId !== record.indicatorId);
    props.onChange(newList);
  };

  return (
    <>
      {props.value?.length > 0 && (
        <Table
          size="small"
          columns={tableColumns}
          dataSource={props.value}
          pagination={false}
          style={{ marginBottom: 20 }}
        />
      )}
      {isModalOpen && (
        <QuoSelectModal
          onChange={handleChange}
          value={props.value}
          onClose={() => setIsModalOpen(false)}
          businessGroup={props.businessGroup}
        />
      )}
      <Button type="primary" onClick={() => setIsModalOpen(true)}>
        选取指标
      </Button>
    </>
  );
};

export default QuotaSelect;
