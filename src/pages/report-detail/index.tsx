import React from 'react';
import { getInitParam } from '@/common/configStore';
import { CommonBlockWrap } from '@/common/components/CommonBlockWrap';
import store from './store';
import PlanDetail from './components';

import './index.less';

const Index: React.FC = () => {
  const storeIns = store.useStore().store;

  return (
    <div className="detail-warp">
      <h1>{storeIns.selectProductName}详情</h1>
      <CommonBlockWrap>
        <PlanDetail />
      </CommonBlockWrap>
    </div>
  );
};

export default () => (
  <store.Container
    component={Index}
    initParam={() => ({
      ...getInitParam(),
    })}
  />
);
