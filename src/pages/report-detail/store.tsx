/* eslint-disable max-len */
import { createStore } from '@alife/kobe-store-next';
import J<PERSON>ook<PERSON> from 'js-cookie';
import { message } from 'antd';
import { ActionTypeEnum, EXP_WIN_DEFAULT_CONFIG_INTERFACE, tracertInfo } from '@/common/Types';
import service from '@/_docplus/target/service/gateway/KpiPolicyQueryGatewayService';
import manageService from '@/_docplus/target/service/gateway/KpiPolicyManageGatewayService';
import {
  DEFAULT_INDICATOR_LIST_PAGE_SIZE,
} from '@/common/constants';
import { generateRandomString } from '@/common/utils';
import { POLICY_TYPE_PARAMS } from '@/common/constants';

export default createStore({
  store: {
    editType: ActionTypeEnum.ADD,
    planId: '',
    role: '', // direct：直营，agent：服务商，从url参数中获取
    /** 为圈选达标组件创建的临时planId */
    tempPlanId: '',
    copyBussiness: [],
    currentBusinessInfoSimpleData: [],
    dimension: [],
    business: ['ENCOURAGE', 'TO_HOME', 'BL', 'BATTALION'],
    selectProduct: 'KPI',
    selectProductName: '看板',
    productDimension: [],
    /** 产品域支持多层级 */
    productMoreLevel: false,
    ruleListData: [], // 考核模块列表
    ruleModuleIndic: [], // 考核模块指标
    ruleItemIndic: [], // 考核项指标
    instanceTemplates: [], // 激励赛奖励方案列表
    planDetails: null,
    circleTagsList: [], // 岗位圈人标签列表
    circleOrgTagsList: [], // 组织圈人标签列表
    levelTypes: [], // 指标池
    indicatorsList: [], // 字段筛选指标
    strategyList: [], // 策略模版数据
    tracertInfo: {
      // 产品域
      domain: JSCookie.get('ASSESS_PLAN_PRODUCT_CODE'),
      // 业务域
      businessLine: null,
      // 方案ID
      planId: null,
      // 其它所有信息
      othersInfo: {
        // 方案名称
        planName: null,
        // 方案状态
        planStatus: 'WAIT_SUBMIT',
        // 考核周期:
        planCycleStart: null,
        planCycleEnd: null,
      },
      // 操作类型（新建/复制新建/编辑）
      operateType: 'add',
      // 打开页面的时间戳
      pageStartTime: Date.parse(new Date() as any),
    } as tracertInfo,
    /** 日达标因子指标列表 */
    dayFactorOptions: [],
    expWinDefaultData: {
      EXP_WIN_BIZ_TYPE: null,
      EXP_WIN_INDICATOR_DATASETS: null,
      EXP_WIN_DETAIL_DATASETS: null,
      EXP_WIN_DETAIL_EXTRA: null,
      PPEandProd: null,
      EXP_WIN_DEFAULT_CONFIG: null,
      DETAIL_LIST: null,
    } as EXP_WIN_DEFAULT_CONFIG_INTERFACE,
    businessGroup: '', // 业务域，C33和C31服务商登录是可以切换
  },

  // 查询指标
  async queryIndicators(businessGroup) {
    try {
      const res = await service.queryIndicatorList({
        businessGroup,
        pageSize: DEFAULT_INDICATOR_LIST_PAGE_SIZE,
        pageStart: 0,
      });

      // message.success({ content: '成功', key: 'loading' });
      const arr = res.data?.map((v: any) => ({
        label: v.indicatorName,
        value: v.indicatorId,
        type: 'Indicator',
        indicatorType: v.indicatorType,
        classificationLabel: v?.label,
      }));

      return {
        currentBusinessInfoSimpleData: arr || [],
      };
    } catch (error) {
      return {
        currentBusinessInfoSimpleData: [],
      };
    }
  },

  /** 获取唯一ID */
  async generateUniqueId(tip = true) {
    try {
      const d = await manageService.generateItemId();
      if (tip) {
        message.success({ content: '成功', key: 'loading' });
      }
      return d?.data || '';
    } catch (error) {
      // 如果接口挂了，随机生成一个 19 位的字符串
      return generateRandomString(19);
    }
  },

  // 查询指标详情
  async queryPlanDetail(planId, editType, other = {}) {
    try {
      let d: any = {};
      if (editType === ActionTypeEnum.COPY) {
        d = await manageService.copy({ kpiPolicyId: planId, ...other });
      } else {
        d = await service.queryPolicyDetail({ kpiPolicyId: planId, bizType: 'DISPLAY_CONFIG' });
      }
      const result = d?.data;
      return {
        planDetails: result,
        ruleListData: result?.modules,
      };
    } catch (error) {
      console.log('queryPlanDetail error', error);
      return {};
    }
  },

  // 设置当前tracertInfo的内容
  setTracertInfo(params: Partial<tracertInfo>) {
    this.setStore({
      tracertInfo: {
        ...this.store.tracertInfo,
        ...params,
      },
    });
  },
  /**
   * 切换业务域
   * @param businessGroup
   */
  async handleBusinessGroupChange(businessGroup) {
    this.setStore({
      businessGroup,
    });
    this.queryIndicators(businessGroup).then((indicators) => {
      this.setStore({
        ...indicators,
      });
    });
  },

  /**
   * 启动
   */
  async setup() {
    const { type = ActionTypeEnum.SEE, planId = '', role = 'direct' } = this.param || {};
    let businessGroup = 'C33_DIRECT';

    this.setStore({
      editType: type,
      planId,
      role,
      businessGroup,
      tracertInfo: {
        ...this.store.tracertInfo,
        operateType: type,
      },
    });

    if (type !== ActionTypeEnum.ADD && planId) {
      const detail = await this.queryPlanDetail(planId, type);
      const { policyType } = detail?.planDetails?.kpiPolicy || {};

      businessGroup = POLICY_TYPE_PARAMS[policyType];

      const indicators = await this.queryIndicators(businessGroup);
      this.setStore({
        ...(indicators as any),
        ...(detail as any),
        businessGroup,
      });
    } else {
      const indicators = await this.queryIndicators(businessGroup);
      this.setStore({
        ...indicators,
      });
    }
  },
});
