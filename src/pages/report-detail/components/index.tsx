/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { useHistory } from 'react-router-dom';
import { useBoolean } from 'ahooks';
import {
  Form,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Button,
  message,
  Anchor,
  Space,
  Checkbox,
  Spin,
  Modal,
} from 'antd';
import moment, { Moment } from 'moment';
import { isEqual } from 'lodash';
import { CalItemInfoDTO, KpiPolicyEditGatewayRequest } from '@/_docplus/target/types/gateway';
import { planStateEnum, ActionTypeEnum } from '@/common/Types';
import {
  handleRuleDataToForm,
  debounce,
  monthListToStr,
  strToMonthList,
  lazyValidate,
} from '@/common/utils';
import manageService from '@/_docplus/target/service/gateway/KpiPolicyManageGatewayService';
import CommonStructure from '@/common/components/CommonStructure';
import { CommonBlockWrap } from '@/common/components/CommonBlockWrap';
import store from '../store';
import { DetailAnchorEnum } from '../helper';
import { BusinessGroupEnum } from '@/common/Types';
import AssessObject from '@/common/components/AssessObject';
import { getDefaultSchema, transFormItemToModule } from './helper';
import PlanSelect from './plan-select';
import { savePlan } from '../service';
import './index.less';

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};

const { Option } = Select;
const { TextArea } = Input;
const { Link } = Anchor;

const Index: React.FC = () => {
  const [form] = Form.useForm();
  const history = useHistory();
  const periodTypeWatch = Form.useWatch('periodType', form);
  const periodValueWatch: Moment | null = Form.useWatch('periodValue', form);
  const periodValueMultiWatch: Moment[] | null = Form.useWatch('periodValueMulti', form);
  const [businessEnum, setBusinessEnum] = useState<string>('');

  const latestPeriodTypeAndValueRef = useRef({
    periodTypeWatch,
    periodValueWatch,
    periodValueMultiWatch,
  });
  latestPeriodTypeAndValueRef.current = {
    periodTypeWatch,
    periodValueWatch,
    periodValueMultiWatch,
  };

  const indexStore = store.useStore();

  const {
    planId,
    editType,
    planDetails,
    businessGroup,
  } = indexStore.store;

  const [loading, { setFalse: setLoaded }] = useBoolean(editType !== ActionTypeEnum.ADD);

  const [isRequestLoading, setIsRequestLoading] = useState(false);
  const createPlanId = useRef();

  const jumpHome = () => {
    history.push(`/report-list`);
  };

  const onFinishFailed = (values) => {
    console.log('onFinishFailed', values.values);
    message.error({ content: '请检查表单校验报错' });
  };

  const onFinish = async (value, withPublish?: boolean) => {
    setIsRequestLoading(true);

    const periodType = value?.periodType;

    const request: Partial<KpiPolicyEditGatewayRequest> = {
      businessGroup: value?.businessGroup,
      kpiPolicyName: value.kpiPolicyName,
      kpiPolicyDesc: value.kpiPolicyDesc,
      preDependencyPolicyList: value?.preDependencyPolicyList,
      periodType,
      objectType: [value.objectType],
      objectOssKey: value.objectOssKey?.[0]?.ossFileName,
      displayitemCodes: [],
      displayConfigInfo: getDefaultSchema(),
    };

    if (value?.objectType === 'RULE') {
      // 圈选人员
      request.objectRuleList = value?.objectRuleList;
      request.objectWhiteList = value?.objectWhiteList?.map((item) => {
        if (typeof item === 'string') {
          const res = planDetails?.kpiPolicy?.objectWhiteList?.find((w) => w.id === item);
          return res;
        } else {
          return {
            id: item.value,
            name: item.label,
          };
        }
      });
      request.objectBlackList = value?.objectBlackList?.map((item) => {
        if (typeof item === 'string') {
          const res = planDetails?.kpiPolicy?.objectBlackList?.find((w) => w.id === item);
          return res;
        } else {
          return {
            id: item.value,
            name: item.label,
          };
        }
      });
    }

    if (periodType === 'MONTH') {
      request.periodValue = moment(value?.periodValue).format('YYYYMM');
    }

    // 多月
    if (periodType === 'MULTI_MONTH') {
      request.periodValue = monthListToStr([
        moment(value?.periodValueMulti?.[0]).format('YYYYMM'),
        moment(value?.periodValueMulti?.[1]).format('YYYYMM'),
      ])?.join(',');
    }

    if (editType === ActionTypeEnum.EDIT) {
      // 编辑时带ID
      createPlanId.current = planId as any;
      request.displayitemCodes = planDetails?.kpiPolicy?.displayitemCodes || [];
      request.displayConfigInfo = planDetails?.kpiPolicy?.displayConfigInfo || "";
    }

    try {
      request.kpiPolicyId = createPlanId.current;
      const res = await savePlan(request);
      if (res?.data && !createPlanId.current) {
        createPlanId.current = res?.data as any;
      }
      if (withPublish && createPlanId.current) {
        await manageService.manage({
          bizAction: planStateEnum.PASS,
          kpiPolicyId: createPlanId.current,
          businessGroup: request?.businessGroup,
        });
        message.success({ content: '看板发布成功', key: 'release', duration: 10 });
      } else {
        message.success({ content: '保存成功' });
      }
      setIsRequestLoading(false);
      jumpHome();
    } catch (e) {
      setIsRequestLoading(false);
      message.error(e?.message || '保存失败');
    }
  };

  const isLook = useMemo(() => {
    const showTag = [ActionTypeEnum.ADD, ActionTypeEnum.COPY, ActionTypeEnum.EDIT];
    return !showTag.includes(editType);
  }, [editType]);

  useEffect(() => {
    form.setFieldsValue({
      businessGroup: 'C33_DIRECT',
      periodType: 'MONTH',
      kpiItem: {
        modules: [],
        ruleGroup: {
          expression: {
            // 公式
            expression: null,
          },
          // indicatorRules: [{}], // 规则组，这里使用initalValue的[{}]
          arrayExpressionObj: null, // 矩阵
          showType: 'EXPRESSION',
        },
        resultFormat: {
          formatType: 'NUMBER', // 数值类型
          precision: 2, // 数据精度
        },
      },
      commItem: {
        modules: [],
        ruleGroup: {
          expression: {
            expression: null,
          },
          // arrayExpressionObj: null,
          showType: 'EXPRESSION',
        },
        resultFormat: {
          formatType: 'NUMBER', // 数值类型
          precision: 2, // 数据精度
        },
      },
    });
  }, []);

  useEffect(() => {
    if (planDetails) {
      // JSCookie.set('ASSESS_PLAN_PRODUCT_CODE', planDetails?.businessDomain);
      // 默认把考核模块、考核项 拆分填充到公式列表
      // defauleSetIndic(planDetails?.modules);
      const kpiItem = {
        ruleGroup: handleRuleDataToForm(planDetails.kpiItem?.ruleGroupInfo),
        resultFormat: planDetails.kpiItem?.resultFormat,
        modules: transFormItemToModule(planDetails.kpiItem?.calItems),
      };

      const commItem = {
        ruleGroup: handleRuleDataToForm(planDetails.commItem?.ruleGroupInfo),
        resultFormat: planDetails.commItem?.resultFormat,
        modules: transFormItemToModule(planDetails.commItem?.calItems),
      };

      const kpiPolicy = planDetails?.kpiPolicy || {};
      const objectOssKey = kpiPolicy.objectOssKey && [
        {
          uid: kpiPolicy.objectOssKey,
          // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
          name: kpiPolicy.objectOssKey.substring(kpiPolicy.objectOssKey?.lastIndexOf('/') + 1),
          ossFileName: kpiPolicy.objectOssKey,
          url: kpiPolicy.objectOssUrl,
          status: 'done',
        },
      ];

      const periodType = kpiPolicy?.commDurationUnit;

      const formd: any = {
        businessGroup: kpiPolicy.policyType,
        kpiPolicyName: kpiPolicy.policyName,
        kpiPolicyDesc: kpiPolicy.policyDesc,
        preDependencyPolicyList:
          kpiPolicy?.preDependencyPolicyList?.map((item) => item.policyId) || [],
        periodType,
        objectOssKey,
        objectType: kpiPolicy?.objectType?.[0] || 'CUSTOMIZE',
        objectRuleList: kpiPolicy?.objectRuleList,
        objectWhiteList: kpiPolicy?.objectWhiteList?.map((item) => item.id),
        objectBlackList: kpiPolicy?.objectBlackList?.map((item) => item.id),
        kpiItem,
        commItem,
      };

      if (periodType === 'MONTH') {
        formd.periodValue = moment(kpiPolicy?.periodValue);
      }

      // 多月
      if (periodType === 'MULTI_MONTH') {
        formd.periodValueMulti = strToMonthList(kpiPolicy?.periodValue);
      }

      form.setFieldsValue(formd);
      form.validateFields();

      // useWatch 存在时序问题，会导致排名指标回填不对，见这个 bug：
      // https://aone.alibaba-inc.com/v2/project/1185953/bug/55591067
      // 这里简单处理下：
      // 1. 前提：「看板周期类型」和「看板周期」字段永远透出，否则 antd 的 form 永远不会回填，导致 useWatch 永远没有值
      // 2. 这里增加一个 loaded 状态，在 loaded 变为 true 时，才挂载公式编辑器组件
      // 3. 如果是需要回填的编辑/查看操作，在这里轮询 useWatch 的结果，直到结果显示回填成功才挂载
      const checkFormIsReady = () => {
        const { periodTypeWatch, periodValueWatch, periodValueMultiWatch } =
          latestPeriodTypeAndValueRef.current;
        return !!periodTypeWatch && !!(periodValueWatch || periodValueMultiWatch);
      };
      lazyValidate(checkFormIsReady).then(setLoaded);
    }
  }, [planDetails]);

  const onValuesChange = (formChange) => {
    // console.log('%c Line:1119 🍺 formChange', 'color:#465975', formChange);
  };

  const handlePeriodChange = async () => {
    form.setFieldsValue({
      preDependencyPolicyList: [],
    });

    form.validateFields();
  };

  // 圈选途径规则
  const getExtraOptions = () => {
    // 如果不是代理角色
    return [{ label: '圈选人员', value: 'RULE' }];
  };

  const toBuilder = () => {
    window.open(`https://pre-mp-next.amap.com/app/amap_web_c3_info/xy-report-builder/app-pc/builder?kpiPolicyId=${planId}`, '_blank');
  }

  return (
    <div className="detail-add-warp">
      <Form
        form={form}
        {...layout}
        className="form-warp"
        onFinish={(forms) => {
          onFinish({ ...forms }, true);
        }}
        onFinishFailed={onFinishFailed}
        onValuesChange={onValuesChange}
        scrollToFirstError
        disabled={editType === ActionTypeEnum.SEE}
      >
        <CommonStructure.Section
          id={DetailAnchorEnum.BaseInfo}
          title="基础信息"
          className="detail-add-base"
        >
          <CommonBlockWrap>

            <Form.Item hidden label="业务域" name="businessGroup" rules={[{ required: true }]}>
              <Select
                placeholder="请选择"
                allowClear
                options={BusinessGroupEnum}
                onChange={(val) => {
                  setBusinessEnum(val);
                  indexStore.handleBusinessGroupChange(val);
                }}
                disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT}
              />
            </Form.Item>

            <Form.Item name="periodType" label={'周期类型'} rules={[{ required: true }]}>
              <Select
                placeholder="请选择"
                disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT}
                onChange={handlePeriodChange}
              >
                <Option value="MONTH" key="MONTH">
                  单月
                </Option>
                {/* <Option value="MULTI_MONTH" key="MULTI_MONTH">
                  多月
                </Option> */}
              </Select>
            </Form.Item>

            {periodTypeWatch === 'MONTH' && (
              <Form.Item
                label="看板周期"
                name="periodValue"
                rules={[
                  {
                    required: true,
                    message: '请选择看板周期',
                  },
                ]}
              >
                <DatePicker
                  picker="month"
                  onChange={handlePeriodChange}
                  disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT}
                />
              </Form.Item>
            )}

            {periodTypeWatch === 'MULTI_MONTH' && (
              <Form.Item
                label="看板周期"
                name="periodValueMulti"
                rules={[
                  {
                    required: true,
                    message: '请选择看板周期',
                  },
                ]}
              >
                <DatePicker.RangePicker
                  picker="month"
                  onChange={handlePeriodChange}
                  disabled={editType === ActionTypeEnum.SEE || editType === ActionTypeEnum.EDIT}
                />
              </Form.Item>
            )}
            {/* 直营时才展示政策依赖 */}
     
            <Form.Item name="preDependencyPolicyList" label="政策依赖">
              <PlanSelect
                initialItems={planDetails?.kpiPolicy?.preDependencyPolicyList || []}
                businessGroup="C33_DIRECT"
                periodType={periodTypeWatch}
                periodValue={periodValueWatch}
                periodValueMulti={periodValueMultiWatch}
              />
            </Form.Item>
      
            <Form.Item name="kpiPolicyName" label="看板名称" rules={[{ required: true }]}>
              <Input placeholder="请输入" />
            </Form.Item>

            <Form.Item name="kpiPolicyDesc" label="说明">
              <TextArea
                placeholder="请输入内容"
                style={{ height: 120 }}
                showCount
                maxLength={4000}
              />
            </Form.Item>
          </CommonBlockWrap>
        </CommonStructure.Section>

        <CommonStructure.Section
          id={DetailAnchorEnum.Object}
          title="执行对象"
          className="detail-add-object"
        >
          <CommonBlockWrap>
            {/* 如果是服务商，执行对象除了自定义，还要加一个【渠道业务部】 */}
            {/* businessEnum区分服务商旺铺跟商户通 */}
            <AssessObject
              tempUrl='https://a.amap.com/smallBiz/static/agent-xy/上传考核对象模板.xlsx'
              extraOptions={getExtraOptions()}
              periodType={periodTypeWatch}
              periodValue={periodValueWatch}
              periodValueMulti={periodValueMultiWatch}
              kpiPolicy={planDetails?.kpiPolicy}
              disabled={editType === ActionTypeEnum.SEE}
            />
          </CommonBlockWrap>
        </CommonStructure.Section>

        {loading ? (
          <Spin />
        ) : (
          <>
            {!isLook && (
              <div className="detail-done-warp">
                <Space>
                  <Form.Item wrapperCol={{ ...layout.wrapperCol, offset: 8 }}>
                    {
                      editType === ActionTypeEnum.EDIT && (
                        <Button type="primary" loading={isRequestLoading} onClick={toBuilder}>
                          去搭建
                        </Button>
                      )
                    }
                  </Form.Item>
                  <Form.Item wrapperCol={{ ...layout.wrapperCol, offset: 8 }}>
                    <Button type="default" htmlType="button" onClick={jumpHome}>
                      返回列表
                    </Button>
                  </Form.Item>
                  <Form.Item wrapperCol={{ ...layout.wrapperCol, offset: 8 }}>
                    <Button
                      type="primary"
                      htmlType="button"
                      loading={isRequestLoading}
                      onClick={() => {
                        form
                          .validateFields()
                          .then(() => {
                            debounce(onFinish(form.getFieldsValue(), false), 2000);
                          })
                          .catch(() => {
                            message.error({ content: '请检查表单校验报错' });
                          });
                      }}
                    >
                      保存并退出
                    </Button>
                  </Form.Item>
                </Space>
              </div>
            )}
          </>
        )}
      </Form>
    </div>
  );
};

// eslint-disable-next-line no-restricted-syntax
export default Index;
