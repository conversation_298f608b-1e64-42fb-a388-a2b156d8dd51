import moment from 'moment';

/** 处理考核周期提交数据 */
export const formatPeriodFormToData = (time, periodType: string, isStart = false) => {
  if (!time) return null;
  if (periodType === 'MONTH') {
    if (isStart) {
      return time.format('YYYY-MM-01');
    }
    return moment(time).add(1, 'months').format('YYYY-MM-01');
  } else if (periodType === 'QUARTER') {
    if (isStart) {
      return moment(time[0]).format('YYYY-MM-01');
    }
    return moment(time[1]).add(1, 'months').format('YYYY-MM-01');
  } else if (periodType === 'CUSTOMIZE') {
    if (isStart) {
      return moment(time[0]).format('YYYY-MM-DD');
    }
    return moment(time[1]).format('YYYY-MM-DD');
  }
  return undefined;
};

/** 处理考核周期回显数据 */
export const formatPeriodDataToForm = (periodType: string, startTime, endTime) => {
  if (periodType === 'MONTH') {
    return moment(startTime);
  } else if (periodType === 'QUARTER') {
    return [moment(startTime), moment(endTime).subtract(1, 'month').startOf('month')];
  } else if (periodType === 'CUSTOMIZE') {
    return [moment(startTime), moment(endTime)];
  }
  return null;
};

export const transFormModuleToItem = (modules: any[]) =>
  modules?.map(
    (
      module, // 考核项
    ) => ({
      itemId: module.id,
      sortNum: module.sortNum,
      itemName: module.name,
      itemType: 'NORMAL',
      itemCode: module.itemCode,
      level: module.level,
      ruleGroupInfo: module.ruleGroup,
      resultFormat: module.resultFormat,
      calItems: transFormModuleToItem(module.modules),
      extraIndicators: module.extraIndicators,
      description: module.description,
      onlySelfIndicator: module.onlySelfIndicator,
    }),
  );

export const transFormItemToModule = (items: any[]) =>
  items?.map(
    (
      item, // 考核项
    ) => ({
      id: item.itemId,
      sortNum: item.sortNum,
      name: item.itemName,
      itemCode: item.itemCode,
      moduleType: 'NORMAL',
      level: item.level,
      ruleGroup: item.ruleGroupInfo,
      resultFormat: item.resultFormat,
      modules: transFormItemToModule(item.calItems),
      extraIndicators: item.extraIndicators,
      description: item.description,
      onlySelfIndicator: item.onlySelfIndicator,
    }),
  );

export const getDefaultSchema = () => {
  return "{\"version\":\"1.0.0\",\"componentsMap\":[{\"devMode\":\"lowCode\",\"componentName\":\"Page\"},{\"package\":\"@alife/lc-xy-dashboard-components\",\"version\":\"0.5.0-beta.1750252622271\",\"exportName\":\"Card\",\"main\":\"\",\"destructuring\":true,\"subName\":\"\",\"componentName\":\"Card\"},{\"package\":\"@alife/lc-xy-dashboard-components\",\"version\":\"0.5.0-beta.1750252622271\",\"exportName\":\"Column\",\"main\":\"src/index.tsx\",\"destructuring\":true,\"subName\":\"\",\"componentName\":\"Column\"},{\"package\":\"@alife/lc-xy-dashboard-components\",\"version\":\"0.5.0-beta.1750252622271\",\"exportName\":\"ColumnsLayout\",\"main\":\"src/index.tsx\",\"destructuring\":true,\"subName\":\"\",\"componentName\":\"ColumnsLayout\"},{\"package\":\"@alife/lc-xy-dashboard-components\",\"version\":\"0.5.0-beta.1750252622271\",\"exportName\":\"BonusPredict\",\"main\":\"\",\"destructuring\":true,\"subName\":\"\",\"componentName\":\"BonusPredict\"}],\"componentsTree\":[{\"componentName\":\"Page\",\"id\":\"node_ocmcbyguki1\",\"props\":{},\"fileName\":\"sample\",\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\",\"children\":[{\"componentName\":\"Card\",\"id\":\"node_ocmcbyguki2\",\"props\":{\"title\":\"核心业绩指标\"},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\",\"children\":[{\"componentName\":\"ColumnsLayout\",\"id\":\"node_ocmcbyguki4\",\"props\":{\"layout\":\"8:8:8\"},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\",\"children\":[{\"componentName\":\"Column\",\"id\":\"node_ocmcbyguki5\",\"props\":{},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\"},{\"componentName\":\"Column\",\"id\":\"node_ocmcbyguki6\",\"props\":{},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\"},{\"componentName\":\"Column\",\"id\":\"node_ocmcbyguki7\",\"props\":{},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\"}]},{\"componentName\":\"ColumnsLayout\",\"id\":\"node_ocmcbyguki8\",\"props\":{\"layout\":\"8:8:8\"},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\",\"children\":[{\"componentName\":\"Column\",\"id\":\"node_ocmcbyguki9\",\"props\":{},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\"},{\"componentName\":\"Column\",\"id\":\"node_ocmcbygukia\",\"props\":{},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\"},{\"componentName\":\"Column\",\"id\":\"node_ocmcbygukib\",\"props\":{},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\"}]}]},{\"componentName\":\"Card\",\"id\":\"node_ocmcbyguki3\",\"props\":{\"title\":\"奖金预测\"},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\",\"children\":[{\"componentName\":\"BonusPredict\",\"id\":\"node_ocmcbygukic\",\"props\":{\"moduleIds\":[]},\"hidden\":false,\"title\":\"\",\"isLocked\":false,\"condition\":true,\"conditionGroup\":\"\"}]}]}],\"i18n\":{},\"packages\":[{\"package\":\"moment\",\"version\":\"2.24.0\",\"urls\":[\"https://g.alicdn.com/mylib/moment/2.24.0/min/moment.min.js\"],\"library\":\"moment\"},{\"package\":\"lodash\",\"library\":\"_\",\"urls\":[\"https://g.alicdn.com/platform/c/lodash/4.6.1/lodash.min.js\"]},{\"package\":\"iconfont-icons\",\"urls\":\"//at.alicdn.com/t/font_2369445_ukrtsovd92r.js\"},{\"package\":\"@ant-design/icons\",\"version\":\"4.7.0\",\"urls\":[\"//g.alicdn.com/code/npm/@ali/ant-design-icons-cdn/4.5.0/index.umd.min.js\"],\"library\":\"icons\"},{\"package\":\"antd\",\"version\":\"4.19.5\",\"urls\":[\"//g.alicdn.com/code/lib/antd/4.23.0/antd.min.js\",\"//g.alicdn.com/code/lib/antd/4.23.0/antd.min.css\"],\"library\":\"antd\"},{\"title\":\"fusion组件库\",\"package\":\"@alifd/next\",\"version\":\"1.26.4\",\"urls\":[\"https://g.alicdn.com/code/lib/alifd__next/1.26.4/next.min.css\",\"https://g.alicdn.com/code/lib/alifd__next/1.26.4/next-with-locales.min.js\"],\"library\":\"Next\"},{\"package\":\"@alife/lc-xy-dashboard-components\",\"version\":\"0.5.0-beta.1\",\"library\":\"AlifeLcXyDashboardComponents\",\"urls\":[\"https://dev.g.alicdn.com/code/npm/@alife/lc-xy-dashboard-components/0.5.0/lowcode/render/default/view.js\",\"https://dev.g.alicdn.com/code/npm/@alife/lc-xy-dashboard-components/0.5.0/lowcode/render/default/view.css\"],\"editUrls\":[\"https://dev.g.alicdn.com/code/npm/@alife/lc-xy-dashboard-components/0.5.0/lowcode/view.js\",\"https://dev.g.alicdn.com/code/npm/@alife/lc-xy-dashboard-components/0.5.0/lowcode/view.css\"],\"advancedUrls\":{\"default\":[\"https://dev.g.alicdn.com/code/npm/@alife/lc-xy-dashboard-components/0.5.0/lowcode/render/default/view.js\",\"https://dev.g.alicdn.com/code/npm/@alife/lc-xy-dashboard-components/0.5.0/lowcode/render/default/view.css\"]},\"advancedEditUrls\":{}}]}"

};