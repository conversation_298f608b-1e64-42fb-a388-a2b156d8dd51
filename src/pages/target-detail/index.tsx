import React from 'react';
import { AMixPage, createAMix, AMixRenderer } from '@alife/amix-pc';
import Header from './components/Header';
import BaseInfo from './components/BaseInfo';
import TargetDetail from './components/TargetDetail';
import Footer from './components/Footer';
import TargetPlanQueryGatewayService from '@/_docplus/target/service/gateway/TargetPlanQueryGatewayService';
import './index.less';

const Page = () => {
  const config = {
    service: {
      getDetail: {
        url: '/getDetail', // 接口请求
        sign: ['channel'],
        transformRequest: (props, options) => {
          return {
            shopId: props.urlQuery.shopId,
          };
        },
      },
    },
    model: {
      state: {
        pageStatus: '',
        regions: {
          content: ['HeaderModule', 'BaseInfoModule', 'TargetDetailModule'],
          footer: ['FooterModule'],
        },
        modules: {
          HeaderModule: {
            card_id: 'Header',
            data: {},
          },
          BaseInfoModule: {
            card_id: 'BaseInfo',
            data: {},
          },
          TargetDetailModule: {
            card_id: 'TargetDetail',
            data: [],
          },
          FooterModule: {
            card_id: 'Footer',
            data: {},
          },
        },
      },
    },
  };
  const amix = createAMix(config);

  const onLoadData = async () => {
    try {
      const { urlQuery } = amix.state;
      amix.setPageStatus('loading');
      const { data } = await TargetPlanQueryGatewayService.queryTargetPlanModel({
        planId: urlQuery.planId,
        conditions: ['TARGET_PLAN_INDICCATOR'],
      });
      amix.setPageStatus('');

      amix.setModuleData({
        moduleId: 'BaseInfoModule',
        data: {
          targetPlanInfo: data?.targetPlanInfo || {},
        },
      });
      amix.setModuleData({
        moduleId: 'TargetDetailModule',
        data: {
          planId: urlQuery.planId,
          tabList: data?.indicatorInfos || [],
        },
      });
      amix.setModuleData({
        moduleId: 'FooterModule',
        data: {
          targetPlanInfo: data?.targetPlanInfo || {},
        },
      });
    } catch (error) {
      amix.setPageStatus('');
      console.log(error, '目标详情报错');
    }
  };

  const components = {
    Header: {
      fn: (props) => <Header />,
    },
    BaseInfo: {
      fn: (props) => <BaseInfo {...props.data} />,
    },
    TargetDetail: {
      fn: (props) => <TargetDetail {...props.data} />,
    },
    Footer: {
      fn: (props) => <Footer {...props.data} />,
    },
  };
  return (
    <AMixPage
      className="common-structure target-detail-page"
      amix={amix}
      onLoadData={onLoadData}
      onErrorRetry={onLoadData}
    >
      <AMixRenderer components={components} />
    </AMixPage>
  );
};

export default Page;
