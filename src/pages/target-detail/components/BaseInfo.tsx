import React from 'react';
import CommonStructure from '@/common/components/CommonStructure';
import { Form, Row, Col, Typography } from 'antd';
import { BusinessGroupEnum, BusinessLineEnum } from '@/pages/target-list/enum';

const { Text } = Typography;

interface IProps {
  targetPlanInfo?: any;
}
// 格式化日期
const formatDateString = (dateString) => {
  return dateString?.length === 6
    ? `${dateString?.slice(0, 4)}-${dateString?.slice(4)}`
    : dateString;
};

const BaseInfo = ({ targetPlanInfo }: IProps) => {
  const { planName, periodValue, businessGroup, businessLine } = targetPlanInfo || {};

  return (
    <CommonStructure.Section title="基础信息">
      <Form>
        <Row justify="space-between">
          <Col>
            <Form.Item label="目标名称" name="planName">
              <Text>{planName}</Text>
            </Form.Item>
          </Col>
          <Col>
            <Form.Item label="考核月份" name="endTime">
              <Text>{formatDateString(periodValue)}</Text>
            </Form.Item>
          </Col>
          <Col>
            <Form.Item label="业务域" name="businessGroup">
              <Text>{BusinessGroupEnum[businessGroup]}</Text>
            </Form.Item>
          </Col>
          <Col>
            <Form.Item label="业务线" name="businessLine">
              <Text>{BusinessLineEnum[businessLine]}</Text>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </CommonStructure.Section>
  );
};

export default BaseInfo;
