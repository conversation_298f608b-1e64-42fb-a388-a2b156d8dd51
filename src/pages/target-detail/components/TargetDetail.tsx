import React, { useState, useEffect } from 'react';
import CommonStructure from '@/common/components/CommonStructure';
import { Table, Tabs } from 'antd';
import { useAntdTable } from 'ahooks';
import TargetPlanQueryFacade from '@/_docplus/target/service/gateway/TargetPlanQueryGatewayService';

const columns = [
  {
    title: '主体Id',
    dataIndex: 'objectId',
    key: 'objectId',
  },
  {
    title: '主体名称',
    dataIndex: 'objectName',
    key: 'objectName',
  },
  {
    title: '目标',
    dataIndex: 'sourceValue',
    key: 'sourceValue',
  },
];

interface IProps {
  planId: string;
  tabList?: any[];
}

const TargetDetail = (props: IProps) => {
  const { tabList = [], planId } = props;
  const [currentTab, setCurrentTab] = useState<string | undefined>();

  const { tableProps, search } = useAntdTable(
    async ({ current, pageSize }) => {
      try {
        const { data } = await TargetPlanQueryFacade.queryTargetPlanDataInfos({
          planId,
          indicatorId: currentTab,
          pageSize: pageSize,
          pageNo: current,
        });
        const newData = data?.targetDataDetailModelInfoList?.map((item) => {
          return {
            objectId: item.targetDataInfo.objectId,
            objectName: item.targetDataInfo.objectName,
            sourceValue: item.targetDataDetailInfo.sourceValue,
          };
        });
        return {
          total: data?.pageInfoDTO?.totalCount,
          list: newData,
        };
      } catch (error) {
        return {
          total: 0,
          list: [],
        };
      }
    },
    {
      defaultPageSize: 10,
      manual: true,
    },
  );

  useEffect(() => {
    if (currentTab) {
      search.submit();
    }
  }, [currentTab]);

  useEffect(() => {
    if (tabList.length > 0 && currentTab === undefined) {
      setCurrentTab(tabList[0].indicatorId);
    }
  }, [tabList]);

  const handleTabChange = (key) => {
    setCurrentTab(key);
  };

  return (
    <CommonStructure.Section title="目标详情">
      <Tabs type="card" activeKey={currentTab} onChange={handleTabChange} destroyInactiveTabPane>
        {tabList?.map((item) => {
          return (
            <Tabs.TabPane key={item.indicatorId} tab={item.indicatorName}>
              <Table
                {...(tableProps as any)}
                columns={columns}
                rowKey={(record) => `${record.productId}`}
                pagination={{
                  ...(tableProps.pagination || {}),
                  showSizeChanger: true,
                  showTotal: (total) => `共 ${total} 条`,
                }}
              />
            </Tabs.TabPane>
          );
        })}
      </Tabs>
    </CommonStructure.Section>
  );
};

export default TargetDetail;
