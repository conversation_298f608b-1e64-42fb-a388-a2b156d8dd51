import React from 'react';
import { useHistory } from 'react-router-dom';
import { Button, Space } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { downloadFile } from '@alife/kb-biz-util';

interface IProps {
  targetPlanInfo?: any;
}

const Footer = (props: IProps) => {
  const { targetPlanInfo } = props;
  const history = useHistory();

  return (
    <div className="footer-btn">
      <Space>
        <Button
          type="primary"
          onClick={() => {
            if (targetPlanInfo?.ossUrl) {
              downloadFile('', targetPlanInfo?.ossUrl);
            }
          }}
        >
          下载
          <DownloadOutlined />
        </Button>
        <Button
          onClick={() => {
            history.push('/target-list');
          }}
        >
          返回列表
        </Button>
      </Space>
    </div>
  );
};

export default Footer;
