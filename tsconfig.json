{"compilerOptions": {"ignoreDeprecations": "5.0", "module": "esnext", "target": "esnext", "lib": ["es6", "dom"], "sourceMap": true, "allowJs": true, "jsx": "preserve", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": false, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "strictNullChecks": false, "useUnknownInCatchVariables": false, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "experimentalDecorators": true, "alwaysStrict": true, "skipLibCheck": true, "noEmit": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "exclude": ["node_modules", "build", "scripts"]}