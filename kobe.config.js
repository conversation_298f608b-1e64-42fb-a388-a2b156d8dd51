module.exports = {
  solution: '@ali/kobe-solution-eleme',
  /**
   * @doc 解决方案4.0文档 https://yuque.antfin.com/alsc-sale-fe/ss9ub3/qagttvop29ug3mou
   * @type {import('@ali/kobe-solution-eleme/src/type').SolutionConfig}
   */
  config: {
    'xy-page': {
      mode: 'spa',
      history: 'browser',
      basename: '/mp-kpi-assess-plan',
    },
    'micro-app': {
      enable: true,
      debug: true,
    },
    'xy-logger': false,
    'xy-tracker': false,
    'xy-setting': {
      env: 'ppe',
    },
    import: {
      libraryList: [
        {
          libraryName: 'antd',
          style: true,
        },
      ],
    },
    'test-coverage': false,
  },
  plugins: [
    require.resolve('@alipay/kobe-plugin-aems'),
    require.resolve('@ali/kobe-plugin-sass'),
  ],
  dev: {
    // app: 'edge',
    // hostname: 'agent.dev.elenet.me',
    hostname: 'pre-local-xy.amap.com',
    path: '/',
    browserHistory: true,
    https: true,
    overlay: false,
  },
  wconfig: {
    extend: {
      theme: {
        // 全局主色
        'primary-color': '#1a66ff',
      },
      output: {
        // 增加这三行配置，设置打包产物为umd
        library: '[name]',
        libraryTarget: 'umd',
      },
    },
  },
};
