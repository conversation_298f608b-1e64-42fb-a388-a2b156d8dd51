/**
 * Generated By @ali/alsc-docplus-cli (Ver.1.3.21-beta.1)
 * @see https://yuque.antfin.com/alsc-gateway/docplus/ouo0ls
 */
module.exports = {
  // 云鼎appId
  app: ['kbcommprod'],
  // 内置模板: 默认（云鼎网关）; 云鼎网关 - default; 销售网络标准接口 - sn;
  template: 'default',
  // 文档模式: 版本模式 - version; 分支模式 - repo;(具体每项配置参见站点帮助文档)
  mode: {
    kbcommprod: {
      gateway: {
        // 'branch': 'feature/20230901_17636380_target_performance_two_1',
        branch: 'releases/20240617141615973_r_release_123565_kbcommprod-code',
      },
      // 'commcenter-client': {
      //   branch: 'releases/20240617141615973_r_release_123565_kbcommprod-code',
      // },
    },
  },
  // 是否开启控制台挂件
  debug: false,
  // 生成代码目录配置，默认路径为
  output: {
    // API过滤配置(保留符合过滤条件的接口)。支持树形结构
    apiFilter: null,
    // 生成文件根目录。默认：工程目录/src
    rootDir: '_docplus/target',
    // Typescript类型声明文件位置。默认: 工程目录/src/types
    types: '',
    // API请求代码位置。默认: 工程目录/src/service
    service: '',
    // 网络请求库代码位置。默认：工程目录/src/request
    client: '',
  },
  custom: {
    client: '@/common/request',
  },
};
