{"name": "mp-kpi-assess-plan", "version": "1.1.0", "description": "新版考核方案", "author": "luozhou.csy", "repository": {"type": "git", "url": "**************************:alsc-merchants/mp-kpi-assess-plan.git"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "start": "tnpm run dev", "dev": "kobe dev --sourceMap", "build": "npm run clean && npm run prebuild && kobe build --publicPath __FAAS_CDN__/ --hash --sourceMap", "prebuild": "node ./scripts/pre-build.js", "defs": "dp update -a", "postinstall": "husky install", "prepare": "husky install", "alsclint-scan": "alsclint scan -i ./src", "alsclint-fix": "alsclint fix"}, "dependencies": {"@ali/alsc-gateway-web-client": "^1.1.8", "@ali/doraemon-pocket": "^1.1.4", "@ali/kb-fetch": "^2.2.1", "@ali/prometheus-fetch": "^2.3.7", "@ali/sn-docplus-request-client": "^2.0.2", "@ali/sn-strategy-tool": "1.0.32", "@alife/amap-fetch": "^3.3.2", "@alife/amix-pc": "^0.1.0-beta.42", "@alife/custom-react-route": "^1.0.0", "@alife/dic-formula-input": "^1.0.3", "@alife/kb-biz-util": "^1.4.4", "@alife/kobe-store-next": "^1.0.2", "@alife/mo-bd-select": "^2.4.3", "@alife/mp-oss-upload": "^2.5.4", "@alife/smart-data-client": "^0.0.6", "@alife/sn-element-selector": "^1.0.4-beta.1", "@alife/sn-permission": "^2", "@alipay/kobe-types": "^1.1.0", "@alipay/techui-rule-tree": "1.0.0-beta.13", "@ant-design/icons": "^4.8.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@emotion/css": "^11.10.6", "@types/array-move": "^2.0.0", "@types/classnames": "^2.3.1", "@types/query-string": "^6.3.0", "@types/react-sortable-hoc": "^0.7.1", "ahooks": "3", "antd": "^4.17.0", "array-move": "^4.0.0", "classnames": "^2.3.2", "core-js": "^3.2.1", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.groupby": "^4.6.0", "lodash.isempty": "^4.4.0", "lodash.set": "^4.3.2", "monaco-editor": "^0.44.0", "omit.js": "^2.0.2", "query-string": "^7.1.3", "react-monaco-editor": "^0.54.0", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-sortable-hoc": "^2.0.0", "ts-enum-object": "^1.3.2", "unstated-next": "^1.1.0"}, "devDependencies": {"@ali/alsc-docplus-cli": "^1.4.6", "@ali/alsc-docplus-console": "^2.0.4", "@ali/auto-push-window-polyfill": "^0.1.1", "@ali/feinsight": "1.0.5", "@ali/kobe-cli": "^3.0.30", "@ali/kobe-plugin-sass": "^3.0.0", "@ali/kobe-solution-eleme": "4.9.5-beta.2", "@ali/trace-plugin-perf": "^1.1.21", "@ali/trace-plugin-pv": "^1.1.21", "@ali/trace-plugin-resource-error": "^1.1.21", "@ali/trace-sdk": "^1.1.21", "@ali/tracker": "^4.3.3", "@alife/alsclint": "^1.1.1", "@alife/husky": "^4.3.8", "@alipay/kobe-plugin-aems": "^5.0.1", "@applint/spec": "^1.2.3", "@commitlint/cli": "16", "@types/lodash": "^4.14.191", "@types/lodash.clonedeep": "^4.5.7", "@types/lodash.get": "^4.4.7", "@types/lodash.groupby": "^4.6.7", "@types/lodash.isempty": "^4.4.7", "@types/lodash.set": "^4.3.7", "@types/react": "^16.14.46", "@types/react-dom": "^16.9.19", "@types/react-router": "^5.1.7", "@types/react-router-dom": "^5.1.5", "husky": "8", "lint-staged": "^15.2.2", "omit": "^1.0.1", "postcss-less": "^6.0.0", "resolve": "1.12.2", "rimraf": "2", "tsc-files": "^1.1.4", "typescript": "^5.4.2"}, "publishConfig": {"registry": "http://registry.anpm.alibaba-inc.com"}, "engines": {"install-node": "16"}, "faas": {"domain": "", "public": "dist", "description": "", "author_name": "", "author_email": "", "notice": [""], "build": ["tnpm install", "npm run build"]}, "tnpm": {"mode": "npm", "lockfile": "enable"}}