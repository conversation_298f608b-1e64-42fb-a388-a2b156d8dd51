const fs = require('fs');
const path = require('path');
const dayjs = require('dayjs');
const yargsParser = require('yargs-parser');

const tryGetFasterEnv = (fasterBuildContext) => {
  try {
    const { FAAS_ENV } = JSON.parse(fasterBuildContext);
    return FAAS_ENV;
  } catch (err) {
    console.error('尝试解析当前 FASTER 环境时发生错误 :-(', err);
  }
};

const tryGetVersion = () => {
  try {
    const { BUILD_ARGV_STR } = process.env;

    // 没有云构建环境变量，本地 build
    if (!BUILD_ARGV_STR) {
      return 'local';
    }

    const {
      def_publish_env: defEnv,
      def_publish_version: defVersion,
      FASTER_BUILD_CONTEXT,
    } = yargsParser(BUILD_ARGV_STR);

    const date = dayjs().format('YYYY.MM.DD.HH.mm.ss');

    if (FASTER_BUILD_CONTEXT) {
      // FAAS 不强制更新版本，用时间作为版本号
      const faasEnv = tryGetFasterEnv(FASTER_BUILD_CONTEXT);
      return [faasEnv, date].filter(Boolean).join('.');
    }

    if (defEnv || defVersion) {
      // DEF 一般来说强依赖版本号，直接使用
      return [
        defEnv,
        defVersion ?? date,
      ].filter(Boolean).join('.');
    }

    // 不认识的平台用构建时间兜底
    return date;
  } catch (err) {
    console.error('尝试解析当前版本号时发生错误 :-(', err);
  }
  return null;
};

const appVersionTsPath = path.resolve(__dirname, '../src/common/app-version.ts');

const template = fs.readFileSync(appVersionTsPath, 'utf-8');

const version = tryGetVersion();
const content = template.replace(
  /const\s+appVersion\s*=\s*['"`].*['"`];?/gmi,
  `const appVersion = '${version}';`,
);

fs.writeFileSync(appVersionTsPath, content);

console.log('file =   ', appVersionTsPath);
console.log('content =', content);
