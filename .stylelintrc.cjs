// .stylelintrc.js
const { getStylelintConfig } = require('@applint/spec');

// getStylelintConfig(rule: 'common'|'rax'|'react'|'vue', customConfig?);
module.exports = getStylelintConfig('react', {
  overrides: [
    {
      files: ['*.less', '**/*.less'],
      customSyntax: 'postcss-less',
    },
  ],
  rules: {
    'selector-max-id': 10000,
    'no-duplicate-selectors': [true, {
      severity: 'warning',
    }],
    'max-line-length': 150,
    'selector-max-compound-selectors': [5, {
      severity: 'warning',
    }],
    'color-no-invalid-hex': true,
    'color-named': 'never',
    'color-hex-case': 'lower',
    'color-hex-length': 'short',
    'selector-pseudo-class-no-unknown': [true, {
      ignorePseudoClasses: ['global'],
    }],
  }
});
