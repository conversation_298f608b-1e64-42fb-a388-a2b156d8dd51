// .eslintrc.js
const { getESLintConfig } = require('@applint/spec');

// getESLintConfig(rule: 'common'|'rax'|'react'|'vue' , customConfig?);
module.exports = getESLintConfig('react-ts', {
  // globals: {
  //   kBridge: false,
  //   AlipayJSBridge: false,
  // },
  rules: {
    '@typescript-eslint/indent': ['error', 2],
    'import/order': ['error', {
      groups: [
        'builtin',
        'external',
        'internal',
        'parent',
        'sibling',
        'index',
        'object',
        'type',
      ],
      pathGroups: [
        { pattern: '@/**', group: 'internal' },
        { pattern: '**/*.less', group: 'type' },
        { pattern: '**/*.scss', group: 'type' },
        { pattern: '**/*.css', group: 'type' },
      ],
    }],
    'no-script-url': 'warn',
    'react/jsx-wrap-multilines': ['error', {
      declaration: 'parens-new-line',
      assignment: 'parens-new-line',
      return: 'parens-new-line',
      arrow: 'parens-new-line',
      condition: 'parens-new-line',
      logical: 'parens-new-line',
      prop: 'parens-new-line',
    }],
    'react/jsx-curly-newline': ['error', {
      multiline: 'forbid',
      singleline: 'forbid',
    }],
    'react/jsx-indent': ['error', 2, {
      checkAttributes: true,
      indentLogicalExpressions: true,
    }],
    'no-restricted-syntax': [
      'error',
      {
        selector: 'ExportDefaultDeclaration',
        message: 'export default 为不具名导出，可能导致代码无法追溯，请使用 export',
      },
    ],
  },
  overrides: [
    {
      files: [
        './src/pages/*/index.tsx',
        './src/pages/*/index.jsx',
      ],
      rules: {
        'no-restricted-syntax': 0,
      },
    },
    {
      files: [
        './src/_docplus/**/*',
      ],
      rules: {
        'no-restricted-syntax': 0,
        '@typescript-eslint/ban-tslint-comment': 0,
      },
    },
  ],
});
